"""
Jira Webhook Service for handling webhook events and managing webhooks
"""

import requests
from datetime import datetime
from flask import current_app
from app.helpers.extensions import db
# Fix import - use the correct class name from webhook.py
from app.models.webhook import WebhookEvent
from app.models.project import Project
from app.models.task import Task
from app.models.integration import Integration
from app.integrations.jira.api_client import JiraAPIClient
from app.integrations.jira.sync_service import JiraSyncService
from app.integrations.jira.webhook_handler import JiraWebhookHandler

class JiraWebhookService:
    """Service for managing Jira webhooks"""
    
    def __init__(self, user_id: int):
        """
        Initialize Jira webhook service for a specific user
        
        Args:
            user_id (int): User ID
        """
        self.user_id = user_id
        
        # Get Jira integration
        self.jira_integration = Integration.query.filter_by(
            user_id=user_id, platform="jira", is_active=True
        ).first()
        
        if not self.jira_integration:
            raise ValueError("No active Jira integration found for user")
        
        self.jira_client = JiraAPIClient(
            self.jira_integration.access_token,
            self.jira_integration.settings.get("cloud_id")
        )
        
        self.sync_service = JiraSyncService(user_id)
        self.webhook_handler = JiraWebhookHandler(user_id)
    
    def create_webhook_for_project(self, project_key: str, webhook_url: str) -> dict:
        """
        Create a webhook for a Jira project
        
        Args:
            project_key (str): Jira project key
            webhook_url (str): Webhook URL
            
        Returns:
            dict: Webhook creation response
        """
        try:
            webhook_data = {
                "name": f"TMS Webhook - {project_key}",
                "url": webhook_url,
                "events": [
                    "jira:issue_created",
                    "jira:issue_updated",
                    "jira:issue_deleted"
                ],
                "filters": {
                    "project": [project_key]
                }
            }
            
            response = self.jira_client.create_webhook(webhook_data)
            return response
            
        except Exception as e:
            current_app.logger.error(f"Failed to create Jira webhook: {str(e)}")
            raise
    
    def delete_webhook(self, webhook_id: str) -> bool:
        """
        Delete a Jira webhook
        
        Args:
            webhook_id (str): Jira webhook ID
            
        Returns:
            bool: True if webhook was deleted successfully
        """
        try:
            self.jira_client.delete_webhook(webhook_id)
            return True
            
        except Exception as e:
            current_app.logger.error(f"Failed to delete Jira webhook: {str(e)}")
            return False

class JiraWebhookEventProcessor:
    """Service for processing Jira webhook events"""
    
    def __init__(self, user_id: int = None):
        """Initialize with optional user_id"""
        self.user_id = user_id
        if user_id:
            self.webhook_handler = JiraWebhookHandler(user_id)
        else:
            self.webhook_handler = None
    
    def process_webhook_event(self, webhook_data: dict, user_id: int = None) -> dict:
        """
        Process a Jira webhook event
        
        Args:
            webhook_data (dict): Webhook event data
            user_id (int, optional): User ID if not provided at initialization
            
        Returns:
            dict: Processing result
        """
        try:
            # Use provided user_id or fall back to initialized one
            if user_id and not self.webhook_handler:
                self.webhook_handler = JiraWebhookHandler(user_id)
            elif not self.webhook_handler and not user_id:
                raise ValueError("User ID is required to process webhook")
                
            event_type = webhook_data.get('webhookEvent')
            issue = webhook_data.get('issue')
            
            if not event_type or not issue:
                raise ValueError("Missing required webhook data")
                
            # Process event based on type
            if event_type == 'jira:issue_created':
                self.webhook_handler.handle_issue_event(webhook_data)
                return {"success": True, "message": "Issue created event processed"}
            elif event_type == 'jira:issue_updated':
                self.webhook_handler.handle_issue_event(webhook_data)
                return {"success": True, "message": "Issue updated event processed"}
            elif event_type == 'jira:issue_deleted':
                self.webhook_handler.handle_issue_event(webhook_data)
                return {"success": True, "message": "Issue deleted event processed"}
            
            return {"success": False, "message": "Unsupported event type"}
            
        except Exception as e:
            current_app.logger.error(f"Error processing Jira webhook: {str(e)}")
            return {"success": False, "message": str(e)}
