"""
Webhook validation utilities for GitHub webhooks
"""

import hmac
import hashlib
from flask import request


class WebhookValidator:
    """Utility class for validating GitHub webhook signatures"""
    
    @staticmethod
    def verify_github_signature(payload_body: bytes, secret: str, signature_header: str) -> bool:
        """
        Verify GitHub webhook signature
        
        Args:
            payload_body (bytes): Raw request body
            secret (str): Webhook secret
            signature_header (str): X-Hub-Signature-256 header value
            
        Returns:
            bool: True if signature is valid, False otherwise
        """
        if not signature_header:
            return False
            
        # GitHub sends signature as "sha256=<hash>"
        if not signature_header.startswith('sha256='):
            return False
            
        # Extract the hash part
        github_signature = signature_header[7:]
        
        # Calculate expected signature
        expected_signature = hmac.new(
            secret.encode('utf-8'),
            payload_body,
            hashlib.sha256
        ).hexdigest()
        
        # Compare signatures using secure comparison
        return hmac.compare_digest(github_signature, expected_signature)
    
    @staticmethod
    def validate_github_webhook_request() -> tuple[bool, str, dict]:
        """
        Validate incoming GitHub webhook request
        
        Returns:
            tuple: (is_valid, error_message, headers_info)
        """
        # Check required headers
        github_event = request.headers.get('X-GitHub-Event')
        github_delivery = request.headers.get('X-GitHub-Delivery')
        github_signature = request.headers.get('X-Hub-Signature-256')
        
        headers_info = {
            'event': github_event,
            'delivery_id': github_delivery,
            'signature': github_signature
        }
        
        if not github_event:
            return False, "Missing X-GitHub-Event header", headers_info
            
        if not github_delivery:
            return False, "Missing X-GitHub-Delivery header", headers_info
            
        if not github_signature:
            return False, "Missing X-Hub-Signature-256 header", headers_info
            
        return True, "", headers_info
