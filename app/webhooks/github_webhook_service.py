"""
GitHub Webhook Service for handling webhook events and managing webhooks
"""

import requests
import secrets
from datetime import datetime
from flask import current_app
from app.helpers.extensions import db
from app.models.webhook import GitHubWebhook, WebhookEvent
from app.models.project import Project
from app.models.task import Task
from app.models.integration import Integration
from app.models.api_response import ApiResponse
from app.projects.github_service import GitHubRepositoryService
from app.tasks.github_service import GitHubIssuesService


class GitHubWebhookService:
    """Service for managing GitHub webhooks"""
    
    def __init__(self, user_id: int):
        """
        Initialize GitHub webhook service for a specific user
        
        Args:
            user_id (int): User ID
        """
        self.user_id = user_id
        
        # Get GitHub integration
        self.github_integration = Integration.query.filter_by(
            user_id=user_id, platform="github", is_active=True
        ).first()
        
        if not self.github_integration:
            raise ValueError("No active GitHub integration found for user")
        
        self.headers = {
            "Authorization": f"Bearer {self.github_integration.access_token}",
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "TMS-Python-Backend",
        }
    
    def create_webhook_for_repository(self, repo_full_name: str, webhook_url: str) -> ApiResponse:
        """
        Create a webhook for a GitHub repository
        
        Args:
            repo_full_name (str): Repository full name (owner/repo)
            webhook_url (str): Webhook endpoint URL
            
        Returns:
            ApiResponse: Response containing webhook creation result
        """
        try:
            # Generate webhook secret
            webhook_secret = secrets.token_urlsafe(32)
            
            # Webhook configuration
            webhook_config = {
                "name": "web",
                "active": True,
                "events": [
                    "push",
                    "issues",
                    "issue_comment", 
                    "pull_request",
                    "pull_request_review",
                    "repository"
                ],
                "config": {
                    "url": webhook_url,
                    "content_type": "json",
                    "secret": webhook_secret,
                    "insecure_ssl": "0"
                }
            }
            
            # Create webhook on GitHub
            url = f"https://api.github.com/repos/{repo_full_name}/hooks"
            response = requests.post(url, json=webhook_config, headers=self.headers)
            
            if response.status_code == 201:
                webhook_data = response.json()
                
                # Find associated project
                project = Project.query.filter_by(
                    github_repo_full_name=repo_full_name,
                    user_id=self.user_id,
                    deleted_at=None
                ).first()
                
                # Save webhook to database
                github_webhook = GitHubWebhook(
                    user_id=self.user_id,
                    project_id=project.id if project else None,
                    github_webhook_id=str(webhook_data["id"]),
                    github_repo_full_name=repo_full_name,
                    webhook_url=webhook_url,
                    secret=webhook_secret,
                    events=webhook_config["events"],
                    is_active=True
                )
                
                db.session.add(github_webhook)
                db.session.commit()
                
                return ApiResponse.success(
                    f"Webhook created successfully for {repo_full_name}",
                    data={
                        "webhook_id": github_webhook.id,
                        "github_webhook_id": webhook_data["id"],
                        "repository": repo_full_name,
                        "events": webhook_config["events"]
                    }
                )
            else:
                error_msg = response.json().get("message", "Unknown error")
                return ApiResponse.failure(
                    f"Failed to create webhook for {repo_full_name}: {error_msg}",
                    code=response.status_code
                )
                
        except Exception as e:
            return ApiResponse.failure(
                f"Error creating webhook for {repo_full_name}: {str(e)}",
                code=500
            )
    
    def create_webhooks_for_repositories(self, repository_urls: list, base_webhook_url: str) -> ApiResponse:
        """
        Create webhooks for multiple repositories
        
        Args:
            repository_urls (list): List of repository URLs
            base_webhook_url (str): Base webhook URL
            
        Returns:
            ApiResponse: Response containing creation results
        """
        try:
            created_webhooks = []
            errors = []
            
            for repo_url in repository_urls:
                try:
                    # Extract repo full name from URL
                    if not repo_url.startswith("https://github.com/"):
                        errors.append(f"Invalid GitHub URL format: {repo_url}")
                        continue
                    
                    repo_full_name = repo_url.replace("https://github.com/", "").rstrip("/")
                    if "/" not in repo_full_name:
                        errors.append(f"Invalid GitHub URL format: {repo_url}")
                        continue
                    
                    # Check if webhook already exists
                    existing_webhook = GitHubWebhook.query.filter_by(
                        user_id=self.user_id,
                        github_repo_full_name=repo_full_name,
                        is_active=True
                    ).first()
                    
                    if existing_webhook:
                        errors.append(f"Webhook already exists for {repo_full_name}")
                        continue
                    
                    # Create webhook
                    webhook_response = self.create_webhook_for_repository(
                        repo_full_name, 
                        base_webhook_url
                    )
                    
                    if webhook_response.success:
                        created_webhooks.append(webhook_response.data)
                    else:
                        errors.append(f"{repo_full_name}: {webhook_response.message}")
                        
                except Exception as e:
                    errors.append(f"Error processing {repo_url}: {str(e)}")
            
            if created_webhooks:
                message = f"Successfully created {len(created_webhooks)} webhook(s)"
                if errors:
                    message += f" with {len(errors)} error(s)"
                
                return ApiResponse.success(
                    message,
                    data={
                        "created_webhooks": created_webhooks,
                        "errors": errors
                    }
                )
            else:
                return ApiResponse.failure(
                    "No webhooks were created",
                    data={"errors": errors},
                    code=400
                )
                
        except Exception as e:
            return ApiResponse.failure(
                f"Error creating webhooks: {str(e)}",
                code=500
            )
    
    def delete_webhook(self, webhook_id: int) -> ApiResponse:
        """
        Delete a webhook
        
        Args:
            webhook_id (int): Local webhook ID
            
        Returns:
            ApiResponse: Response containing deletion result
        """
        try:
            # Get webhook from database
            webhook = GitHubWebhook.query.filter_by(
                id=webhook_id,
                user_id=self.user_id
            ).first()
            
            if not webhook:
                return ApiResponse.failure("Webhook not found", code=404)
            
            # Delete webhook from GitHub
            url = f"https://api.github.com/repos/{webhook.github_repo_full_name}/hooks/{webhook.github_webhook_id}"
            response = requests.delete(url, headers=self.headers)
            
            # Delete from database regardless of GitHub response
            # (webhook might already be deleted on GitHub)
            webhook.is_active = False
            db.session.commit()
            
            if response.status_code in [204, 404]:
                return ApiResponse.success(
                    f"Webhook deleted successfully for {webhook.github_repo_full_name}"
                )
            else:
                return ApiResponse.success(
                    f"Webhook deactivated locally for {webhook.github_repo_full_name} "
                    f"(GitHub deletion failed: {response.status_code})"
                )
                
        except Exception as e:
            return ApiResponse.failure(
                f"Error deleting webhook: {str(e)}",
                code=500
            )
    
    def get_user_webhooks(self) -> ApiResponse:
        """
        Get all webhooks for the user
        
        Returns:
            ApiResponse: Response containing user's webhooks
        """
        try:
            webhooks = GitHubWebhook.query.filter_by(
                user_id=self.user_id,
                is_active=True
            ).all()
            
            webhook_list = [webhook.to_dict() for webhook in webhooks]
            
            return ApiResponse.success(
                f"Found {len(webhook_list)} webhook(s)",
                data={"webhooks": webhook_list}
            )
            
        except Exception as e:
            return ApiResponse.failure(
                f"Error fetching webhooks: {str(e)}",
                code=500
            )


class GitHubWebhookEventProcessor:
    """Service for processing GitHub webhook events"""

    @staticmethod
    def process_webhook_event(webhook: GitHubWebhook, event_type: str, payload: dict, delivery_id: str) -> ApiResponse:
        """
        Process a GitHub webhook event

        Args:
            webhook (GitHubWebhook): Webhook configuration
            event_type (str): Type of GitHub event
            payload (dict): Event payload
            delivery_id (str): GitHub delivery ID

        Returns:
            ApiResponse: Processing result
        """
        try:
            # Log the event
            webhook_event = WebhookEvent(
                webhook_id=webhook.id,
                github_delivery_id=delivery_id,
                event_type=event_type,
                action=payload.get('action'),
                payload=payload,
                processed=False
            )
            db.session.add(webhook_event)
            db.session.flush()

            # Process based on event type
            if event_type == 'push':
                result = GitHubWebhookEventProcessor._process_push_event(webhook, payload)
            elif event_type == 'issues':
                result = GitHubWebhookEventProcessor._process_issues_event(webhook, payload)
            elif event_type == 'issue_comment':
                result = GitHubWebhookEventProcessor._process_issue_comment_event(webhook, payload)
            elif event_type == 'pull_request':
                result = GitHubWebhookEventProcessor._process_pull_request_event(webhook, payload)
            elif event_type == 'repository':
                result = GitHubWebhookEventProcessor._process_repository_event(webhook, payload)
            else:
                result = ApiResponse.success(f"Event type '{event_type}' not processed")

            # Update event processing status
            webhook_event.processed = True
            webhook_event.processed_at = datetime.utcnow()
            if not result.success:
                webhook_event.error_message = result.message

            # Update webhook last ping
            webhook.last_ping = datetime.utcnow()

            db.session.commit()

            # Send Telegram notifications if processing was successful
            if result.success:
                try:
                    from app.telegram.notification_service import TelegramNotificationService
                    notification_service = TelegramNotificationService()
                    notification_service.send_github_webhook_notification(webhook_event)
                except Exception as e:
                    current_app.logger.error(f"Error sending Telegram notification: {str(e)}")
                    # Don't fail the webhook processing if notification fails

            return result

        except Exception as e:
            db.session.rollback()
            # Update event with error
            if 'webhook_event' in locals():
                webhook_event.error_message = str(e)
                db.session.commit()

            return ApiResponse.failure(f"Error processing webhook event: {str(e)}", code=500)

    @staticmethod
    def _process_push_event(webhook: GitHubWebhook, payload: dict) -> ApiResponse:
        """Process push event - sync repository info"""
        try:
            if webhook.project_id:
                # Update project information
                github_service = GitHubRepositoryService(webhook.user_id)
                repo_url = f"https://github.com/{webhook.github_repo_full_name}"
                sync_result = github_service.sync_repositories_to_projects([repo_url])

                return ApiResponse.success(
                    f"Repository sync triggered for push to {webhook.github_repo_full_name}",
                    data={"sync_result": sync_result.data if sync_result.success else None}
                )
            else:
                return ApiResponse.success("Push event received but no associated project")

        except Exception as e:
            return ApiResponse.failure(f"Error processing push event: {str(e)}")

    @staticmethod
    def _process_issues_event(webhook: GitHubWebhook, payload: dict) -> ApiResponse:
        """Process issues event - sync issues to tasks"""
        try:
            if webhook.project_id:
                # Sync issues for the project
                github_service = GitHubIssuesService(webhook.user_id)
                sync_result = github_service.sync_repository_issues_to_tasks(webhook.project_id)

                action = payload.get('action', 'unknown')
                issue_number = payload.get('issue', {}).get('number', 'unknown')

                return ApiResponse.success(
                    f"Issues sync triggered for {action} action on issue #{issue_number}",
                    data={"sync_result": sync_result.data if sync_result.success else None}
                )
            else:
                return ApiResponse.success("Issues event received but no associated project")

        except Exception as e:
            return ApiResponse.failure(f"Error processing issues event: {str(e)}")

    @staticmethod
    def _process_issue_comment_event(webhook: GitHubWebhook, payload: dict) -> ApiResponse:
        """Process issue comment event"""
        try:
            # For now, just acknowledge the event
            # Could be extended to sync comments in the future
            action = payload.get('action', 'unknown')
            issue_number = payload.get('issue', {}).get('number', 'unknown')

            return ApiResponse.success(
                f"Issue comment {action} on issue #{issue_number} - acknowledged"
            )

        except Exception as e:
            return ApiResponse.failure(f"Error processing issue comment event: {str(e)}")

    @staticmethod
    def _process_pull_request_event(webhook: GitHubWebhook, payload: dict) -> ApiResponse:
        """Process pull request event"""
        try:
            # For now, just acknowledge the event
            # Could be extended to handle PR-related tasks in the future
            action = payload.get('action', 'unknown')
            pr_number = payload.get('pull_request', {}).get('number', 'unknown')

            return ApiResponse.success(
                f"Pull request {action} on PR #{pr_number} - acknowledged"
            )

        except Exception as e:
            return ApiResponse.failure(f"Error processing pull request event: {str(e)}")

    @staticmethod
    def _process_repository_event(webhook: GitHubWebhook, payload: dict) -> ApiResponse:
        """Process repository event - handle repo changes"""
        try:
            action = payload.get('action', 'unknown')

            if action in ['deleted', 'archived']:
                # Deactivate webhook if repository is deleted or archived
                webhook.is_active = False
                db.session.commit()

                return ApiResponse.success(
                    f"Repository {action} - webhook deactivated"
                )
            elif action in ['renamed', 'transferred']:
                # Update repository information
                if webhook.project_id:
                    github_service = GitHubRepositoryService(webhook.user_id)
                    repo_url = f"https://github.com/{webhook.github_repo_full_name}"
                    sync_result = github_service.sync_repositories_to_projects([repo_url])

                    return ApiResponse.success(
                        f"Repository {action} - project info updated",
                        data={"sync_result": sync_result.data if sync_result.success else None}
                    )

            return ApiResponse.success(f"Repository {action} - acknowledged")

        except Exception as e:
            return ApiResponse.failure(f"Error processing repository event: {str(e)}")
