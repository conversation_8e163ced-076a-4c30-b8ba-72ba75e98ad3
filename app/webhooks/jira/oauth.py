"""
Jira OAuth integration for TMS Backend
Handles Jira OAuth 2.0 flow for authentication and authorization
"""

import os
import requests
from typing import Dict, Any, Optional
from urllib.parse import urlencode

from app.webhooks.jira.exceptions import JiraIntegrationError, ConfigurationError


class JiraOAuthService:
    """Handle Jira OAuth 2.0 authentication flow"""
    
    def __init__(self):
        self.client_id = os.getenv('JIRA_CLIENT_ID')
        self.client_secret = os.getenv('JIRA_CLIENT_SECRET')
        self.redirect_uri = os.getenv('JIRA_REDIRECT_URI')
        
        if not all([self.client_id, self.client_secret, self.redirect_uri]):
            raise ConfigurationError(
                "Jira OAuth configuration incomplete. Please set JIRA_CLIENT_ID, "
                "JIRA_CLIENT_SECRET, and JIRA_REDIRECT_URI environment variables."
            )
        
        # Jira OAuth endpoints
        self.auth_url = "https://auth.atlassian.com/authorize"
        self.token_url = "https://auth.atlassian.com/oauth/token"
        self.accessible_resources_url = "https://api.atlassian.com/oauth/token/accessible-resources"
        
        # OAuth scopes for Jira
        self.scopes = [
            "read:jira-work",
            "write:jira-work", 
            "read:jira-user",
            "offline_access"  # For refresh tokens
        ]
    
    def get_authorization_url(self, state: str) -> str:
        """
        Generate Jira OAuth authorization URL
        
        Args:
            state (str): CSRF protection state parameter
            
        Returns:
            str: Authorization URL
        """
        params = {
            'audience': 'api.atlassian.com',
            'client_id': self.client_id,
            'scope': ' '.join(self.scopes),
            'redirect_uri': self.redirect_uri,
            'state': state,
            'response_type': 'code',
            'prompt': 'consent'
        }
        
        return f"{self.auth_url}?{urlencode(params)}"
    
    def exchange_code_for_token(self, code: str) -> Dict[str, Any]:
        """
        Exchange authorization code for access token
        
        Args:
            code (str): Authorization code from Jira
            
        Returns:
            Dict[str, Any]: Token response containing access_token, refresh_token, etc.
            
        Raises:
            JiraIntegrationError: If token exchange fails
        """
        try:
            data = {
                'grant_type': 'authorization_code',
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'code': code,
                'redirect_uri': self.redirect_uri
            }
            
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            response = requests.post(
                self.token_url,
                json=data,
                headers=headers,
                timeout=30
            )
            
            if response.status_code != 200:
                error_data = response.json() if response.content else {}
                raise JiraIntegrationError(
                    f"Failed to exchange code for token: {response.status_code}",
                    operation="token_exchange",
                    jira_error=error_data.get('error_description', str(error_data))
                )
            
            token_data = response.json()
            
            # Validate required fields
            required_fields = ['access_token', 'token_type']
            for field in required_fields:
                if field not in token_data:
                    raise JiraIntegrationError(
                        f"Missing required field in token response: {field}",
                        operation="token_exchange"
                    )
            
            return token_data
            
        except requests.RequestException as e:
            raise JiraIntegrationError(
                f"Network error during token exchange: {str(e)}",
                operation="token_exchange"
            )
        except Exception as e:
            raise JiraIntegrationError(
                f"Unexpected error during token exchange: {str(e)}",
                operation="token_exchange"
            )
    
    def refresh_access_token(self, refresh_token: str) -> Dict[str, Any]:
        """
        Refresh access token using refresh token
        
        Args:
            refresh_token (str): Refresh token
            
        Returns:
            Dict[str, Any]: New token response
            
        Raises:
            JiraIntegrationError: If token refresh fails
        """
        try:
            data = {
                'grant_type': 'refresh_token',
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'refresh_token': refresh_token
            }
            
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            response = requests.post(
                self.token_url,
                json=data,
                headers=headers,
                timeout=30
            )
            
            if response.status_code != 200:
                error_data = response.json() if response.content else {}
                raise JiraIntegrationError(
                    f"Failed to refresh token: {response.status_code}",
                    operation="token_refresh",
                    jira_error=error_data.get('error_description', str(error_data))
                )
            
            return response.json()
            
        except requests.RequestException as e:
            raise JiraIntegrationError(
                f"Network error during token refresh: {str(e)}",
                operation="token_refresh"
            )
        except Exception as e:
            raise JiraIntegrationError(
                f"Unexpected error during token refresh: {str(e)}",
                operation="token_refresh"
            )
    
    def get_accessible_resources(self, access_token: str) -> list:
        """
        Get list of Jira sites accessible to the user
        
        Args:
            access_token (str): Access token
            
        Returns:
            list: List of accessible Jira sites
            
        Raises:
            JiraIntegrationError: If request fails
        """
        try:
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Accept': 'application/json'
            }
            
            response = requests.get(
                self.accessible_resources_url,
                headers=headers,
                timeout=30
            )
            
            if response.status_code != 200:
                error_data = response.json() if response.content else {}
                raise JiraIntegrationError(
                    f"Failed to get accessible resources: {response.status_code}",
                    operation="get_resources",
                    jira_error=str(error_data)
                )
            
            resources = response.json()
            
            # Filter for Jira sites only
            jira_sites = [
                resource for resource in resources
                if resource.get('scopes', {}).get('jira')
            ]
            
            return jira_sites
            
        except requests.RequestException as e:
            raise JiraIntegrationError(
                f"Network error getting accessible resources: {str(e)}",
                operation="get_resources"
            )
        except Exception as e:
            raise JiraIntegrationError(
                f"Unexpected error getting accessible resources: {str(e)}",
                operation="get_resources"
            )
    
    def revoke_token(self, token: str) -> bool:
        """
        Revoke access token
        
        Args:
            token (str): Token to revoke
            
        Returns:
            bool: True if successful
        """
        try:
            # Atlassian doesn't have a standard revoke endpoint
            # Token will expire naturally or can be revoked from Atlassian admin
            return True
            
        except Exception as e:
            raise JiraIntegrationError(
                f"Error revoking token: {str(e)}",
                operation="revoke_token"
            )
    
    @staticmethod
    def validate_token_response(token_data: Dict[str, Any]) -> bool:
        """
        Validate token response structure
        
        Args:
            token_data (Dict[str, Any]): Token response data
            
        Returns:
            bool: True if valid
        """
        required_fields = ['access_token', 'token_type']
        return all(field in token_data for field in required_fields)
    
    @staticmethod
    def is_token_expired(token_data: Dict[str, Any]) -> bool:
        """
        Check if token is expired
        
        Args:
            token_data (Dict[str, Any]): Token data with expires_in field
            
        Returns:
            bool: True if expired
        """
        import time
        
        if 'expires_in' not in token_data or 'created_at' not in token_data:
            return False
        
        expires_at = token_data['created_at'] + token_data['expires_in']
        return time.time() >= expires_at
