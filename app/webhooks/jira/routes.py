"""
Jira integration routes for TMS Backend
Handles Jira projects and issues synchronization
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity

from app.webhooks.jira.sync_service import JiraSyncService
from app.webhooks.jira_webhook_service import JiraWebhookService, JiraWebhookEventProcessor
from app.webhooks.jira.utils import ApiResponse
from app.models.integration import Integration
from app.exceptions import ValidationError

jira_bp = Blueprint("jira", __name__, url_prefix="/webhooks/jira")

from app.webhooks.jira.webhook_handler import jira_webhook as webhook_handler


@jira_bp.route("/status", methods=["GET"])
@jwt_required()
def get_jira_status():
    """Get Jira integration status for current user"""
    try:
        user_id = int(get_jwt_identity())
        sync_service = JiraSyncService(user_id)
        
        is_connected = sync_service.is_connected()
        
        data = {
            "connected": is_connected,
            "platform": "jira"
        }
        
        if is_connected:
            data["cloud_id"] = sync_service.integration.settings.get("cloud_id")
            data["platform_user_id"] = sync_service.integration.platform_user_id
        
        response = ApiResponse.success(
            "Jira integration status retrieved",
            data=data
        )
        return jsonify(response.to_dict()), response.code
        
    except Exception as e:
        response = ApiResponse.failure(f"Error getting Jira status: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@jira_bp.route("/webhook", methods=["POST"])
def handle_jira_webhook():
    """Handle Jira webhook events"""
    try:
        webhook_data = request.get_json()
        if not webhook_data:
            return jsonify({"success": False, "message": "No webhook data received"}), 400
        
        # Get user_id from query parameters
        user_id = request.args.get('user_id')
        if not user_id:
            return jsonify({"success": False, "message": "Missing user_id parameter"}), 400
            
        user_id = int(user_id)
        
        # Process webhook using the processor
        webhook_processor = JiraWebhookEventProcessor()
        result = webhook_processor.process_webhook_event(webhook_data, user_id)
        
        return jsonify(result), 200 if result.get("success", False) else 400
    except Exception as e:
        current_app.logger.error(f"Error processing Jira webhook: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@jira_bp.route("/projects", methods=["GET"])
@jwt_required()
def get_jira_projects():
    """Get all Jira projects accessible to the user"""
    try:
        user_id = int(get_jwt_identity())
        sync_service = JiraSyncService(user_id)
        
        response = sync_service.get_jira_projects()
        return jsonify(response.to_dict()), response.code
        
    except Exception as e:
        response = ApiResponse.failure(f"Error retrieving Jira projects: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@jira_bp.route("/projects/<project_key>/sync", methods=["POST"])
@jwt_required()
def sync_jira_project(project_key):
    """Sync a Jira project to TMS"""
    try:
        user_id = int(get_jwt_identity())
        sync_service = JiraSyncService(user_id)
        
        response = sync_service.sync_jira_project_to_tms(project_key)
        return jsonify(response.to_dict()), response.code
        
    except Exception as e:
        response = ApiResponse.failure(f"Error syncing Jira project: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@jira_bp.route("/projects/<project_key>/issues", methods=["GET"])
@jwt_required()
def get_jira_project_issues(project_key):
    """Get issues from a Jira project"""
    try:
        user_id = int(get_jwt_identity())
        sync_service = JiraSyncService(user_id)
        
        # Get optional JQL parameter
        jql = request.args.get("jql")
        if jql:
            # Use custom JQL
            issues_response = sync_service.jira_client.get_issues(
                project_key,
                jql=jql,
                fields=["summary", "description", "status", "priority", "assignee", "created", "updated"]
            )
        else:
            # Use default project query
            issues_response = sync_service.jira_client.get_issues(
                project_key,
                fields=["summary", "description", "status", "priority", "assignee", "created", "updated"]
            )
        
        # Format issues for frontend
        formatted_issues = []
        for issue in issues_response.get("issues", []):
            fields = issue.get("fields", {})
            formatted_issues.append({
                "id": issue.get("id"),
                "key": issue.get("key"),
                "summary": fields.get("summary", ""),
                "description": fields.get("description", ""),
                "status": fields.get("status", {}).get("name", ""),
                "priority": fields.get("priority", {}).get("name", ""),
                "assignee": fields.get("assignee", {}).get("displayName", "") if fields.get("assignee") else "",
                "created": fields.get("created"),
                "updated": fields.get("updated")
            })
        
        response = ApiResponse.success(
            "Jira issues retrieved successfully",
            data={
                "issues": formatted_issues,
                "total": issues_response.get("total", 0)
            }
        )
        return jsonify(response.to_dict()), response.code
        
    except Exception as e:
        response = ApiResponse.failure(f"Error retrieving Jira issues: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@jira_bp.route("/issues/<issue_key>/sync", methods=["POST"])
@jwt_required()
def sync_jira_issue(issue_key):
    """Sync a Jira issue to TMS task"""
    try:
        user_id = int(get_jwt_identity())
        data = request.get_json()
        
        if not data or "project_id" not in data:
            response = ApiResponse.failure("project_id is required", code=400)
            return jsonify(response.to_dict()), response.code
        
        project_id = data["project_id"]
        sync_service = JiraSyncService(user_id)
        
        response = sync_service.sync_jira_issue_to_task(project_id, issue_key)
        return jsonify(response.to_dict()), response.code
        
    except Exception as e:
        response = ApiResponse.failure(f"Error syncing Jira issue: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@jira_bp.route("/tasks/<int:task_id>/sync", methods=["POST"])
@jwt_required()
def sync_task_to_jira(task_id):
    """Sync a TMS task to Jira issue"""
    try:
        user_id = int(get_jwt_identity())
        data = request.get_json()
        
        if not data or "jira_project_key" not in data:
            response = ApiResponse.failure("jira_project_key is required", code=400)
            return jsonify(response.to_dict()), response.code
        
        jira_project_key = data["jira_project_key"]
        sync_service = JiraSyncService(user_id)
        
        response = sync_service.sync_task_to_jira(task_id, jira_project_key)
        return jsonify(response.to_dict()), response.code
        
    except Exception as e:
        response = ApiResponse.failure(f"Error syncing task to Jira: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@jira_bp.route("/issues/<issue_key>", methods=["GET"])
@jwt_required()
def get_jira_issue(issue_key):
    """Get details of a specific Jira issue"""
    try:
        user_id = int(get_jwt_identity())
        sync_service = JiraSyncService(user_id)
        
        if not sync_service.is_connected():
            response = ApiResponse.failure("No active Jira integration found", code=400)
            return jsonify(response.to_dict()), response.code
        
        # Get issue from Jira
        jira_issue = sync_service.jira_client.get_issue(
            issue_key,
            fields=["summary", "description", "status", "priority", "assignee", "created", "updated", "comments"]
        )
        
        fields = jira_issue.get("fields", {})
        
        # Format issue for frontend
        formatted_issue = {
            "id": jira_issue.get("id"),
            "key": jira_issue.get("key"),
            "summary": fields.get("summary", ""),
            "description": fields.get("description", ""),
            "status": fields.get("status", {}).get("name", ""),
            "priority": fields.get("priority", {}).get("name", ""),
            "assignee": fields.get("assignee", {}).get("displayName", "") if fields.get("assignee") else "",
            "created": fields.get("created"),
            "updated": fields.get("updated"),
            "comments": []
        }
        
        # Get comments
        comments = sync_service.jira_client.get_comments(issue_key)
        for comment in comments:
            formatted_issue["comments"].append({
                "id": comment.get("id"),
                "author": comment.get("author", {}).get("displayName", ""),
                "body": comment.get("body", ""),
                "created": comment.get("created"),
                "updated": comment.get("updated")
            })
        
        response = ApiResponse.success(
            "Jira issue retrieved successfully",
            data=formatted_issue
        )
        return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(f"Error retrieving Jira issue: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@jira_bp.route("/issues/<issue_key>/comments", methods=["POST"])
@jwt_required()
def add_jira_comment(issue_key):
    """Add comment to a Jira issue"""
    try:
        user_id = int(get_jwt_identity())
        data = request.get_json()

        if not data or "comment" not in data:
            response = ApiResponse.failure("comment is required", code=400)
            return jsonify(response.to_dict()), response.code

        comment_text = data["comment"]
        sync_service = JiraSyncService(user_id)

        if not sync_service.is_connected():
            response = ApiResponse.failure("No active Jira integration found", code=400)
            return jsonify(response.to_dict()), response.code

        # Add comment to Jira
        comment_response = sync_service.jira_client.add_comment(issue_key, comment_text)

        response = ApiResponse.success(
            "Comment added successfully",
            data=comment_response
        )
        return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(f"Error adding comment: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@jira_bp.route("/search", methods=["GET"])
@jwt_required()
def search_jira_issues():
    """Search Jira issues using JQL"""
    try:
        user_id = int(get_jwt_identity())
        sync_service = JiraSyncService(user_id)

        if not sync_service.is_connected():
            response = ApiResponse.failure("No active Jira integration found", code=400)
            return jsonify(response.to_dict()), response.code

        # Get JQL from query parameters
        jql = request.args.get("jql", "")
        start_at = int(request.args.get("startAt", 0))
        max_results = int(request.args.get("maxResults", 50))

        if not jql:
            response = ApiResponse.failure("JQL query is required", code=400)
            return jsonify(response.to_dict()), response.code

        # Search issues
        search_response = sync_service.jira_client._make_request(
            'GET',
            'search',
            params={
                'jql': jql,
                'startAt': start_at,
                'maxResults': max_results,
                'fields': 'summary,description,status,priority,assignee,created,updated'
            }
        )

        # Format issues for frontend
        formatted_issues = []
        for issue in search_response.get("issues", []):
            fields = issue.get("fields", {})
            formatted_issues.append({
                "id": issue.get("id"),
                "key": issue.get("key"),
                "summary": fields.get("summary", ""),
                "description": fields.get("description", ""),
                "status": fields.get("status", {}).get("name", ""),
                "priority": fields.get("priority", {}).get("name", ""),
                "assignee": fields.get("assignee", {}).get("displayName", "") if fields.get("assignee") else "",
                "created": fields.get("created"),
                "updated": fields.get("updated")
            })

        response = ApiResponse.success(
            "Jira search completed successfully",
            data={
                "issues": formatted_issues,
                "total": search_response.get("total", 0),
                "startAt": search_response.get("startAt", 0),
                "maxResults": search_response.get("maxResults", 50)
            }
        )
        return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(f"Error searching Jira issues: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code
