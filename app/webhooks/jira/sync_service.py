"""
Jira synchronization service for TMS Backend
Handles syncing projects and issues between Jira and TMS
"""

from datetime import datetime
from typing import Dict, Any, List, Optional
from tenacity import retry, stop_after_attempt, wait_exponential

from app.helpers.extensions import db
from app.models import User, Project, Task, Integration, ExternalTaskMapping
from app.models.api_response import ApiResponse
from app.webhooks.jira.api_client import JiraAPIClient
from app.webhooks.jira.exceptions import JiraIntegrationError, NotFoundError


class JiraSyncService:
    """Service for syncing data between Jira and TMS"""
    
    def __init__(self, user_id: int):
        """
        Initialize Jira sync service for a user
        
        Args:
            user_id (int): User ID
        """
        self.user_id = user_id
        self.integration = self._get_jira_integration()
        
        if self.integration:
            cloud_id = self.integration.settings.get("cloud_id")
            self.jira_client = JiraAPIClient(self.integration.access_token, cloud_id)
        else:
            self.jira_client = None
    
    def _get_jira_integration(self) -> Optional[Integration]:
        """Get active Jira integration for user"""
        return Integration.query.filter_by(
            user_id=self.user_id,
            platform="jira",
            is_active=True
        ).first()
    
    def is_connected(self) -> bool:
        """Check if user has active Jira integration"""
        return self.integration is not None and self.jira_client is not None
    
    def get_jira_projects(self) -> ApiResponse:
        """
        Get all Jira projects accessible to the user

        Returns:
            ApiResponse: API response object
        """
        print(f"DEBUG: get_jira_projects called for user {self.user_id}")
        print(f"DEBUG: is_connected = {self.is_connected()}")

        if not self.is_connected():
            print("DEBUG: Not connected, returning failure")
            return ApiResponse.failure("Jira integration not connected", code=400)

        try:
            print("DEBUG: Calling jira_client.get_projects()")
            projects = self.jira_client.get_projects()
            print(f"DEBUG: Got {len(projects) if projects else 0} projects")

            print("DEBUG: Creating success response")
            response = ApiResponse.success(
                "Jira projects retrieved successfully",
                data={"projects": projects}
            )
            print(f"DEBUG: Success response created: {type(response)}")
            return response
        except Exception as e:
            print(f"DEBUG: Exception in get_jira_projects: {e}")
            print(f"DEBUG: Exception type: {type(e)}")
            import traceback
            traceback.print_exc()
            return ApiResponse.failure(
                f"Failed to get Jira projects: {str(e)}",
                code=500
            )
    
    def sync_jira_project_to_tms(self, jira_project_key: str) -> ApiResponse:
        """
        Sync a Jira project to TMS as a new project
        
        Args:
            jira_project_key (str): Jira project key
            
        Returns:
            ApiResponse: Created TMS project
        """
        try:
            if not self.is_connected():
                return ApiResponse.failure("No active Jira integration found", code=400)
            
            # Get Jira project details
            jira_project = self.jira_client.get_project(jira_project_key, expand="description,lead")
            
            # Check if project already synced
            existing_project = Project.query.filter_by(
                jira_project_key=jira_project_key,
                deleted_at=None
            ).first()
            
            if existing_project:
                return ApiResponse.failure("Project already synced", code=400)
            
            # Create TMS project
            project = Project(
                name=jira_project.get("name"),
                description=jira_project.get("description", ""),
                created_by=self.user_id,
                jira_project_key=jira_project_key,
                jira_project_id=jira_project.get("id")
            )
            
            db.session.add(project)
            db.session.flush()  # Get project ID
            
            # Add creator as project admin
            from app.models import UserProject
            user_project = UserProject(
                user_id=self.user_id,
                project_id=project.id,
                role="admin"
            )
            db.session.add(user_project)
            
            db.session.commit()
            
            return ApiResponse.success(
                "Jira project synced successfully",
                data=project.to_dict()
            )
            
        except JiraIntegrationError as e:
            db.session.rollback()
            return ApiResponse.failure(str(e), code=e.code)
        except Exception as e:
            db.session.rollback()
            return ApiResponse.failure(f"Error syncing Jira project: {str(e)}", code=500)
    
    def get_jira_issues(self, project_id: int, jql: Optional[str] = None) -> ApiResponse:
        """
        Get Jira issues for a synced project
        
        Args:
            project_id (int): TMS project ID
            jql (str, optional): Custom JQL query
            
        Returns:
            ApiResponse: List of Jira issues
        """
        try:
            if not self.is_connected():
                return ApiResponse.failure("No active Jira integration found", code=400)
            
            # Get project
            project = Project.query.get(project_id)
            if not project or not project.jira_project_key:
                return ApiResponse.failure("Project not found or not synced with Jira", code=404)
            
            # Get issues from Jira
            if not jql:
                jql = f"project = {project.jira_project_key}"
            
            issues_response = self.jira_client.get_issues(
                project.jira_project_key,
                jql=jql,
                fields=["summary", "description", "status", "priority", "assignee", "created", "updated"]
            )
            
            # Format issues for frontend
            formatted_issues = []
            for issue in issues_response.get("issues", []):
                fields = issue.get("fields", {})
                formatted_issues.append({
                    "id": issue.get("id"),
                    "key": issue.get("key"),
                    "summary": fields.get("summary", ""),
                    "description": fields.get("description", ""),
                    "status": fields.get("status", {}).get("name", ""),
                    "priority": fields.get("priority", {}).get("name", ""),
                    "assignee": fields.get("assignee", {}).get("displayName", "") if fields.get("assignee") else "",
                    "created": fields.get("created"),
                    "updated": fields.get("updated")
                })
            
            return ApiResponse.success(
                "Jira issues retrieved successfully",
                data={
                    "issues": formatted_issues,
                    "total": issues_response.get("total", 0)
                }
            )
            
        except JiraIntegrationError as e:
            return ApiResponse.failure(str(e), code=e.code)
        except Exception as e:
            return ApiResponse.failure(f"Error retrieving Jira issues: {str(e)}", code=500)
    
    def sync_jira_issue_to_task(self, project_id: int, jira_issue_key: str) -> ApiResponse:
        """
        Sync a Jira issue to TMS as a new task
        
        Args:
            project_id (int): TMS project ID
            jira_issue_key (str): Jira issue key
            
        Returns:
            ApiResponse: Created TMS task
        """
        try:
            if not self.is_connected():
                return ApiResponse.failure("No active Jira integration found", code=400)
            
            # Get project
            project = Project.query.get(project_id)
            if not project:
                return ApiResponse.failure("Project not found", code=404)
            
            # Check if issue already synced
            existing_mapping = ExternalTaskMapping.query.filter_by(
                external_platform="jira",
                external_task_id=jira_issue_key
            ).first()
            
            if existing_mapping:
                return ApiResponse.failure("Issue already synced", code=400)
            
            # Get Jira issue details
            jira_issue = self.jira_client.get_issue(jira_issue_key)
            fields = jira_issue.get("fields", {})
            
            # Map Jira status to TMS status
            jira_status = fields.get("status", {}).get("name", "").lower()
            tms_status = self._map_jira_status_to_tms(jira_status)
            
            # Map Jira priority to TMS priority
            jira_priority = fields.get("priority", {}).get("name", "").lower()
            tms_priority = self._map_jira_priority_to_tms(jira_priority)
            
            # Create TMS task
            task = Task(
                title=fields.get("summary", ""),
                description=fields.get("description", ""),
                status=tms_status,
                priority=tms_priority,
                project_id=project_id,
                created_by=self.user_id
            )
            
            db.session.add(task)
            db.session.flush()  # Get task ID
            
            # Create external mapping
            mapping = ExternalTaskMapping(
                task_id=task.id,
                external_platform="jira",
                external_task_id=jira_issue_key,
                external_project_id=project.jira_project_key,
                sync_enabled=True
            )
            
            db.session.add(mapping)
            db.session.commit()
            
            return ApiResponse.success(
                "Jira issue synced successfully",
                data=task.to_dict()
            )
            
        except JiraIntegrationError as e:
            db.session.rollback()
            return ApiResponse.failure(str(e), code=e.code)
        except Exception as e:
            db.session.rollback()
            return ApiResponse.failure(f"Error syncing Jira issue: {str(e)}", code=500)

    def sync_task_to_jira(self, task_id: int, jira_project_key: str) -> ApiResponse:
        """
        Sync a TMS task to Jira as a new issue

        Args:
            task_id (int): TMS task ID
            jira_project_key (str): Jira project key

        Returns:
            ApiResponse: Created Jira issue
        """
        try:
            if not self.is_connected():
                return ApiResponse.failure("No active Jira integration found", code=400)

            # Get task
            task = Task.query.get(task_id)
            if not task:
                return ApiResponse.failure("Task not found", code=404)

            # Check if task already synced
            existing_mapping = ExternalTaskMapping.query.filter_by(
                task_id=task_id,
                external_platform="jira"
            ).first()

            if existing_mapping:
                return ApiResponse.failure("Task already synced", code=400)

            # Get issue types for the project
            issue_types = self.jira_client.get_issue_types(jira_project_key)
            if not issue_types:
                return ApiResponse.failure("No issue types found for project", code=400)

            # Use first available issue type (usually "Task" or "Story")
            issue_type_id = issue_types[0]["id"]

            # Create Jira issue
            issue_data = {
                "fields": {
                    "project": {"key": jira_project_key},
                    "summary": task.title,
                    "description": {
                        "type": "doc",
                        "version": 1,
                        "content": [
                            {
                                "type": "paragraph",
                                "content": [
                                    {
                                        "type": "text",
                                        "text": task.description or "No description"
                                    }
                                ]
                            }
                        ]
                    },
                    "issuetype": {"id": issue_type_id}
                }
            }

            jira_issue = self.jira_client.create_issue(issue_data)
            jira_issue_key = jira_issue.get("key")

            # Create external mapping
            mapping = ExternalTaskMapping(
                task_id=task_id,
                external_platform="jira",
                external_task_id=jira_issue_key,
                external_project_id=jira_project_key,
                sync_enabled=True
            )

            db.session.add(mapping)
            db.session.commit()

            return ApiResponse.success(
                "Task synced to Jira successfully",
                data={"jira_issue_key": jira_issue_key}
            )

        except JiraIntegrationError as e:
            db.session.rollback()
            return ApiResponse.failure(str(e), code=e.code)
        except Exception as e:
            db.session.rollback()
            return ApiResponse.failure(f"Error syncing task to Jira: {str(e)}", code=500)

    def _map_jira_status_to_tms(self, jira_status: str) -> str:
        """Map Jira status to TMS status"""
        status_mapping = {
            "to do": "todo",
            "in progress": "in_progress",
            "done": "done",
            "closed": "done",
            "resolved": "done",
            "open": "todo",
            "new": "todo"
        }
        return status_mapping.get(jira_status, "todo")

    def _map_jira_priority_to_tms(self, jira_priority: str) -> str:
        """Map Jira priority to TMS priority"""
        priority_mapping = {
            "highest": "high",
            "high": "high",
            "medium": "medium",
            "low": "low",
            "lowest": "low",
            "critical": "high",
            "major": "high",
            "minor": "low",
            "trivial": "low"
        }
        return priority_mapping.get(jira_priority, "medium")
