"""
Webhook routes for GitHub integration
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.models.api_response import ApiResponse
from app.models.webhook import GitHubWebhook
from app.webhooks.github_webhook_service import (
    GitHubWebhookService,
    GitHubWebhookEventProcessor,
)
from app.webhooks.webhook_validator import WebhookValidator
from app.models.webhook import WebhookEvent

webhooks_bp = Blueprint("webhooks_bp", __name__, url_prefix="/webhooks")


@webhooks_bp.route("/github", methods=["POST"])
def github_webhook():
    """
    GitHub webhook endpoint - receives webhook events from GitHub
    This endpoint does not require JWT authentication as it's called by GitHub
    """
    try:
        # Validate webhook request
        is_valid, error_msg, headers_info = (
            WebhookValidator.validate_github_webhook_request()
        )
        if not is_valid:
            return jsonify({"error": error_msg}), 400

        # Get request data
        payload = request.get_json()
        if not payload:
            return jsonify({"error": "No JSON payload"}), 400

        # Extract repository information
        repository = payload.get("repository", {})
        repo_full_name = repository.get("full_name")

        if not repo_full_name:
            return (
                jsonify({"error": "Repository information not found in payload"}),
                400,
            )

        # Find webhook configuration
        webhook = GitHubWebhook.query.filter_by(
            github_repo_full_name=repo_full_name, is_active=True
        ).first()

        if not webhook:
            return (
                jsonify(
                    {
                        "error": f"No active webhook found for repository {repo_full_name}"
                    }
                ),
                404,
            )

        # Verify webhook signature
        payload_body = request.get_data()
        signature_header = headers_info["signature"]

        if not WebhookValidator.verify_github_signature(
            payload_body, webhook.secret, signature_header
        ):
            return jsonify({"error": "Invalid webhook signature"}), 401

        # Process the webhook event
        event_type = headers_info["event"]
        delivery_id = headers_info["delivery_id"]

        result = GitHubWebhookEventProcessor.process_webhook_event(
            webhook, event_type, payload, delivery_id
        )

        if result.success:
            return jsonify({"message": result.message, "data": result.data}), 200
        else:
            return jsonify({"error": result.message}), 500

    except Exception as e:
        current_app.logger.error(f"Webhook processing error: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@webhooks_bp.route("/create", methods=["POST"])
@jwt_required()
def create_webhooks():
    """
    Create webhooks for multiple repositories
    """
    try:
        current_user_id = int(get_jwt_identity())
        data = request.get_json()

        if not data:
            response = ApiResponse.failure("No data provided", code=400)
            return jsonify(response.to_dict()), response.code

        repository_urls = data.get("repository_urls", [])
        if not repository_urls or not isinstance(repository_urls, list):
            response = ApiResponse.failure(
                "repository_urls is required and must be a list", code=400
            )
            return jsonify(response.to_dict()), response.code

        # Get base webhook URL from config or request
        base_webhook_url = data.get("webhook_url")
        if not base_webhook_url:
            # Use default webhook URL
            base_webhook_url = f"{request.host_url.rstrip('/')}/webhooks/github"

        # Create webhook service
        webhook_service = GitHubWebhookService(current_user_id)

        # Create webhooks
        result = webhook_service.create_webhooks_for_repositories(
            repository_urls, base_webhook_url
        )

        return jsonify(result.to_dict()), result.code

    except ValueError as e:
        response = ApiResponse.failure(str(e), code=400)
        return jsonify(response.to_dict()), response.code
    except Exception as e:
        response = ApiResponse.failure(f"Error creating webhooks: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@webhooks_bp.route("/list", methods=["GET"])
@jwt_required()
def list_webhooks():
    """
    List all webhooks for the authenticated user
    """
    try:
        current_user_id = int(get_jwt_identity())

        # Create webhook service
        webhook_service = GitHubWebhookService(current_user_id)

        # Get user webhooks
        result = webhook_service.get_user_webhooks()

        return jsonify(result.to_dict()), result.code

    except ValueError as e:
        response = ApiResponse.failure(str(e), code=400)
        return jsonify(response.to_dict()), response.code
    except Exception as e:
        response = ApiResponse.failure(f"Error fetching webhooks: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@webhooks_bp.route("/<int:webhook_id>", methods=["DELETE"])
@jwt_required()
def delete_webhook(webhook_id):
    """
    Delete a specific webhook
    """
    try:
        current_user_id = int(get_jwt_identity())

        # Create webhook service
        webhook_service = GitHubWebhookService(current_user_id)

        # Delete webhook
        result = webhook_service.delete_webhook(webhook_id)

        return jsonify(result.to_dict()), result.code

    except ValueError as e:
        response = ApiResponse.failure(str(e), code=400)
        return jsonify(response.to_dict()), response.code
    except Exception as e:
        response = ApiResponse.failure(f"Error deleting webhook: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@webhooks_bp.route("/events/<int:webhook_id>", methods=["GET"])
@jwt_required()
def get_webhook_events(webhook_id):
    """
    Get events for a specific webhook
    """
    try:
        current_user_id = int(get_jwt_identity())

        # Check if webhook belongs to user
        webhook = GitHubWebhook.query.filter_by(
            id=webhook_id, user_id=current_user_id
        ).first()

        if not webhook:
            response = ApiResponse.failure("Webhook not found", code=404)
            return jsonify(response.to_dict()), response.code

        # Get pagination parameters
        page = request.args.get("page", 1, type=int)
        per_page = min(request.args.get("per_page", 20, type=int), 100)

        # Get events
        events_query = WebhookEvent.query.filter_by(webhook_id=webhook_id).order_by(
            WebhookEvent.created_at.desc()
        )
        events_paginated = events_query.paginate(
            page=page, per_page=per_page, error_out=False
        )

        events_list = [event.to_dict() for event in events_paginated.items]

        response = ApiResponse.success(
            f"Found {len(events_list)} event(s) for webhook {webhook_id}",
            data={
                "events": events_list,
                "pagination": {
                    "page": page,
                    "per_page": per_page,
                    "total": events_paginated.total,
                    "pages": events_paginated.pages,
                    "has_next": events_paginated.has_next,
                    "has_prev": events_paginated.has_prev,
                },
            },
        )

        return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(
            f"Error fetching webhook events: {str(e)}", code=500
        )
        return jsonify(response.to_dict()), response.code
