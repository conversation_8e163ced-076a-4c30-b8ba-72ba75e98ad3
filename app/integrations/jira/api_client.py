"""
Jira API Client for TMS Backend
Handles communication with Jira REST API
"""

import requests
from typing import Dict, Any, List, Optional
from datetime import datetime

from app.integrations.jira.exceptions import JiraIntegrationError
from app.models.api_response import ApiResponse


class JiraAPIClient:
    """Jira REST API client"""
    
    def __init__(self, access_token: str, cloud_id: str, base_url: Optional[str] = None):
        """
        Initialize Jira API client
        
        Args:
            access_token (str): OAuth access token
            cloud_id (str): Atlassian cloud ID
            base_url (str, optional): Custom base URL for Jira instance
        """
        self.access_token = access_token
        self.cloud_id = cloud_id
        
        if base_url:
            self.base_url = base_url.rstrip('/')
        else:
            self.base_url = f"https://api.atlassian.com/ex/jira/{cloud_id}"
        
        self.headers = {
            'Authorization': f'Bearer {access_token}',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    
    def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        params: Optional[Dict] = None,
        data: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        Make HTTP request to Jira API
        
        Args:
            method (str): HTTP method
            endpoint (str): API endpoint
            params (Dict, optional): Query parameters
            data (Dict, optional): Request body data
            
        Returns:
            Dict[str, Any]: Response data
            
        Raises:
            JiraIntegrationError: If request fails
        """
        url = f"{self.base_url}/rest/api/3/{endpoint.lstrip('/')}"
        
        try:
            response = requests.request(
                method=method,
                url=url,
                headers=self.headers,
                params=params,
                json=data,
                timeout=30
            )
            
            # Handle different response codes
            if response.status_code == 401:
                raise JiraIntegrationError(
                    "Unauthorized: Invalid or expired token",
                    operation=f"{method} {endpoint}",
                    jira_error="401 Unauthorized"
                )
            elif response.status_code == 403:
                raise JiraIntegrationError(
                    "Forbidden: Insufficient permissions",
                    operation=f"{method} {endpoint}",
                    jira_error="403 Forbidden"
                )
            elif response.status_code == 404:
                raise JiraIntegrationError(
                    "Not found: Resource does not exist",
                    operation=f"{method} {endpoint}",
                    jira_error="404 Not Found"
                )
            elif response.status_code >= 400:
                error_data = response.json() if response.content else {}
                raise JiraIntegrationError(
                    f"Jira API error: {response.status_code}",
                    operation=f"{method} {endpoint}",
                    jira_error=str(error_data)
                )
            
            # Return JSON response or empty dict for 204 No Content
            if response.status_code == 204:
                return {}
            
            return response.json()
            
        except requests.RequestException as e:
            raise JiraIntegrationError(
                f"Network error: {str(e)}",
                operation=f"{method} {endpoint}"
            )
        except Exception as e:
            raise JiraIntegrationError(
                f"Unexpected error: {str(e)}",
                operation=f"{method} {endpoint}"
            )
    
    def get_projects(self, expand: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get all projects accessible to the user
        
        Args:
            expand (str, optional): Additional fields to expand
            
        Returns:
            List[Dict[str, Any]]: List of projects
        """
        params = {}
        if expand:
            params['expand'] = expand
        
        response = self._make_request('GET', 'project', params=params)
        return response if isinstance(response, list) else []
    
    def get_project(self, project_key: str, expand: Optional[str] = None) -> Dict[str, Any]:
        """
        Get specific project by key
        
        Args:
            project_key (str): Project key
            expand (str, optional): Additional fields to expand
            
        Returns:
            Dict[str, Any]: Project data
        """
        params = {}
        if expand:
            params['expand'] = expand
        
        return self._make_request('GET', f'project/{project_key}', params=params)
    
    def get_issues(
        self, 
        project_key: str, 
        jql: Optional[str] = None,
        start_at: int = 0,
        max_results: int = 50,
        fields: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Get issues from a project
        
        Args:
            project_key (str): Project key
            jql (str, optional): JQL query
            start_at (int): Starting index for pagination
            max_results (int): Maximum number of results
            fields (List[str], optional): Fields to include
            
        Returns:
            Dict[str, Any]: Issues response with pagination
        """
        if not jql:
            jql = f"project = {project_key}"
        
        params = {
            'jql': jql,
            'startAt': start_at,
            'maxResults': max_results
        }
        
        if fields:
            params['fields'] = ','.join(fields)
        
        return self._make_request('GET', 'search', params=params)
    
    def get_issue(self, issue_key: str, fields: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Get specific issue by key
        
        Args:
            issue_key (str): Issue key
            fields (List[str], optional): Fields to include
            
        Returns:
            Dict[str, Any]: Issue data
        """
        params = {}
        if fields:
            params['fields'] = ','.join(fields)
        
        return self._make_request('GET', f'issue/{issue_key}', params=params)
    
    def create_issue(self, issue_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create new issue
        
        Args:
            issue_data (Dict[str, Any]): Issue creation data
            
        Returns:
            Dict[str, Any]: Created issue data
        """
        return self._make_request('POST', 'issue', data=issue_data)
    
    def update_issue(self, issue_key: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update existing issue
        
        Args:
            issue_key (str): Issue key
            update_data (Dict[str, Any]): Update data
            
        Returns:
            Dict[str, Any]: Response data
        """
        return self._make_request('PUT', f'issue/{issue_key}', data=update_data)
    
    def delete_issue(self, issue_key: str) -> bool:
        """
        Delete issue
        
        Args:
            issue_key (str): Issue key
            
        Returns:
            bool: True if successful
        """
        self._make_request('DELETE', f'issue/{issue_key}')
        return True
    
    def get_issue_types(self, project_key: str) -> List[Dict[str, Any]]:
        """
        Get issue types for a project
        
        Args:
            project_key (str): Project key
            
        Returns:
            List[Dict[str, Any]]: Issue types
        """
        project = self.get_project(project_key, expand='issueTypes')
        return project.get('issueTypes', [])
    
    def get_statuses(self) -> List[Dict[str, Any]]:
        """
        Get all available statuses
        
        Returns:
            List[Dict[str, Any]]: Statuses
        """
        return self._make_request('GET', 'status')
    
    def get_priorities(self) -> List[Dict[str, Any]]:
        """
        Get all available priorities
        
        Returns:
            List[Dict[str, Any]]: Priorities
        """
        return self._make_request('GET', 'priority')
    
    def get_users(self, project_key: str) -> List[Dict[str, Any]]:
        """
        Get users who can be assigned to issues in a project
        
        Args:
            project_key (str): Project key
            
        Returns:
            List[Dict[str, Any]]: Users
        """
        params = {'project': project_key}
        return self._make_request('GET', 'user/assignable/search', params=params)
    
    def add_comment(self, issue_key: str, comment_body: str) -> Dict[str, Any]:
        """
        Add comment to issue
        
        Args:
            issue_key (str): Issue key
            comment_body (str): Comment text
            
        Returns:
            Dict[str, Any]: Created comment data
        """
        comment_data = {
            'body': {
                'type': 'doc',
                'version': 1,
                'content': [
                    {
                        'type': 'paragraph',
                        'content': [
                            {
                                'type': 'text',
                                'text': comment_body
                            }
                        ]
                    }
                ]
            }
        }
        
        return self._make_request('POST', f'issue/{issue_key}/comment', data=comment_data)
    
    def get_comments(self, issue_key: str) -> List[Dict[str, Any]]:
        """
        Get comments for an issue
        
        Args:
            issue_key (str): Issue key
            
        Returns:
            List[Dict[str, Any]]: Comments
        """
        response = self._make_request('GET', f'issue/{issue_key}/comment')
        return response.get('comments', [])
    
    def transition_issue(self, issue_key: str, transition_id: str) -> bool:
        """
        Transition issue to new status
        
        Args:
            issue_key (str): Issue key
            transition_id (str): Transition ID
            
        Returns:
            bool: True if successful
        """
        transition_data = {
            'transition': {
                'id': transition_id
            }
        }
        
        self._make_request('POST', f'issue/{issue_key}/transitions', data=transition_data)
        return True
    
    def get_transitions(self, issue_key: str) -> List[Dict[str, Any]]:
        """
        Get available transitions for an issue

        Args:
            issue_key (str): Issue key

        Returns:
            List[Dict[str, Any]]: Available transitions
        """
        response = self._make_request('GET', f'issue/{issue_key}/transitions')
        return response.get('transitions', [])

    def get_current_user(self) -> Dict[str, Any]:
        """
        Get current user information

        Returns:
            Dict[str, Any]: Current user data
        """
        return self._make_request('GET', 'myself')

    def create_webhook(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a webhook

        Args:
            webhook_data (Dict[str, Any]): Webhook configuration data

        Returns:
            Dict[str, Any]: Created webhook data
        """
        return self._make_request('POST', 'webhook', data=webhook_data)

    def delete_webhook(self, webhook_id: str) -> bool:
        """
        Delete a webhook

        Args:
            webhook_id (str): Webhook ID

        Returns:
            bool: True if successful
        """
        self._make_request('DELETE', f'webhook/{webhook_id}')
        return True
