"""
Custom exceptions for Jira integration
"""

from typing import Optional, Dict, Any

class JiraIntegrationError(Exception):
    """Exception for Jira integration errors"""
    def __init__(
        self, 
        message: str, 
        code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.code = code
        self.details = details
        super().__init__(message)

class NotFoundError(Exception):
    """Exception for not found errors"""
    def __init__(
        self, 
        message: str, 
        code: int = 404,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.code = code
        self.details = details
        super().__init__(message)

class ConfigurationError(Exception):
    """Exception for configuration errors"""
    def __init__(
        self, 
        message: str, 
        code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.code = code
        self.details = details
        super().__init__(message)
