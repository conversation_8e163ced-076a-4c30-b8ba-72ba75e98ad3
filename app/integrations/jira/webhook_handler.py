"""
Jira Webhook Handler for TMS Backend
Handles incoming webhooks from Jira
"""

from typing import Dict, Any
from flask import request
from app.integrations.jira.exceptions import JiraIntegrationError
from app.models import Task, ExternalTaskMapping, Integration
from app.integrations.jira.sync_service import JiraSyncService


class JiraWebhookHandler:
    """Handler for Jira webhooks"""
    
    def __init__(self, user_id: int):
        self.user_id = user_id
        self.sync_service = JiraSyncService(user_id)
    
    def handle_issue_event(self, webhook_data: Dict[str, Any]) -> None:
        """
        Handle Jira issue events (created, updated, deleted)
        """
        try:
            event_type = webhook_data.get('webhookEvent')
            issue = webhook_data.get('issue')
            
            if not issue:
                raise JiraIntegrationError("No issue data in webhook")
                
            external_id = issue.get('id')
            external_key = issue.get('key')
            
            # Find or create external task mapping
            mapping = ExternalTaskMapping.query.filter_by(
                external_id=external_id,
                integration_id=self.sync_service.integration.id
            ).first()
            
            if event_type == 'jira:issue_created':
                self._handle_issue_created(issue, mapping)
            elif event_type == 'jira:issue_updated':
                self._handle_issue_updated(issue, mapping)
            elif event_type == 'jira:issue_deleted':
                self._handle_issue_deleted(external_id)
            
        except Exception as e:
            raise JiraIntegrationError(f"Failed to handle webhook: {str(e)}")
    
    def _handle_issue_created(self, issue: Dict[str, Any], mapping: ExternalTaskMapping) -> None:
        """Handle new issue creation"""
        if not mapping:
            # Create new task in TMS
            task = Task(
                title=issue.get('fields', {}).get('summary'),
                description=issue.get('fields', {}).get('description'),
                status=issue.get('fields', {}).get('status', {}).get('name'),
                priority=issue.get('fields', {}).get('priority', {}).get('name')
            )
            
            db.session.add(task)
            db.session.flush()
            
            # Create mapping
            mapping = ExternalTaskMapping(
                task_id=task.id,
                integration_id=self.sync_service.integration.id,
                external_id=issue.get('id'),
                external_key=issue.get('key')
            )
            db.session.add(mapping)
            
            # Send telegram notification
            self._send_telegram_notification(
                f"New Jira issue created: {issue.get('key')} - {issue.get('fields', {}).get('summary')}"
            )
            
    def _handle_issue_updated(self, issue: Dict[str, Any], mapping: ExternalTaskMapping) -> None:
        """Handle issue update"""
        if not mapping:
            raise JiraIntegrationError("No mapping found for updated issue")
            
        task = Task.query.get(mapping.task_id)
        if not task:
            raise JiraIntegrationError("No task found for mapping")
            
        # Update task fields
        task.title = issue.get('fields', {}).get('summary')
        task.description = issue.get('fields', {}).get('description')
        task.status = issue.get('fields', {}).get('status', {}).get('name')
        task.priority = issue.get('fields', {}).get('priority', {}).get('name')
        
        # Send telegram notification
        self._send_telegram_notification(
            f"Jira issue updated: {issue.get('key')} - {issue.get('fields', {}).get('summary')}"
        )
    
    def _handle_issue_deleted(self, external_id: str) -> None:
        """Handle issue deletion"""
        mapping = ExternalTaskMapping.query.filter_by(
            external_id=external_id,
            integration_id=self.sync_service.integration.id
        ).first()
        
        if mapping:
            # Soft delete task in TMS
            task = Task.query.get(mapping.task_id)
            if task:
                task.is_deleted = True
                
            # Delete mapping
            db.session.delete(mapping)
            
            # Send telegram notification
            self._send_telegram_notification(
                f"Jira issue deleted: {external_id}"
            )
    
    def _send_telegram_notification(self, message: str) -> None:
        """Send notification to Telegram"""
        from app.telegram.bot import send_message
        send_message(message)

# Add webhook route
def jira_webhook():
    """Jira webhook endpoint"""
    data = request.get_json()
    user_id = request.args.get('user_id')
    
    if not user_id:
        return {'error': 'Missing user_id'}, 400
        
    handler = JiraWebhookHandler(user_id)
    handler.handle_issue_event(data)
    
    return {'success': True}, 200
