"""
Utility functions for Jira integration
"""

from typing import Dict, Any, Optional
from flask import jsonify

class ApiResponse:
    """Base API response class"""
    
    def __init__(
        self, 
        success: bool, 
        message: str, 
        data: Optional[Dict[str, Any]] = None,
        code: int = 200
    ):
        self.success = success
        self.message = message
        self.data = data
        self.code = code
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert response to dictionary"""
        response = {
            "success": self.success,
            "message": self.message
        }
        
        if self.data is not None:
            response["data"] = self.data
            
        return response
        
    @classmethod
    def success(
        cls, 
        message: str, 
        data: Optional[Dict[str, Any]] = None
    ) -> 'ApiResponse':
        """Create success response"""
        return cls(True, message, data)
        
    @classmethod
    def failure(
        cls, 
        message: str, 
        code: int = 400,
        data: Optional[Dict[str, Any]] = None
    ) -> 'ApiResponse':
        """Create failure response"""
        return cls(False, message, data, code)
