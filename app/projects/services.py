from app.models import Project, User
from app.models.api_response import ApiResponse
from app.helpers.extensions import db
from datetime import datetime
from typing import <PERSON><PERSON>


def get_all_projects(user_id: int, include_deleted: bool = False, page: int = 1, per_page: int = 20) -> Tuple[dict, int]:
    """Get all projects for a user with pagination"""
    try:
        from app.models.auth import Role, UserRole
        from sqlalchemy import or_

        # Check if user is admin
        user_roles = db.session.query(Role).join(UserRole).filter(
            UserRole.user_id == user_id
        ).all()
        role_names = [role.name for role in user_roles]
        is_admin = 'admin' in role_names

        query = Project.query

        if not include_deleted:
            query = query.filter(Project.deleted_at.is_(None))

        # If user is admin, show all projects
        # Otherwise, show projects where user is creator, owner, or member
        if not is_admin:
            from app.models.project import UserProject

            # Get projects where user is creator, owner, or has membership
            query = query.filter(
                or_(
                    Project.created_by == user_id,
                    Project.user_id == user_id,
                    Project.id.in_(
                        db.session.query(UserProject.project_id).filter(
                            UserProject.user_id == user_id
                        )
                    )
                )
            )

        # Get total count for pagination
        total_items = query.count()

        # Apply pagination
        projects = query.offset((page - 1) * per_page).limit(per_page).all()

        project_dicts = [project.to_dict() for project in projects]

        response = ApiResponse.paginated(
            items=project_dicts,
            page=page,
            per_page=per_page,
            total_items=total_items,
            message="Projects retrieved successfully"
        )
        return response.to_dict(), response.code
    except Exception as e:
        response = ApiResponse.failure(f"Error retrieving projects: {str(e)}", code=500)
        return response.to_dict(), response.code


def get_project_by_id(project_id: int, user_id: int) -> Tuple[dict, int]:
    """Get a specific project by ID"""
    try:
        project = Project.query.filter(
            Project.id == project_id,
            Project.deleted_at.is_(None),
            (Project.created_by == user_id) | (Project.user_id == user_id)
        ).first()

        if not project:
            response = ApiResponse.failure("Project not found", code=404)
            return response.to_dict(), response.code

        response = ApiResponse.success("Project retrieved successfully", data={"project": project.to_dict()})
        return response.to_dict(), response.code
    except Exception as e:
        response = ApiResponse.failure(f"Error retrieving project: {str(e)}", code=500)
        return response.to_dict(), response.code


def create_project(data: dict, user_id: int) -> Tuple[dict, int]:
    """Create a new project"""
    try:
        # Parse dates if provided
        start_date = None
        end_date = None

        if data.get("start_date"):
            try:
                start_date = datetime.fromisoformat(data["start_date"]).date()
            except ValueError:
                response = ApiResponse.failure("Invalid start_date format. Use YYYY-MM-DD", code=400)
                return response.to_dict(), response.code

        if data.get("end_date"):
            try:
                end_date = datetime.fromisoformat(data["end_date"]).date()
            except ValueError:
                response = ApiResponse.failure("Invalid end_date format. Use YYYY-MM-DD", code=400)
                return response.to_dict(), response.code

        # Create project instance
        project = Project(
            name=data.get("name", ""),
            description=data.get("description"),
            status=data.get("status", "active"),
            priority=data.get("priority", "medium"),
            start_date=start_date,
            end_date=end_date,
            created_by=user_id,
            user_id=data.get("user_id", user_id)  # Default to creator as owner
        )

        # Validate project
        is_valid, error_message = project.is_valid()
        if not is_valid:
            response = ApiResponse.failure(error_message, code=400)
            return response.to_dict(), response.code

        # Check if user_id exists if provided and different from creator
        if project.user_id != user_id:
            owner = User.query.get(project.user_id)
            if not owner:
                response = ApiResponse.failure("Invalid user_id: User not found", code=400)
                return response.to_dict(), response.code

        db.session.add(project)
        db.session.commit()

        # Return created project
        response = ApiResponse.success("Project created successfully", data={"project": project.to_dict()}, code=201)
        return response.to_dict(), response.code

    except Exception as e:
        db.session.rollback()
        response = ApiResponse.failure(f"Error creating project: {str(e)}", code=500)
        return response.to_dict(), response.code


def update_project(project_id: int, data: dict, user_id: int) -> Tuple[dict, int]:
    """Update an existing project"""
    try:
        project = Project.query.filter(
            Project.id == project_id,
            Project.deleted_at.is_(None),
            (Project.created_by == user_id) | (Project.user_id == user_id)
        ).first()

        if not project:
            response = ApiResponse.failure("Project not found", code=404)
            return response.to_dict(), response.code

        # Parse dates if provided
        start_date = project.start_date
        end_date = project.end_date

        if "start_date" in data:
            if data["start_date"]:
                try:
                    start_date = datetime.fromisoformat(data["start_date"]).date()
                except ValueError:
                    response = ApiResponse.failure("Invalid start_date format. Use YYYY-MM-DD", code=400)
                    return response.to_dict(), response.code
            else:
                start_date = None

        if "end_date" in data:
            if data["end_date"]:
                try:
                    end_date = datetime.fromisoformat(data["end_date"]).date()
                except ValueError:
                    response = ApiResponse.failure("Invalid end_date format. Use YYYY-MM-DD", code=400)
                    return response.to_dict(), response.code
            else:
                end_date = None

        # Update project fields
        new_user_id = data.get("user_id", project.user_id)

        # Check if user_id exists if changed
        if new_user_id != project.user_id:
            owner = User.query.get(new_user_id)
            if not owner:
                response = ApiResponse.failure("Invalid user_id: User not found", code=400)
                return response.to_dict(), response.code

        # Update model
        project.name = data.get("name", project.name)
        project.description = data.get("description", project.description)
        project.status = data.get("status", project.status)
        project.priority = data.get("priority", project.priority)
        project.start_date = start_date
        project.end_date = end_date
        project.user_id = new_user_id
        project.updated_at = datetime.utcnow()

        # Validate project
        is_valid, error_message = project.is_valid()
        if not is_valid:
            response = ApiResponse.failure(error_message, code=400)
            return response.to_dict(), response.code

        db.session.commit()

        # Return updated project
        response = ApiResponse.success("Project updated successfully", data={"project": project.to_dict()})
        return response.to_dict(), response.code

    except Exception as e:
        db.session.rollback()
        response = ApiResponse.failure(f"Error updating project: {str(e)}", code=500)
        return response.to_dict(), response.code

def delete_project(project_id: int, user_id: int, hard_delete: bool = False) -> Tuple[dict, int]:
    """Delete a project (soft delete by default)"""
    try:
        project = Project.query.filter(
            Project.id == project_id,
            Project.deleted_at.is_(None),
            Project.created_by == user_id  # Only creator can delete
        ).first()

        if not project:
            response = ApiResponse.failure("Project not found or you don't have permission to delete it", code=404)
            return response.to_dict(), response.code

        if hard_delete:
            # Hard delete - remove from database
            db.session.delete(project)
        else:
            # Soft delete - mark as deleted
            project.deleted_at = datetime.utcnow()

        db.session.commit()

        delete_type = "permanently deleted" if hard_delete else "deleted"
        response = ApiResponse.success(f"Project {delete_type} successfully")
        return response.to_dict(), response.code

    except Exception as e:
        db.session.rollback()
        response = ApiResponse.failure(f"Error deleting project: {str(e)}", code=500)
        return response.to_dict(), response.code

def restore_project(project_id: int, user_id: int) -> Tuple[dict, int]:
    """Restore a soft-deleted project"""
    try:
        project = Project.query.filter(
            Project.id == project_id,
            Project.deleted_at.is_not(None),
            Project.created_by == user_id
        ).first()

        if not project:
            response = ApiResponse.failure("Deleted project not found", code=404)
            return response.to_dict(), response.code

        project.deleted_at = None
        project.updated_at = datetime.utcnow()

        db.session.commit()

        response = ApiResponse.success("Project restored successfully", data={"project": project.to_dict()})
        return response.to_dict(), response.code

    except Exception as e:
        db.session.rollback()
        response = ApiResponse.failure(f"Error restoring project: {str(e)}", code=500)
        return response.to_dict(), response.code