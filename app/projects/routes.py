from flask import Blueprint, request, jsonify, g
from flask_jwt_extended import jwt_required, get_jwt_identity

from app.models.api_response import ApiResponse
from app.tasks.github_service import GitHubIssuesService
from app.projects.github_service import GitHubRepositoryService
from app.models.project import Project
from app.history import with_activity_log, activity_logger

from .services import (
    get_all_projects,
    get_project_by_id,
    create_project,
    update_project,
    delete_project,
    restore_project,
)

projects_bp = Blueprint("projects_bp", __name__, url_prefix="/projects")


@projects_bp.route("/", methods=["GET"])
@jwt_required()
def list_projects():
    """Get all projects for the current user with pagination"""
    current_user_id = int(get_jwt_identity())
    include_deleted = request.args.get("include_deleted", "false").lower() == "true"
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    # Limit per_page to prevent abuse
    per_page = min(per_page, 100)

    result, status = get_all_projects(current_user_id, include_deleted, page, per_page)
    return jsonify(result), status


@projects_bp.route("/<int:project_id>", methods=["GET"])
@jwt_required()
def get_project(project_id):
    """Get a specific project by ID"""
    current_user_id = int(get_jwt_identity())
    result, status = get_project_by_id(project_id, current_user_id)
    return jsonify(result), status


@projects_bp.route("/", methods=["POST"])
@jwt_required()
@with_activity_log(
    action="create_project",
    entity_type="project",
    description_template="Created project {name}"
)
def create_project_endpoint():
    """Create a new project"""
    current_user_id = int(get_jwt_identity())
    data = request.get_json()

    if not data:
        response = ApiResponse.failure("No data provided", code=400)
        return jsonify(response.to_dict()), response.code

    result, status = create_project(data, current_user_id)
    
    # If project creation was successful, set up activity logging data
    if status == 201 and isinstance(result, dict):
        # Extract project data from the ApiResponse structure
        project_data = result.get("data", {}).get("project", {})
        
        g.log_user_id = int(current_user_id)
        g.log_data = {
            "name": project_data.get("name", "Unknown"),
            "entity_id": project_data.get("id"),
            "project_id": project_data.get("id"),
            "description": project_data.get("description", "")
        }
        
    return jsonify(result), status


@projects_bp.route("/<int:project_id>", methods=["PUT"])
@jwt_required()
@with_activity_log(
    action="update_project",
    entity_type="project",
    description_template="Updated project {name}"
)
def update_project_endpoint(project_id):
    """Update an existing project"""
    current_user_id = int(get_jwt_identity())
    data = request.get_json()

    if not data:
        response = ApiResponse.failure("No data provided", code=400)
        return jsonify(response.to_dict()), response.code

    result, status = update_project(project_id, data, current_user_id)
    
    # If project update was successful, set up activity logging data
    if status == 200 and isinstance(result, dict):
        # Extract project data from the ApiResponse structure
        project_data = result.get("data", {}).get("project", {})
        
        g.log_user_id = int(current_user_id)
        g.log_data = {
            "name": project_data.get("name", "Unknown"),
            "entity_id": project_data.get("id", project_id),
            "project_id": project_id,
            "description": project_data.get("description", ""),
            "updated_fields": list(data.keys())
        }
        
    return jsonify(result), status


@projects_bp.route("/<int:project_id>", methods=["DELETE"])
@jwt_required()
def delete_project_endpoint(project_id):
    """Delete a project (soft delete by default)"""
    current_user_id = int(get_jwt_identity())
    hard_delete = request.args.get("hard_delete", "false").lower() == "true"
    
    # Get project data before deletion for logging
    project = Project.query.get(project_id)
    project_name = "Unknown"
    
    if project:
        project_name = project.name
    
    result, status = delete_project(project_id, current_user_id, hard_delete)
    
    # If deletion was successful, log the activity
    if status == 200:
        delete_type = "permanently deleted" if hard_delete else "deleted"
        activity_logger.log(
            user_id=int(current_user_id),
            action="delete_project" if not hard_delete else "hard_delete_project",
            entity_type="project",
            description=f"{delete_type.capitalize()} project {project_name}",
            entity_id=project_id,
            project_id=None,  # Don't reference the project if it's permanently deleted
            details={
                "name": project_name,
                "hard_delete": hard_delete
            }
        )
    
    return jsonify(result), status


@projects_bp.route("/<int:project_id>/restore", methods=["POST"])
@jwt_required()
def restore_project_endpoint(project_id):
    """Restore a soft-deleted project"""
    current_user_id = int(get_jwt_identity())
    result, status = restore_project(project_id, current_user_id)
    
    # If restore was successful, log the activity
    if status == 200:
        # Get project data after restoration for logging
        project = Project.query.get(project_id)
        project_name = "Unknown"
        
        if project:
            project_name = project.name
            
            activity_logger.log(
                user_id=int(current_user_id),
                action="restore_project",
                entity_type="project",
                description=f"Restored project {project_name}",
                entity_id=project_id,
                project_id=project_id,
                details={
                    "name": project_name
                }
            )
    
    return jsonify(result), status


@projects_bp.route("/github/status", methods=["GET"])
@jwt_required()
def get_github_status():
    """Get GitHub integration status for current user"""
    try:
        current_user_id = int(get_jwt_identity())

        # Check if user has active GitHub integration
        from app.models.integration import Integration
        integration = Integration.query.filter_by(
            user_id=current_user_id,
            platform="github",
            is_active=True
        ).first()

        data = {
            "connected": bool(integration),
            "platform": "github"
        }

        if integration:
            data["platform_user_id"] = integration.platform_user_id
            data["created_at"] = integration.created_at.isoformat() if integration.created_at else None
            data["updated_at"] = integration.updated_at.isoformat() if integration.updated_at else None

            # Try to get user info from GitHub API
            try:
                github_service = GitHubRepositoryService(current_user_id)
                # Get user info by making a simple API call
                import requests
                headers = {
                    "Authorization": f"Bearer {integration.access_token}",
                    "Accept": "application/vnd.github.v3+json",
                    "User-Agent": "TMS-Python-Backend",
                }
                response = requests.get("https://api.github.com/user", headers=headers, timeout=10)
                if response.status_code == 200:
                    user_info = response.json()
                    data["user"] = {
                        "login": user_info.get("login"),
                        "name": user_info.get("name"),
                        "avatar_url": user_info.get("avatar_url"),
                        "public_repos": user_info.get("public_repos", 0)
                    }
            except Exception as e:
                print(f"Warning: Could not fetch GitHub user info: {e}")

        response = ApiResponse.success(
            "GitHub integration status retrieved",
            data=data
        )
        return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(f"Error getting GitHub status: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@projects_bp.route("/github/repositories", methods=["GET"])
@jwt_required()
def fetch_github_repositories():
    """Fetch all GitHub repositories for the authenticated user"""
    try:
        current_user_id = int(get_jwt_identity())
        github_service = GitHubRepositoryService(current_user_id)

        response = github_service.get_all_user_repositories()
        return jsonify(response.to_dict()), response.code

    except ValueError as e:
        response = ApiResponse.failure(str(e), code=400)
        return jsonify(response.to_dict()), response.code
    except Exception as e:
        response = ApiResponse.failure(
            f"Error fetching repositories: {str(e)}", code=500
        )
        return jsonify(response.to_dict()), response.code


@projects_bp.route("/github/repositories/<path:repo_full_name>/issues", methods=["GET"])
@jwt_required()
def get_github_repository_issues(repo_full_name):
    """Get issues from a specific GitHub repository"""
    try:
        current_user_id = int(get_jwt_identity())

        # Parse owner/repo from full name
        if '/' not in repo_full_name:
            response = ApiResponse.failure("Invalid repository format. Expected: owner/repo", code=400)
            return jsonify(response.to_dict()), response.code

        owner, repo = repo_full_name.split('/', 1)

        # Import GitHub issues service
        from app.tasks.github_service import GitHubIssuesService
        github_service = GitHubIssuesService(current_user_id)

        # Get query parameters
        state = request.args.get('state', 'open')
        per_page = min(int(request.args.get('per_page', 30)), 100)
        page = int(request.args.get('page', 1))

        response = github_service.get_repository_issues(
            owner=owner,
            repo=repo,
            state=state,
            per_page=per_page,
            page=page
        )

        return jsonify(response.to_dict()), response.code

    except ValueError as e:
        response = ApiResponse.failure(str(e), code=400)
        return jsonify(response.to_dict()), response.code
    except Exception as e:
        response = ApiResponse.failure(f"Error fetching repository issues: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@projects_bp.route("/github/repositories/<path:repo_full_name>/issues/<int:issue_number>/sync", methods=["POST"])
@jwt_required()
def sync_github_issue_to_task(repo_full_name, issue_number):
    """Sync a specific GitHub issue to a local task"""
    try:
        current_user_id = int(get_jwt_identity())

        # Parse owner/repo from full name
        if '/' not in repo_full_name:
            response = ApiResponse.failure("Invalid repository format. Expected: owner/repo", code=400)
            return jsonify(response.to_dict()), response.code

        owner, repo = repo_full_name.split('/', 1)

        # Import GitHub issues service
        from app.tasks.github_service import GitHubIssuesService
        github_service = GitHubIssuesService(current_user_id)

        # Get the specific issue first
        issue_response = github_service.get_repository_issues(
            owner=owner,
            repo=repo,
            state='all',
            per_page=100,
            page=1
        )

        if not issue_response.success:
            return jsonify(issue_response.to_dict()), issue_response.code

        # Find the specific issue
        target_issue = None
        for issue in issue_response.data.get('issues', []):
            if issue.get('number') == issue_number:
                target_issue = issue
                break

        if not target_issue:
            response = ApiResponse.failure(f"Issue #{issue_number} not found", code=404)
            return jsonify(response.to_dict()), response.code

        # Create task from issue
        from app.models import Task, Project
        from app import db

        # Try to find existing project for this repository
        repo_url = f"https://github.com/{owner}/{repo}"
        project = Project.query.filter_by(
            user_id=current_user_id,
            github_repo_url=repo_url
        ).first()

        if not project:
            # Create a default project for this repository
            project = Project(
                name=f"{owner}/{repo}",
                description=f"GitHub repository: {repo_full_name}",
                user_id=current_user_id,
                github_repo_url=repo_url
            )
            db.session.add(project)
            db.session.flush()

        # Check if task already exists
        existing_task = Task.query.filter_by(
            project_id=project.id,
            github_issue_number=issue_number
        ).first()

        if existing_task:
            response = ApiResponse.success(
                f"Task for issue #{issue_number} already exists",
                data={"task_id": existing_task.id}
            )
            return jsonify(response.to_dict()), response.code

        # Create new task
        task = Task(
            title=target_issue.get('title', f"GitHub Issue #{issue_number}"),
            description=target_issue.get('body', ''),
            status='todo',
            priority='medium',
            project_id=project.id,
            user_id=current_user_id,
            github_issue_number=issue_number,
            github_issue_url=target_issue.get('html_url')
        )

        db.session.add(task)
        db.session.commit()

        response = ApiResponse.success(
            f"GitHub issue #{issue_number} synced successfully",
            data={"task_id": task.id}
        )
        return jsonify(response.to_dict()), response.code

    except ValueError as e:
        response = ApiResponse.failure(str(e), code=400)
        return jsonify(response.to_dict()), response.code
    except Exception as e:
        response = ApiResponse.failure(f"Error syncing issue: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@projects_bp.route("/github/sync", methods=["POST"])
@jwt_required()
def sync_github_repositories():
    """Sync selected GitHub repositories to local projects"""
    try:
        current_user_id = int(get_jwt_identity())
        data = request.get_json()

        if not data or not data.get("repository_urls"):
            response = ApiResponse.failure("repository_urls is required", code=400)
            return jsonify(response.to_dict()), response.code

        repository_urls = data.get("repository_urls", [])
        if not isinstance(repository_urls, list):
            response = ApiResponse.failure("repository_urls must be a list", code=400)
            return jsonify(response.to_dict()), response.code

        github_service = GitHubRepositoryService(current_user_id)
        response = github_service.sync_repositories_to_projects(repository_urls)

        return jsonify(response.to_dict()), response.code

    except ValueError as e:
        response = ApiResponse.failure(str(e), code=400)
        return jsonify(response.to_dict()), response.code
    except Exception as e:
        response = ApiResponse.failure(
            f"Error syncing repositories: {str(e)}", code=500
        )
        return jsonify(response.to_dict()), response.code


@projects_bp.route("/<int:project_id>/github/sync-complete", methods=["POST"])
@jwt_required()
def sync_project_complete(project_id):  # Add project_id parameter here
    """Complete sync: Update project info and sync all issues"""
    try:
        current_user_id = int(get_jwt_identity())

        project = Project.query.filter_by(
            id=project_id,
            user_id=current_user_id,
            deleted_at=None,
            is_github_synced=True,
        ).first()

        if not project:
            response = ApiResponse.failure(
                "Project not found or not synced from GitHub", code=404
            )
            return jsonify(response.to_dict()), response.code

        github_repo_service = GitHubRepositoryService(current_user_id)

        # Update project info first
        if project.github_repo_url:
            project_sync_response = github_repo_service.sync_repositories_to_projects(
                [project.github_repo_url]
            )
            if not project_sync_response.success:
                return (
                    jsonify(project_sync_response.to_dict()),
                    project_sync_response.code,
                )

        github_issues_service = GitHubIssuesService(current_user_id)
        issues_sync_response = github_issues_service.sync_repository_issues_to_tasks(
            project_id
        )

        if issues_sync_response.success:
            # Combine results
            combined_data = {
                "project_updated": True,
                "synced_tasks": issues_sync_response.data.get("synced_tasks", []),
                "total_synced": issues_sync_response.data.get("total_synced", 0),
                "total_issues": issues_sync_response.data.get("total_issues", 0),
                "errors": issues_sync_response.data.get("errors", []),
                "project": issues_sync_response.data.get("project", {}),
            }

            response = ApiResponse.success(
                "Project synced completely", data=combined_data
            )
        else:
            response = issues_sync_response

        return jsonify(response.to_dict()), response.code

    except ValueError as e:
        response = ApiResponse.failure(str(e), code=400)
        return jsonify(response.to_dict()), response.code
    except Exception as e:
        response = ApiResponse.failure(f"Error syncing project: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code
