{% extends "dashboard/base.html" %}

{% block page_title %}Webhooks Manager{% endblock %}
{% block page_description %}Manage webhooks, test endpoints, and monitor webhook events{% endblock %}

{% block content %}
<!-- Webhook Testing -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-flask me-2"></i>Webhook Testing</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Test GitHub Webhook</h6>
                        <div class="mb-3">
                            <label class="form-label">User ID</label>
                            <input type="number" class="form-control" id="githubUserId" placeholder="1" value="1">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Event Type</label>
                            <select class="form-select" id="githubEventType">
                                <option value="issues">Issues</option>
                                <option value="push">Push</option>
                                <option value="pull_request">Pull Request</option>
                            </select>
                        </div>
                        <button class="btn btn-dark" onclick="testGitHubWebhook()">
                            <i class="fab fa-github me-2"></i>Test GitHub Webhook
                        </button>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>Test Jira Webhook</h6>
                        <div class="mb-3">
                            <label class="form-label">User ID</label>
                            <input type="number" class="form-control" id="jiraUserId" placeholder="1" value="1">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Event Type</label>
                            <select class="form-select" id="jiraEventType">
                                <option value="jira:issue_created">Issue Created</option>
                                <option value="jira:issue_updated">Issue Updated</option>
                                <option value="jira:issue_deleted">Issue Deleted</option>
                            </select>
                        </div>
                        <button class="btn btn-primary" onclick="testJiraWebhook()">
                            <i class="fab fa-atlassian me-2"></i>Test Jira Webhook
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Webhook URLs -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-link me-2"></i>Webhook URLs</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>GitHub Webhook URL</h6>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" id="githubWebhookUrl" readonly>
                            <button class="btn btn-outline-secondary" onclick="copyWebhookUrl('github')">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                        </div>
                        <small class="text-muted">Use this URL in your GitHub repository webhook settings</small>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>Jira Webhook URL</h6>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" id="jiraWebhookUrl" readonly>
                            <button class="btn btn-outline-secondary" onclick="copyWebhookUrl('jira')">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                        </div>
                        <small class="text-muted">Use this URL in your Jira webhook settings</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Webhook Events Log -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Webhook Events</h5>
                    <div>
                        <button class="btn btn-outline-primary" onclick="loadWebhookEvents()">
                            <i class="fas fa-sync me-2"></i>Refresh
                        </button>
                        <button class="btn btn-outline-danger" onclick="clearWebhookEvents()">
                            <i class="fas fa-trash me-2"></i>Clear
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div id="webhookEvents">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading webhook events...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Webhook Configuration -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fab fa-github me-2"></i>GitHub Webhook Setup</h5>
            </div>
            <div class="card-body">
                <h6>Repository Webhook Configuration</h6>
                <ol>
                    <li>Go to your GitHub repository</li>
                    <li>Navigate to <strong>Settings</strong> → <strong>Webhooks</strong></li>
                    <li>Click <strong>"Add webhook"</strong></li>
                    <li>Set <strong>Payload URL</strong> to the GitHub webhook URL above</li>
                    <li>Set <strong>Content type</strong> to <code>application/json</code></li>
                    <li>Select events: <strong>Issues</strong>, <strong>Push</strong>, <strong>Pull requests</strong></li>
                    <li>Click <strong>"Add webhook"</strong></li>
                </ol>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Note:</strong> Make sure to replace <code>USER_ID</code> in the webhook URL with the actual user ID.
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fab fa-atlassian me-2"></i>Jira Webhook Setup</h5>
            </div>
            <div class="card-body">
                <h6>Jira Webhook Configuration</h6>
                <ol>
                    <li>Go to your Jira instance</li>
                    <li>Navigate to <strong>Settings</strong> → <strong>System</strong> → <strong>Webhooks</strong></li>
                    <li>Click <strong>"Create a webhook"</strong></li>
                    <li>Set <strong>Name</strong> to <code>TMS Integration</code></li>
                    <li>Set <strong>URL</strong> to the Jira webhook URL above</li>
                    <li>Select events: <strong>Issue Created</strong>, <strong>Issue Updated</strong>, <strong>Issue Deleted</strong></li>
                    <li>Click <strong>"Create"</strong></li>
                </ol>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Note:</strong> Make sure to replace <code>USER_ID</code> in the webhook URL with the actual user ID.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Webhook Statistics -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Webhook Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h4 class="text-primary" id="totalWebhooks">-</h4>
                            <p class="mb-0">Total Events</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h4 class="text-dark" id="githubWebhooks">-</h4>
                            <p class="mb-0">GitHub Events</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h4 class="text-primary" id="jiraWebhooks">-</h4>
                            <p class="mb-0">Jira Events</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h4 class="text-success" id="successfulWebhooks">-</h4>
                            <p class="mb-0">Successful</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Results Modal -->
<div class="modal fade" id="testResultModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testResultModalTitle">Webhook Test Result</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="testResultModalBody">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let webhookEvents = [];
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        // Set webhook URLs
        const baseUrl = window.location.origin;
        document.getElementById('githubWebhookUrl').value = `${baseUrl}/projects/github/webhook?user_id=USER_ID`;
        document.getElementById('jiraWebhookUrl').value = `${baseUrl}/webhooks/jira/webhook?user_id=USER_ID`;
        
        loadWebhookEvents();
        loadWebhookStats();
    });
    
    // Test GitHub webhook
    async function testGitHubWebhook() {
        const userId = document.getElementById('githubUserId').value;
        const eventType = document.getElementById('githubEventType').value;
        
        if (!userId) {
            showAlert('Please enter a user ID', 'warning');
            return;
        }
        
        const testPayloads = {
            issues: {
                action: 'opened',
                issue: {
                    id: 123456789,
                    number: 1,
                    title: 'Test Issue from Webhook',
                    body: 'This is a test issue created via webhook testing',
                    state: 'open',
                    user: {
                        login: 'testuser',
                        id: 12345
                    },
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                },
                repository: {
                    id: 987654321,
                    name: 'test-repo',
                    full_name: 'testuser/test-repo',
                    private: false
                }
            },
            push: {
                ref: 'refs/heads/main',
                commits: [
                    {
                        id: 'abc123def456',
                        message: 'Test commit from webhook',
                        author: {
                            name: 'Test User',
                            email: '<EMAIL>'
                        },
                        timestamp: new Date().toISOString()
                    }
                ],
                repository: {
                    name: 'test-repo',
                    full_name: 'testuser/test-repo'
                }
            },
            pull_request: {
                action: 'opened',
                pull_request: {
                    id: 123456789,
                    number: 1,
                    title: 'Test Pull Request',
                    body: 'This is a test pull request',
                    state: 'open',
                    user: {
                        login: 'testuser'
                    }
                },
                repository: {
                    name: 'test-repo',
                    full_name: 'testuser/test-repo'
                }
            }
        };
        
        try {
            showAlert('Testing GitHub webhook...', 'info');
            
            const response = await axios.post(`/projects/github/webhook?user_id=${userId}`, testPayloads[eventType], {
                headers: {
                    'Content-Type': 'application/json',
                    'X-GitHub-Event': eventType
                }
            });
            
            showTestResult('GitHub Webhook Test', response.data, true);
            loadWebhookEvents();
            loadWebhookStats();
            
        } catch (error) {
            showTestResult('GitHub Webhook Test', error.response?.data || { error: error.message }, false);
        }
    }
    
    // Test Jira webhook
    async function testJiraWebhook() {
        const userId = document.getElementById('jiraUserId').value;
        const eventType = document.getElementById('jiraEventType').value;
        
        if (!userId) {
            showAlert('Please enter a user ID', 'warning');
            return;
        }
        
        const testPayloads = {
            'jira:issue_created': {
                webhookEvent: 'jira:issue_created',
                issue_event_type_name: 'issue_created',
                user: {
                    accountId: 'test-account-id',
                    displayName: 'Test User'
                },
                issue: {
                    id: '10001',
                    key: 'TEST-1',
                    fields: {
                        summary: 'Test Issue from Webhook',
                        description: 'This is a test issue created via webhook testing',
                        status: {
                            name: 'To Do'
                        },
                        priority: {
                            name: 'Medium'
                        },
                        project: {
                            key: 'TEST',
                            name: 'Test Project'
                        },
                        issuetype: {
                            name: 'Task'
                        },
                        created: new Date().toISOString(),
                        updated: new Date().toISOString()
                    }
                }
            },
            'jira:issue_updated': {
                webhookEvent: 'jira:issue_updated',
                issue_event_type_name: 'issue_updated',
                user: {
                    accountId: 'test-account-id',
                    displayName: 'Test User'
                },
                issue: {
                    id: '10001',
                    key: 'TEST-1',
                    fields: {
                        summary: 'Updated Test Issue',
                        description: 'This issue has been updated via webhook testing',
                        status: {
                            name: 'In Progress'
                        },
                        priority: {
                            name: 'High'
                        },
                        updated: new Date().toISOString()
                    }
                },
                changelog: {
                    items: [
                        {
                            field: 'status',
                            fromString: 'To Do',
                            toString: 'In Progress'
                        }
                    ]
                }
            },
            'jira:issue_deleted': {
                webhookEvent: 'jira:issue_deleted',
                issue_event_type_name: 'issue_deleted',
                user: {
                    accountId: 'test-account-id',
                    displayName: 'Test User'
                },
                issue: {
                    id: '10001',
                    key: 'TEST-1',
                    fields: {
                        summary: 'Deleted Test Issue'
                    }
                }
            }
        };
        
        try {
            showAlert('Testing Jira webhook...', 'info');
            
            const response = await axios.post(`/webhooks/jira/webhook?user_id=${userId}`, testPayloads[eventType], {
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            showTestResult('Jira Webhook Test', response.data, true);
            loadWebhookEvents();
            loadWebhookStats();
            
        } catch (error) {
            showTestResult('Jira Webhook Test', error.response?.data || { error: error.message }, false);
        }
    }
    
    // Show test result in modal
    function showTestResult(title, data, success) {
        document.getElementById('testResultModalTitle').textContent = title;
        document.getElementById('testResultModalBody').innerHTML = `
            <div class="alert alert-${success ? 'success' : 'danger'}">
                <h6><i class="fas fa-${success ? 'check-circle' : 'times-circle'} me-2"></i>${success ? 'Success' : 'Error'}</h6>
            </div>
            <div class="json-viewer">
                <pre><code class="language-json">${JSON.stringify(data, null, 2)}</code></pre>
            </div>
        `;
        
        const modal = new bootstrap.Modal(document.getElementById('testResultModal'));
        modal.show();
        
        // Highlight code
        if (window.Prism) {
            Prism.highlightAll();
        }
        
        showAlert(`${title} ${success ? 'completed successfully' : 'failed'}`, success ? 'success' : 'danger');
    }
    
    // Copy webhook URL
    function copyWebhookUrl(platform) {
        const inputId = platform === 'github' ? 'githubWebhookUrl' : 'jiraWebhookUrl';
        const input = document.getElementById(inputId);
        
        navigator.clipboard.writeText(input.value).then(() => {
            showAlert(`${platform.charAt(0).toUpperCase() + platform.slice(1)} webhook URL copied to clipboard!`, 'success');
        });
    }
    
    // Load webhook events (simulated)
    function loadWebhookEvents() {
        // Since we don't have a real webhook events endpoint, we'll simulate this
        setTimeout(() => {
            const simulatedEvents = [
                {
                    id: 1,
                    platform: 'github',
                    event_type: 'issues',
                    status: 'success',
                    timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
                    payload_summary: 'Issue #123 opened in test-repo'
                },
                {
                    id: 2,
                    platform: 'jira',
                    event_type: 'jira:issue_created',
                    status: 'success',
                    timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
                    payload_summary: 'Issue TEST-1 created'
                },
                {
                    id: 3,
                    platform: 'github',
                    event_type: 'push',
                    status: 'error',
                    timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
                    payload_summary: 'Push to main branch failed'
                }
            ];
            
            displayWebhookEvents(simulatedEvents);
        }, 1000);
    }
    
    // Display webhook events
    function displayWebhookEvents(events) {
        if (events.length === 0) {
            document.getElementById('webhookEvents').innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-webhook me-2"></i>No webhook events found
                </div>
            `;
            return;
        }
        
        const eventsHtml = events.map(event => `
            <div class="card mb-2">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center mb-1">
                                <i class="fab fa-${event.platform} me-2"></i>
                                <strong>${event.platform.charAt(0).toUpperCase() + event.platform.slice(1)}</strong>
                                <span class="badge bg-${event.status === 'success' ? 'success' : 'danger'} ms-2">${event.status}</span>
                            </div>
                            <p class="mb-1">${event.payload_summary}</p>
                            <small class="text-muted">Event: ${event.event_type} | ${new Date(event.timestamp).toLocaleString()}</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <button class="btn btn-sm btn-outline-primary" onclick="viewEventDetails(${event.id})">
                                <i class="fas fa-eye"></i> Details
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
        
        document.getElementById('webhookEvents').innerHTML = eventsHtml;
    }
    
    // View event details
    function viewEventDetails(eventId) {
        showAlert('Event details would be displayed here in a real implementation', 'info');
    }
    
    // Clear webhook events
    function clearWebhookEvents() {
        if (confirm('Are you sure you want to clear all webhook events?')) {
            document.getElementById('webhookEvents').innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-webhook me-2"></i>No webhook events found
                </div>
            `;
            showAlert('Webhook events cleared', 'success');
        }
    }
    
    // Load webhook statistics (simulated)
    function loadWebhookStats() {
        // Simulate loading stats
        setTimeout(() => {
            document.getElementById('totalWebhooks').textContent = '156';
            document.getElementById('githubWebhooks').textContent = '89';
            document.getElementById('jiraWebhooks').textContent = '67';
            document.getElementById('successfulWebhooks').textContent = '142';
        }, 500);
    }
</script>
{% endblock %}
