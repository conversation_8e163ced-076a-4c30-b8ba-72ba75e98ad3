{% extends "dashboard/base.html" %}

{% block page_title %}Error{% endblock %}
{% block page_description %}An error occurred{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                <h4>Oops! Something went wrong</h4>
                <p class="text-muted">{{ error }}</p>
                <div class="mt-4">
                    <a href="{{ url_for('dashboard.index') }}" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>Back to Dashboard
                    </a>
                    <button class="btn btn-outline-secondary" onclick="window.location.reload()">
                        <i class="fas fa-sync me-2"></i>Retry
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
