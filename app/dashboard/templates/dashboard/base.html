<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}TMS API Dashboard{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Prism.js for code highlighting -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 8px;
            margin: 2px 0;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }
        .main-content {
            background: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .method-badge {
            font-size: 0.75rem;
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
        }
        .method-get { background: #28a745; color: white; }
        .method-post { background: #007bff; color: white; }
        .method-put { background: #ffc107; color: black; }
        .method-delete { background: #dc3545; color: white; }
        .method-patch { background: #6f42c1; color: white; }
        
        .endpoint-card {
            border-left: 4px solid #007bff;
            margin-bottom: 1rem;
        }
        .endpoint-card.get { border-left-color: #28a745; }
        .endpoint-card.post { border-left-color: #007bff; }
        .endpoint-card.put { border-left-color: #ffc107; }
        .endpoint-card.delete { border-left-color: #dc3545; }
        .endpoint-card.patch { border-left-color: #6f42c1; }
        
        .json-viewer {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .loading {
            display: none;
        }
        .loading.show {
            display: inline-block;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="d-flex align-items-center mb-4">
                    <i class="fas fa-code text-white me-2"></i>
                    <h5 class="text-white mb-0">TMS API</h5>
                </div>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="{{ url_for('dashboard.index') }}">
                        <i class="fas fa-home me-2"></i> Dashboard
                    </a>
                    <a class="nav-link" href="{{ url_for('dashboard.api_explorer') }}">
                        <i class="fas fa-search me-2"></i> API Explorer
                    </a>
                    <a class="nav-link" href="{{ url_for('dashboard.api_tester') }}">
                        <i class="fas fa-flask me-2"></i> API Tester
                    </a>
                    
                    <hr class="text-white-50">
                    
                    <a class="nav-link" href="{{ url_for('dashboard.auth_manager') }}">
                        <i class="fas fa-key me-2"></i> Authentication
                    </a>
                    <a class="nav-link" href="{{ url_for('dashboard.projects_manager') }}">
                        <i class="fas fa-folder me-2"></i> Projects
                    </a>
                    <a class="nav-link" href="{{ url_for('dashboard.tasks_manager') }}">
                        <i class="fas fa-tasks me-2"></i> Tasks
                    </a>
                    
                    <hr class="text-white-50">
                    
                    <a class="nav-link" href="{{ url_for('dashboard.github_integration') }}">
                        <i class="fab fa-github me-2"></i> GitHub
                    </a>
                    <a class="nav-link" href="{{ url_for('dashboard.jira_integration') }}">
                        <i class="fab fa-atlassian me-2"></i> Jira
                    </a>
                    <a class="nav-link" href="{{ url_for('dashboard.webhooks_manager') }}">
                        <i class="fas fa-webhook me-2"></i> Webhooks
                    </a>
                    
                    <hr class="text-white-50">
                    
                    <a class="nav-link" href="/apidocs/" target="_blank">
                        <i class="fas fa-book me-2"></i> Swagger Docs
                    </a>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content p-4">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2>{% block page_title %}Dashboard{% endblock %}</h2>
                        <p class="text-muted mb-0">{% block page_description %}TMS API Management Interface{% endblock %}</p>
                    </div>
                    <div>
                        <button class="btn btn-outline-primary" id="refreshBtn">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
                
                <!-- Alerts -->
                <div id="alertContainer"></div>
                
                <!-- Content -->
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Axios for API calls -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <!-- Prism.js for code highlighting -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <script>
        // Global utilities
        const API_BASE = window.location.origin;
        
        // Show alert
        function showAlert(message, type = 'info') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            document.getElementById('alertContainer').innerHTML = alertHtml;
        }
        
        // Format JSON
        function formatJSON(obj) {
            return JSON.stringify(obj, null, 2);
        }
        
        // Copy to clipboard
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showAlert('Copied to clipboard!', 'success');
            });
        }

        // JWT Token utilities
        function getAuthToken() {
            return localStorage.getItem('authToken');
        }

        function setAuthToken(token) {
            if (token) {
                localStorage.setItem('authToken', token);
                console.log('JWT token saved to localStorage');
            }
        }

        function removeAuthToken() {
            localStorage.removeItem('authToken');
            console.log('JWT token removed from localStorage');
        }

        function isAuthenticated() {
            const token = getAuthToken();
            return token && token.length > 0;
        }

        // Create axios config with auth header
        function getAuthConfig() {
            const token = getAuthToken();
            if (!token) {
                throw new Error('No authentication token found');
            }

            return {
                headers: { 'Authorization': `Bearer ${token}` }
            };
        }
        
        // Set active nav link
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar .nav-link');
            
            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
