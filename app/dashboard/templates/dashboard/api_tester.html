{% extends "dashboard/base.html" %}

{% block page_title %}API Tester{% endblock %}
{% block page_description %}Interactive API testing interface{% endblock %}

{% block content %}
<div class="row">
    <!-- Request Panel -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-paper-plane me-2"></i>Request</h5>
            </div>
            <div class="card-body">
                <!-- Method and URL -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <select class="form-select" id="methodSelect">
                            <option value="GET">GET</option>
                            <option value="POST">POST</option>
                            <option value="PUT">PUT</option>
                            <option value="DELETE">DELETE</option>
                            <option value="PATCH">PATCH</option>
                        </select>
                    </div>
                    <div class="col-md-9">
                        <div class="input-group">
                            <span class="input-group-text" id="baseUrl">{{ base_url }}</span>
                            <input type="text" class="form-control" id="endpointInput" placeholder="/api/endpoint">
                        </div>
                    </div>
                </div>
                
                <!-- Headers -->
                <div class="mb-3">
                    <label class="form-label">Headers</label>
                    <div id="headersContainer">
                        <div class="row mb-2">
                            <div class="col-5">
                                <input type="text" class="form-control" placeholder="Header name" value="Authorization">
                            </div>
                            <div class="col-6">
                                <input type="text" class="form-control" placeholder="Header value" id="authHeader">
                            </div>
                            <div class="col-1">
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeHeader(this)">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="addHeader()">
                        <i class="fas fa-plus"></i> Add Header
                    </button>
                </div>
                
                <!-- Request Body -->
                <div class="mb-3" id="requestBodySection">
                    <label class="form-label">Request Body (JSON)</label>
                    <textarea class="form-control" id="requestBody" rows="8" placeholder='{"key": "value"}'></textarea>
                    <div class="form-text">
                        <button type="button" class="btn btn-sm btn-outline-info" onclick="formatJSON()">
                            <i class="fas fa-code"></i> Format JSON
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearBody()">
                            <i class="fas fa-trash"></i> Clear
                        </button>
                    </div>
                </div>
                
                <!-- Send Button -->
                <div class="d-grid">
                    <button type="button" class="btn btn-primary" id="sendButton" onclick="sendRequest()">
                        <i class="fas fa-paper-plane me-2"></i>Send Request
                        <span class="spinner-border spinner-border-sm ms-2 loading" role="status"></span>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 mb-2">
                        <button class="btn btn-sm btn-outline-primary w-100" onclick="loadExample('auth')">
                            <i class="fas fa-key me-1"></i>Auth Login
                        </button>
                    </div>
                    <div class="col-6 mb-2">
                        <button class="btn btn-sm btn-outline-success w-100" onclick="loadExample('projects')">
                            <i class="fas fa-folder me-1"></i>Get Projects
                        </button>
                    </div>
                    <div class="col-6 mb-2">
                        <button class="btn btn-sm btn-outline-info w-100" onclick="loadExample('github')">
                            <i class="fab fa-github me-1"></i>GitHub Status
                        </button>
                    </div>
                    <div class="col-6 mb-2">
                        <button class="btn btn-sm btn-outline-warning w-100" onclick="loadExample('jira')">
                            <i class="fab fa-atlassian me-1"></i>Jira Status
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Response Panel -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-reply me-2"></i>Response</h5>
                    <div>
                        <span id="responseStatus" class="badge bg-secondary">Ready</span>
                        <span id="responseTime" class="badge bg-info ms-1" style="display: none;"></span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Response Headers -->
                <div class="mb-3">
                    <label class="form-label">Response Headers</label>
                    <div class="json-viewer" id="responseHeaders" style="max-height: 150px;">
                        <em class="text-muted">No response yet</em>
                    </div>
                </div>
                
                <!-- Response Body -->
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <label class="form-label mb-0">Response Body</label>
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="copyResponse()" id="copyBtn" style="display: none;">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                        </div>
                    </div>
                    <div class="json-viewer" id="responseBody">
                        <em class="text-muted">No response yet</em>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Request History -->
        <div class="card mt-3">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0"><i class="fas fa-history me-2"></i>Request History</h6>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearHistory()">
                        <i class="fas fa-trash"></i> Clear
                    </button>
                </div>
            </div>
            <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                <div id="requestHistory">
                    <em class="text-muted">No requests yet</em>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let requestHistory = [];
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        // Load from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('method')) {
            document.getElementById('methodSelect').value = urlParams.get('method');
        }
        if (urlParams.get('path')) {
            document.getElementById('endpointInput').value = urlParams.get('path');
        }
        
        // Show/hide request body based on method
        updateRequestBodyVisibility();
        
        // Load saved auth token
        const savedToken = localStorage.getItem('tms_auth_token');
        if (savedToken) {
            document.getElementById('authHeader').value = `Bearer ${savedToken}`;
        }
    });
    
    // Method change handler
    document.getElementById('methodSelect').addEventListener('change', updateRequestBodyVisibility);
    
    function updateRequestBodyVisibility() {
        const method = document.getElementById('methodSelect').value;
        const bodySection = document.getElementById('requestBodySection');
        
        if (['POST', 'PUT', 'PATCH'].includes(method)) {
            bodySection.style.display = 'block';
        } else {
            bodySection.style.display = 'none';
        }
    }
    
    // Add header
    function addHeader() {
        const container = document.getElementById('headersContainer');
        const headerRow = document.createElement('div');
        headerRow.className = 'row mb-2';
        headerRow.innerHTML = `
            <div class="col-5">
                <input type="text" class="form-control" placeholder="Header name">
            </div>
            <div class="col-6">
                <input type="text" class="form-control" placeholder="Header value">
            </div>
            <div class="col-1">
                <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeHeader(this)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        container.appendChild(headerRow);
    }
    
    // Remove header
    function removeHeader(button) {
        button.closest('.row').remove();
    }
    
    // Format JSON
    function formatJSON() {
        const textarea = document.getElementById('requestBody');
        try {
            const parsed = JSON.parse(textarea.value);
            textarea.value = JSON.stringify(parsed, null, 2);
        } catch (e) {
            showAlert('Invalid JSON format', 'warning');
        }
    }
    
    // Clear body
    function clearBody() {
        document.getElementById('requestBody').value = '';
    }
    
    // Send request
    async function sendRequest() {
        const method = document.getElementById('methodSelect').value;
        const endpoint = document.getElementById('endpointInput').value;
        const requestBody = document.getElementById('requestBody').value;
        
        if (!endpoint) {
            showAlert('Please enter an endpoint', 'warning');
            return;
        }
        
        // Show loading
        const sendButton = document.getElementById('sendButton');
        const loading = sendButton.querySelector('.loading');
        sendButton.disabled = true;
        loading.classList.add('show');
        
        // Update status
        document.getElementById('responseStatus').textContent = 'Sending...';
        document.getElementById('responseStatus').className = 'badge bg-warning';
        
        const startTime = Date.now();
        
        try {
            // Collect headers
            const headers = {};
            const headerRows = document.querySelectorAll('#headersContainer .row');
            headerRows.forEach(row => {
                const nameInput = row.querySelector('.col-5 input');
                const valueInput = row.querySelector('.col-6 input');
                if (nameInput.value && valueInput.value) {
                    headers[nameInput.value] = valueInput.value;
                }
            });
            
            // Prepare request data
            const requestData = {
                method: method,
                headers: headers
            };
            
            if (['POST', 'PUT', 'PATCH'].includes(method) && requestBody) {
                try {
                    requestData.data = JSON.parse(requestBody);
                } catch (e) {
                    throw new Error('Invalid JSON in request body');
                }
            }
            
            // Make request through proxy
            const response = await axios({
                method: 'POST',
                url: '/dashboard/api/proxy',
                params: { endpoint: endpoint },
                headers: headers,
                data: requestData.data
            });
            
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            
            // Update response display
            displayResponse(response.data, responseTime);
            
            // Add to history
            addToHistory(method, endpoint, response.data.status_code, responseTime);
            
        } catch (error) {
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            
            console.error('Request failed:', error);
            
            let errorResponse = {
                status_code: error.response?.status || 0,
                data: error.response?.data || { error: error.message },
                headers: error.response?.headers || {}
            };
            
            displayResponse(errorResponse, responseTime, true);
            addToHistory(method, endpoint, errorResponse.status_code, responseTime, true);
        } finally {
            // Hide loading
            sendButton.disabled = false;
            loading.classList.remove('show');
        }
    }
    
    // Display response
    function displayResponse(response, responseTime, isError = false) {
        // Status
        const statusBadge = document.getElementById('responseStatus');
        const timeBadge = document.getElementById('responseTime');
        
        statusBadge.textContent = response.status_code;
        timeBadge.textContent = `${responseTime}ms`;
        timeBadge.style.display = 'inline';
        
        if (response.status_code >= 200 && response.status_code < 300) {
            statusBadge.className = 'badge bg-success';
        } else if (response.status_code >= 400) {
            statusBadge.className = 'badge bg-danger';
        } else {
            statusBadge.className = 'badge bg-warning';
        }
        
        // Headers
        document.getElementById('responseHeaders').innerHTML = 
            `<pre><code class="language-json">${JSON.stringify(response.headers || {}, null, 2)}</code></pre>`;
        
        // Body
        document.getElementById('responseBody').innerHTML = 
            `<pre><code class="language-json">${JSON.stringify(response.data, null, 2)}</code></pre>`;
        
        // Show copy button
        document.getElementById('copyBtn').style.display = 'inline-block';
        
        // Highlight code
        if (window.Prism) {
            Prism.highlightAll();
        }
    }
    
    // Copy response
    function copyResponse() {
        const responseBody = document.querySelector('#responseBody pre code').textContent;
        copyToClipboard(responseBody);
    }
    
    // Add to history
    function addToHistory(method, endpoint, statusCode, responseTime, isError = false) {
        const historyItem = {
            method,
            endpoint,
            statusCode,
            responseTime,
            timestamp: new Date(),
            isError
        };
        
        requestHistory.unshift(historyItem);
        if (requestHistory.length > 10) {
            requestHistory = requestHistory.slice(0, 10);
        }
        
        updateHistoryDisplay();
    }
    
    // Update history display
    function updateHistoryDisplay() {
        const container = document.getElementById('requestHistory');
        
        if (requestHistory.length === 0) {
            container.innerHTML = '<em class="text-muted">No requests yet</em>';
            return;
        }
        
        container.innerHTML = requestHistory.map(item => `
            <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                <div>
                    <span class="method-badge method-${item.method.toLowerCase()} me-2">${item.method}</span>
                    <code class="small">${item.endpoint}</code>
                </div>
                <div class="text-end">
                    <span class="badge bg-${item.statusCode >= 200 && item.statusCode < 300 ? 'success' : 'danger'}">${item.statusCode}</span>
                    <small class="text-muted d-block">${item.responseTime}ms</small>
                </div>
            </div>
        `).join('');
    }
    
    // Clear history
    function clearHistory() {
        requestHistory = [];
        updateHistoryDisplay();
    }
    
    // Load examples
    function loadExample(type) {
        const examples = {
            auth: {
                method: 'POST',
                endpoint: '/auth/login',
                body: {
                    email: '<EMAIL>',
                    password: 'password123'
                }
            },
            projects: {
                method: 'GET',
                endpoint: '/projects/',
                body: null
            },
            github: {
                method: 'GET',
                endpoint: '/projects/github/repositories',
                body: null
            },
            jira: {
                method: 'GET',
                endpoint: '/webhooks/jira/status',
                body: null
            }
        };
        
        const example = examples[type];
        if (example) {
            document.getElementById('methodSelect').value = example.method;
            document.getElementById('endpointInput').value = example.endpoint;
            
            if (example.body) {
                document.getElementById('requestBody').value = JSON.stringify(example.body, null, 2);
            } else {
                document.getElementById('requestBody').value = '';
            }
            
            updateRequestBodyVisibility();
        }
    }
</script>
{% endblock %}
