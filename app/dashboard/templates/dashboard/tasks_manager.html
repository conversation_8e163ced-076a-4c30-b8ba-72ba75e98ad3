{% extends "dashboard/base.html" %}

{% block page_title %}Tasks Manager{% endblock %}
{% block page_description %}Manage tasks, create new tasks, and sync with external platforms{% endblock %}

{% block content %}
<!-- Authentication Token -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-key me-2"></i>Authentication Token</h5>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="authTokenInput" placeholder="Enter your JWT token here">
                            <button class="btn btn-outline-secondary" type="button" onclick="toggleTokenVisibility()">
                                <i class="fas fa-eye" id="tokenEye"></i>
                            </button>
                        </div>
                        <small class="text-muted">Get your token from <a href="/dashboard/auth-manager" target="_blank">Auth Manager</a></small>
                    </div>
                    <div class="col-md-4">
                        <div class="d-grid gap-2 d-md-flex">
                            <button class="btn btn-success" onclick="saveTokenFromInput()">
                                <i class="fas fa-save me-2"></i>Save Token
                            </button>
                            <button class="btn btn-outline-danger" onclick="clearAuthToken()">
                                <i class="fas fa-trash me-2"></i>Clear
                            </button>
                        </div>
                    </div>
                </div>
                <div id="tokenStatus" class="mt-2"></div>
            </div>
        </div>
    </div>
</div>

<!-- Create New Task -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-plus me-2"></i>Create New Task</h5>
            </div>
            <div class="card-body">
                <form id="createTaskForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Task Title *</label>
                                <input type="text" class="form-control" id="taskTitle" placeholder="Enter task title" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Description</label>
                                <textarea class="form-control" id="taskDescription" rows="3" placeholder="Task description"></textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Project *</label>
                                <select class="form-select" id="taskProject" required>
                                    <option value="">Select a project</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Priority</label>
                                <select class="form-select" id="taskPriority">
                                    <option value="low">Low</option>
                                    <option value="medium" selected>Medium</option>
                                    <option value="high">High</option>
                                    <option value="urgent">Urgent</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Status</label>
                                <select class="form-select" id="taskStatus">
                                    <option value="todo" selected>To Do</option>
                                    <option value="in_progress">In Progress</option>
                                    <option value="review">Review</option>
                                    <option value="done">Done</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Due Date</label>
                                <input type="date" class="form-control" id="taskDueDate">
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Task
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Task Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <label class="form-label">Filter by Project</label>
                        <select class="form-select" id="filterProject" onchange="filterTasks()">
                            <option value="">All Projects</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Status</label>
                        <select class="form-select" id="filterStatus" onchange="filterTasks()">
                            <option value="">All Status</option>
                            <option value="todo">To Do</option>
                            <option value="in_progress">In Progress</option>
                            <option value="review">Review</option>
                            <option value="done">Done</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Priority</label>
                        <select class="form-select" id="filterPriority" onchange="filterTasks()">
                            <option value="">All Priorities</option>
                            <option value="low">Low</option>
                            <option value="medium">Medium</option>
                            <option value="high">High</option>
                            <option value="urgent">Urgent</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Search</label>
                        <input type="text" class="form-control" id="searchTasks" placeholder="Search tasks..." onkeyup="filterTasks()">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button class="btn btn-outline-primary" onclick="loadTasks()">
                                <i class="fas fa-sync me-2"></i>Refresh
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tasks Board -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>Tasks Board</h5>
                    <div class="btn-group">
                        <button class="btn btn-outline-secondary" onclick="setViewMode('board')" id="boardViewBtn">
                            <i class="fas fa-columns"></i> Board
                        </button>
                        <button class="btn btn-outline-secondary" onclick="setViewMode('list')" id="listViewBtn">
                            <i class="fas fa-list"></i> List
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Board View -->
                <div id="boardView" style="display: none;">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0">To Do <span class="badge bg-secondary" id="todoCount">0</span></h6>
                                </div>
                                <div class="card-body" id="todoTasks" style="min-height: 300px;">
                                    <!-- Tasks will be populated here -->
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0">In Progress <span class="badge bg-warning" id="inProgressCount">0</span></h6>
                                </div>
                                <div class="card-body" id="inProgressTasks" style="min-height: 300px;">
                                    <!-- Tasks will be populated here -->
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0">Review <span class="badge bg-info" id="reviewCount">0</span></h6>
                                </div>
                                <div class="card-body" id="reviewTasks" style="min-height: 300px;">
                                    <!-- Tasks will be populated here -->
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0">Done <span class="badge bg-success" id="doneCount">0</span></h6>
                                </div>
                                <div class="card-body" id="doneTasks" style="min-height: 300px;">
                                    <!-- Tasks will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- List View -->
                <div id="listView">
                    <div id="tasksList">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading tasks...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- External Integration -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fab fa-github me-2"></i>GitHub Issues</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">Repository</label>
                    <select class="form-select" id="githubRepo">
                        <option value="">Select repository</option>
                    </select>
                </div>
                <div class="d-grid gap-2">
                    <button class="btn btn-dark" onclick="loadGitHubIssues()">
                        <i class="fab fa-github me-2"></i>Load Issues
                    </button>
                </div>
                <div id="githubIssues" class="mt-3"></div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fab fa-atlassian me-2"></i>Jira Issues</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">Project Key</label>
                    <select class="form-select" id="jiraProjectKey">
                        <option value="">Select project</option>
                    </select>
                </div>
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="loadJiraIssues()">
                        <i class="fab fa-atlassian me-2"></i>Load Issues
                    </button>
                </div>
                <div id="jiraIssues" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>

<!-- Task Statistics -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Task Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-2">
                        <div class="border rounded p-3">
                            <h4 class="text-primary" id="totalTasks">-</h4>
                            <p class="mb-0">Total Tasks</p>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border rounded p-3">
                            <h4 class="text-secondary" id="todoTasksCount">-</h4>
                            <p class="mb-0">To Do</p>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border rounded p-3">
                            <h4 class="text-warning" id="inProgressTasksCount">-</h4>
                            <p class="mb-0">In Progress</p>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border rounded p-3">
                            <h4 class="text-info" id="reviewTasksCount">-</h4>
                            <p class="mb-0">Review</p>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border rounded p-3">
                            <h4 class="text-success" id="doneTasksCount">-</h4>
                            <p class="mb-0">Done</p>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border rounded p-3">
                            <h4 class="text-danger" id="overdueTasks">-</h4>
                            <p class="mb-0">Overdue</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Task Details Modal -->
<div class="modal fade" id="taskModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="taskModalTitle">Task Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="taskModalBody">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="editTaskBtn">Edit Task</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentToken = '';
    let allTasks = [];
    let allProjects = [];
    let currentViewMode = 'list';

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        // Load saved token
        const savedToken = localStorage.getItem('tms_auth_token');
        if (savedToken) {
            currentToken = savedToken;
            document.getElementById('authTokenInput').value = savedToken;
            updateTokenStatus('Token loaded from storage', 'success');
        } else {
            updateTokenStatus('No token found. Please authenticate first.', 'warning');
        }

        // Check for project_id in URL params
        const urlParams = new URLSearchParams(window.location.search);
        const projectId = urlParams.get('project_id');

        loadProjects().then(() => {
            if (projectId) {
                document.getElementById('filterProject').value = projectId;
            }
            loadTasks();
        });

        // Load integration data
        loadGitHubRepositories();
        loadJiraProjects();
        loadTaskStats();
    });

    // Create task form
    document.getElementById('createTaskForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        if (!currentToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }

        const taskData = {
            title: document.getElementById('taskTitle').value,
            description: document.getElementById('taskDescription').value,
            project_id: parseInt(document.getElementById('taskProject').value),
            priority: document.getElementById('taskPriority').value,
            status: document.getElementById('taskStatus').value,
            due_date: document.getElementById('taskDueDate').value || null
        };

        try {
            showAlert('Creating task...', 'info');

            const response = await axios.post('/tasks/create_task_view', taskData, {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });

            if (response.data.success) {
                showAlert('Task created successfully!', 'success');
                document.getElementById('createTaskForm').reset();
                loadTasks();
                loadTaskStats();
            }
        } catch (error) {
            showAlert('Error creating task: ' + (error.response?.data?.message || error.message), 'danger');
        }
    });

    // Load projects for dropdowns
    async function loadProjects() {
        if (!currentToken) return;

        try {
            const response = await axios.get('/projects/', {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });

            if (response.data.success) {
                // API returns { data: { projects: [...], count: N } }
                allProjects = response.data.data.projects || [];

                // Populate project dropdowns
                const projectOptions = allProjects.map(project =>
                    `<option value="${project.id}">${project.name}</option>`
                ).join('');

                document.getElementById('taskProject').innerHTML =
                    '<option value="">Select a project</option>' + projectOptions;

                document.getElementById('filterProject').innerHTML =
                    '<option value="">All Projects</option>' + projectOptions;
            }
        } catch (error) {
            console.error('Error loading projects:', error);
        }
    }

    // Load GitHub repositories for dropdown
    async function loadGitHubRepositories() {
        if (!currentToken) {
            document.getElementById('githubRepo').innerHTML = '<option value="">Please authenticate first</option>';
            return;
        }

        try {
            const response = await axios.get('/projects/github/repositories', {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });

            if (response.data.success) {
                const repos = response.data.data.repositories || [];

                if (repos.length === 0) {
                    document.getElementById('githubRepo').innerHTML = '<option value="">No repositories found</option>';
                    return;
                }

                const repoOptions = repos.map(repo =>
                    `<option value="${repo.full_name}">${repo.full_name}</option>`
                ).join('');

                document.getElementById('githubRepo').innerHTML =
                    '<option value="">Select repository</option>' + repoOptions;
            }
        } catch (error) {
            console.error('Error loading GitHub repositories:', error);
            document.getElementById('githubRepo').innerHTML = '<option value="">Error loading repositories</option>';
        }
    }

    // Load Jira projects for dropdown
    async function loadJiraProjects() {
        if (!currentToken) {
            document.getElementById('jiraProjectKey').innerHTML = '<option value="">Please authenticate first</option>';
            return;
        }

        try {
            const response = await axios.get('/webhooks/jira/projects', {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });

            if (response.data.success) {
                const projects = response.data.data.projects || [];

                if (projects.length === 0) {
                    document.getElementById('jiraProjectKey').innerHTML = '<option value="">No projects found</option>';
                    return;
                }

                const projectOptions = projects.map(project =>
                    `<option value="${project.key}">${project.key} - ${project.name}</option>`
                ).join('');

                document.getElementById('jiraProjectKey').innerHTML =
                    '<option value="">Select project</option>' + projectOptions;
            }
        } catch (error) {
            console.error('Error loading Jira projects:', error);
            document.getElementById('jiraProjectKey').innerHTML = '<option value="">Error loading projects</option>';
        }
    }

    // Refresh all integration data when token changes
    function refreshIntegrationData() {
        if (currentToken) {
            loadProjects();
            loadGitHubRepositories();
            loadJiraProjects();
            loadTasks();
            loadTaskStats();
        } else {
            // Clear dropdowns when no token
            document.getElementById('githubRepo').innerHTML = '<option value="">Please authenticate first</option>';
            document.getElementById('jiraProjectKey').innerHTML = '<option value="">Please authenticate first</option>';
            document.getElementById('taskProject').innerHTML = '<option value="">Select a project</option>';
            document.getElementById('filterProject').innerHTML = '<option value="">All Projects</option>';
        }
    }

    // Save token and refresh data
    function saveAuthToken(token) {
        currentToken = token;
        localStorage.setItem('tms_auth_token', token);
        refreshIntegrationData();
    }

    // Clear token and refresh data
    function clearAuthToken() {
        currentToken = '';
        localStorage.removeItem('tms_auth_token');
        document.getElementById('authTokenInput').value = '';
        updateTokenStatus('Token cleared', 'warning');
        refreshIntegrationData();
    }

    // Save token from input field
    function saveTokenFromInput() {
        const token = document.getElementById('authTokenInput').value.trim();
        if (!token) {
            updateTokenStatus('Please enter a token', 'danger');
            return;
        }

        saveAuthToken(token);
        updateTokenStatus('Token saved successfully', 'success');
    }

    // Toggle token visibility
    function toggleTokenVisibility() {
        const input = document.getElementById('authTokenInput');
        const eye = document.getElementById('tokenEye');

        if (input.type === 'password') {
            input.type = 'text';
            eye.className = 'fas fa-eye-slash';
        } else {
            input.type = 'password';
            eye.className = 'fas fa-eye';
        }
    }

    // Update token status display
    function updateTokenStatus(message, type) {
        const statusDiv = document.getElementById('tokenStatus');
        const alertClass = type === 'success' ? 'alert-success' :
                          type === 'warning' ? 'alert-warning' : 'alert-danger';

        statusDiv.innerHTML = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="fas fa-${type === 'success' ? 'check' : type === 'warning' ? 'exclamation-triangle' : 'times'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // Auto-hide after 3 seconds
        setTimeout(() => {
            const alert = statusDiv.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 3000);
    }

    // Load tasks
    async function loadTasks() {
        if (!currentToken) {
            document.getElementById('tasksList').innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Please authenticate first to view tasks
                </div>
            `;
            return;
        }

        try {
            const response = await axios.get('/tasks/list_tasks', {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });

            if (response.data.success) {
                // Tasks API returns array directly in data
                allTasks = response.data.data || [];
                filterTasks();
            }
        } catch (error) {
            document.getElementById('tasksList').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error loading tasks: ${error.response?.data?.message || error.message}
                </div>
            `;
        }
    }

    // Filter tasks
    function filterTasks() {
        const projectFilter = document.getElementById('filterProject').value;
        const statusFilter = document.getElementById('filterStatus').value;
        const priorityFilter = document.getElementById('filterPriority').value;
        const searchFilter = document.getElementById('searchTasks').value.toLowerCase();

        let filteredTasks = allTasks.filter(task => {
            const matchesProject = !projectFilter || task.project_id == projectFilter;
            const matchesStatus = !statusFilter || task.status === statusFilter;
            const matchesPriority = !priorityFilter || task.priority === priorityFilter;
            const matchesSearch = !searchFilter ||
                task.title.toLowerCase().includes(searchFilter) ||
                (task.description && task.description.toLowerCase().includes(searchFilter));

            return matchesProject && matchesStatus && matchesPriority && matchesSearch;
        });

        if (currentViewMode === 'board') {
            displayTasksBoard(filteredTasks);
        } else {
            displayTasksList(filteredTasks);
        }
    }

    // Display tasks in list view
    function displayTasksList(tasks) {
        if (tasks.length === 0) {
            document.getElementById('tasksList').innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-tasks me-2"></i>No tasks found
                </div>
            `;
            return;
        }

        const tasksHtml = tasks.map(task => {
            const project = allProjects.find(p => p.id === task.project_id);
            const isOverdue = task.due_date && new Date(task.due_date) < new Date() && task.status !== 'done';

            return `
                <div class="card mb-3 task-card ${isOverdue ? 'border-danger' : ''}">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h6 class="mb-1">${task.title} ${isOverdue ? '<i class="fas fa-exclamation-triangle text-danger" title="Overdue"></i>' : ''}</h6>
                                <p class="mb-1 text-muted">${task.description || 'No description'}</p>
                                <div class="d-flex gap-2 mb-2">
                                    <span class="badge bg-${getStatusColor(task.status)}">${task.status.replace('_', ' ')}</span>
                                    <span class="badge bg-${getPriorityColor(task.priority)}">${task.priority}</span>
                                    ${project ? `<span class="badge bg-secondary">${project.name}</span>` : ''}
                                    ${task.github_issue_number ? '<span class="badge bg-dark"><i class="fab fa-github me-1"></i>GitHub</span>' : ''}
                                    ${task.jira_issue_key ? '<span class="badge bg-primary"><i class="fab fa-atlassian me-1"></i>Jira</span>' : ''}
                                </div>
                                <small class="text-muted">
                                    Created: ${new Date(task.created_at).toLocaleDateString()}
                                    ${task.due_date ? ` | Due: ${new Date(task.due_date).toLocaleDateString()}` : ''}
                                </small>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="btn-group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewTask(${task.id})">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button class="btn btn-sm btn-outline-success" onclick="updateTaskStatus(${task.id}, '${getNextStatus(task.status)}')">
                                        <i class="fas fa-arrow-right"></i> ${getNextStatus(task.status).replace('_', ' ')}
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteTask(${task.id})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        document.getElementById('tasksList').innerHTML = tasksHtml;
    }

    // Display tasks in board view
    function displayTasksBoard(tasks) {
        const tasksByStatus = {
            'todo': tasks.filter(t => t.status === 'todo'),
            'in_progress': tasks.filter(t => t.status === 'in_progress'),
            'review': tasks.filter(t => t.status === 'review'),
            'done': tasks.filter(t => t.status === 'done')
        };

        // Update counts
        document.getElementById('todoCount').textContent = tasksByStatus.todo.length;
        document.getElementById('inProgressCount').textContent = tasksByStatus.in_progress.length;
        document.getElementById('reviewCount').textContent = tasksByStatus.review.length;
        document.getElementById('doneCount').textContent = tasksByStatus.done.length;

        // Populate columns
        Object.keys(tasksByStatus).forEach(status => {
            const columnId = status === 'in_progress' ? 'inProgressTasks' :
                           status === 'todo' ? 'todoTasks' :
                           status === 'review' ? 'reviewTasks' : 'doneTasks';

            const tasksHtml = tasksByStatus[status].map(task => {
                const project = allProjects.find(p => p.id === task.project_id);
                const isOverdue = task.due_date && new Date(task.due_date) < new Date() && task.status !== 'done';

                return `
                    <div class="card mb-2 task-card-small ${isOverdue ? 'border-danger' : ''}" onclick="viewTask(${task.id})" style="cursor: pointer;">
                        <div class="card-body p-2">
                            <h6 class="card-title mb-1 small">${task.title}</h6>
                            <div class="d-flex gap-1 mb-1">
                                <span class="badge bg-${getPriorityColor(task.priority)} small">${task.priority}</span>
                                ${project ? `<span class="badge bg-secondary small">${project.name}</span>` : ''}
                            </div>
                            ${task.due_date ? `<small class="text-muted">Due: ${new Date(task.due_date).toLocaleDateString()}</small>` : ''}
                        </div>
                    </div>
                `;
            }).join('');

            document.getElementById(columnId).innerHTML = tasksHtml || '<div class="text-muted text-center">No tasks</div>';
        });
    }

    // Helper functions
    function getStatusColor(status) {
        const colors = {
            'todo': 'secondary',
            'in_progress': 'warning',
            'review': 'info',
            'done': 'success'
        };
        return colors[status] || 'secondary';
    }

    function getPriorityColor(priority) {
        const colors = {
            'low': 'success',
            'medium': 'warning',
            'high': 'danger',
            'urgent': 'dark'
        };
        return colors[priority] || 'secondary';
    }

    function getNextStatus(currentStatus) {
        const statusFlow = {
            'todo': 'in_progress',
            'in_progress': 'review',
            'review': 'done',
            'done': 'todo'
        };
        return statusFlow[currentStatus] || 'in_progress';
    }

    // Set view mode
    function setViewMode(mode) {
        currentViewMode = mode;

        if (mode === 'board') {
            document.getElementById('listView').style.display = 'none';
            document.getElementById('boardView').style.display = 'block';
            document.getElementById('boardViewBtn').classList.add('active');
            document.getElementById('listViewBtn').classList.remove('active');
        } else {
            document.getElementById('boardView').style.display = 'none';
            document.getElementById('listView').style.display = 'block';
            document.getElementById('listViewBtn').classList.add('active');
            document.getElementById('boardViewBtn').classList.remove('active');
        }

        filterTasks();
    }

    // View task details
    async function viewTask(taskId) {
        if (!currentToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }

        try {
            const response = await axios.get(`/tasks/${taskId}`, {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });

            if (response.data.success) {
                const task = response.data.data;
                const project = allProjects.find(p => p.id === task.project_id);

                document.getElementById('taskModalTitle').textContent = task.title;
                document.getElementById('taskModalBody').innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Basic Information</h6>
                            <p><strong>Title:</strong> ${task.title}</p>
                            <p><strong>Description:</strong> ${task.description || 'No description'}</p>
                            <p><strong>Project:</strong> ${project ? project.name : 'No project'}</p>
                            <p><strong>Status:</strong> <span class="badge bg-${getStatusColor(task.status)}">${task.status.replace('_', ' ')}</span></p>
                            <p><strong>Priority:</strong> <span class="badge bg-${getPriorityColor(task.priority)}">${task.priority}</span></p>
                        </div>
                        <div class="col-md-6">
                            <h6>Dates & Timeline</h6>
                            <p><strong>Created:</strong> ${new Date(task.created_at).toLocaleString()}</p>
                            <p><strong>Due Date:</strong> ${task.due_date ? new Date(task.due_date).toLocaleDateString() : 'Not set'}</p>
                            <p><strong>Last Updated:</strong> ${new Date(task.updated_at).toLocaleString()}</p>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>External Integration</h6>
                            <p><strong>GitHub:</strong> ${task.github_issue_number ? `✅ Issue #${task.github_issue_number}` : '❌ Not linked'}</p>
                            <p><strong>Jira:</strong> ${task.jira_issue_key ? `✅ Issue ${task.jira_issue_key}` : '❌ Not linked'}</p>
                        </div>
                    </div>
                `;

                const modal = new bootstrap.Modal(document.getElementById('taskModal'));
                modal.show();
            }
        } catch (error) {
            showAlert('Error loading task details: ' + (error.response?.data?.message || error.message), 'danger');
        }
    }

    // Update task status
    async function updateTaskStatus(taskId, newStatus) {
        if (!currentToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }

        try {
            const response = await axios.put(`/tasks/update_task_view/${taskId}`, {
                status: newStatus
            }, {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });

            if (response.data.success) {
                showAlert(`Task status updated to ${newStatus.replace('_', ' ')}!`, 'success');
                loadTasks();
                loadTaskStats();
            }
        } catch (error) {
            showAlert('Error updating task: ' + (error.response?.data?.message || error.message), 'danger');
        }
    }

    // Delete task
    async function deleteTask(taskId) {
        if (!currentToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }

        if (!confirm('Are you sure you want to delete this task?')) {
            return;
        }

        try {
            const response = await axios.delete(`/tasks/delete_task_view/${taskId}`, {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });

            if (response.data.success) {
                showAlert('Task deleted successfully!', 'success');
                loadTasks();
                loadTaskStats();
            }
        } catch (error) {
            showAlert('Error deleting task: ' + (error.response?.data?.message || error.message), 'danger');
        }
    }

    // Load task statistics
    async function loadTaskStats() {
        try {
            const response = await axios.get('/dashboard/api/stats');
            const stats = response.data;

            document.getElementById('totalTasks').textContent = stats.total_tasks || 0;

            // Calculate other stats from allTasks
            if (allTasks.length > 0) {
                const todoCount = allTasks.filter(t => t.status === 'todo').length;
                const inProgressCount = allTasks.filter(t => t.status === 'in_progress').length;
                const reviewCount = allTasks.filter(t => t.status === 'review').length;
                const doneCount = allTasks.filter(t => t.status === 'done').length;
                const overdueCount = allTasks.filter(t =>
                    t.due_date && new Date(t.due_date) < new Date() && t.status !== 'done'
                ).length;

                document.getElementById('todoTasksCount').textContent = todoCount;
                document.getElementById('inProgressTasksCount').textContent = inProgressCount;
                document.getElementById('reviewTasksCount').textContent = reviewCount;
                document.getElementById('doneTasksCount').textContent = doneCount;
                document.getElementById('overdueTasks').textContent = overdueCount;
            }
        } catch (error) {
            console.error('Error loading task stats:', error);
        }
    }

    // Load GitHub issues
    async function loadGitHubIssues() {
        const repo = document.getElementById('githubRepo').value;
        if (!repo) {
            showAlert('Please select a repository', 'warning');
            return;
        }

        if (!currentToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }

        try {
            showAlert('Loading GitHub issues...', 'info');

            const response = await axios.get(`/projects/github/repositories/${encodeURIComponent(repo)}/issues`, {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });

            if (response.data.success) {
                const issues = response.data.data.issues;

                if (issues.length === 0) {
                    document.getElementById('githubIssues').innerHTML = `
                        <div class="text-muted">No issues found</div>
                    `;
                    return;
                }

                const issuesHtml = issues.slice(0, 5).map(issue => `
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                        <div>
                            <strong>#${issue.number}</strong> ${issue.title}
                            <br><small class="text-muted">${issue.state}</small>
                        </div>
                        <button class="btn btn-sm btn-outline-primary" onclick="syncGitHubIssue('${repo}', ${issue.number})">
                            Sync
                        </button>
                    </div>
                `).join('');

                document.getElementById('githubIssues').innerHTML = issuesHtml;
                showAlert(`Loaded ${issues.length} issues`, 'success');
            }
        } catch (error) {
            document.getElementById('githubIssues').innerHTML = `
                <div class="text-danger">Error: ${error.response?.data?.message || error.message}</div>
            `;
        }
    }

    // Load Jira issues
    async function loadJiraIssues() {
        const projectKey = document.getElementById('jiraProjectKey').value;
        if (!projectKey) {
            showAlert('Please enter a project key', 'warning');
            return;
        }

        if (!currentToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }

        try {
            showAlert('Loading Jira issues...', 'info');

            const response = await axios.get(`/webhooks/jira/projects/${projectKey}/issues`, {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });

            if (response.data.success) {
                const issues = response.data.data.issues;

                if (issues.length === 0) {
                    document.getElementById('jiraIssues').innerHTML = `
                        <div class="text-muted">No issues found</div>
                    `;
                    return;
                }

                const issuesHtml = issues.slice(0, 5).map(issue => `
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                        <div>
                            <strong>${issue.key}</strong> ${issue.summary}
                            <br><small class="text-muted">${issue.status?.name || 'Unknown'}</small>
                        </div>
                        <button class="btn btn-sm btn-outline-primary" onclick="syncJiraIssue('${issue.key}')">
                            Sync
                        </button>
                    </div>
                `).join('');

                document.getElementById('jiraIssues').innerHTML = issuesHtml;
                showAlert(`Loaded ${issues.length} issues`, 'success');
            }
        } catch (error) {
            document.getElementById('jiraIssues').innerHTML = `
                <div class="text-danger">Error: ${error.response?.data?.message || error.message}</div>
            `;
        }
    }

    // Sync GitHub issue
    async function syncGitHubIssue(repo, issueNumber) {
        if (!currentToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }

        try {
            showAlert(`Syncing GitHub issue #${issueNumber}...`, 'info');

            const response = await axios.post(`/projects/github/repositories/${encodeURIComponent(repo)}/issues/${issueNumber}/sync`, {}, {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });

            if (response.data.success) {
                showAlert(`GitHub issue #${issueNumber} synced successfully!`, 'success');
                loadTasks();
                loadTaskStats();
            }
        } catch (error) {
            showAlert(`Error syncing issue: ${error.response?.data?.message || error.message}`, 'danger');
        }
    }

    // Sync Jira issue
    async function syncJiraIssue(issueKey) {
        if (!currentToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }

        // Get project ID from filter or first project
        const projectId = document.getElementById('filterProject').value ||
                         (allProjects.length > 0 ? allProjects[0].id : null);

        if (!projectId) {
            showAlert('Please select a project first', 'warning');
            return;
        }

        try {
            showAlert(`Syncing Jira issue ${issueKey}...`, 'info');

            const response = await axios.post(`/webhooks/jira/issues/${issueKey}/sync`, {
                project_id: parseInt(projectId)
            }, {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });

            if (response.data.success) {
                showAlert(`Jira issue ${issueKey} synced successfully!`, 'success');
                loadTasks();
                loadTaskStats();
            }
        } catch (error) {
            showAlert(`Error syncing issue: ${error.response?.data?.message || error.message}`, 'danger');
        }
    }
</script>
{% endblock %}