{% extends "dashboard/base.html" %}

{% block page_title %}GitHub Integration{% endblock %}
{% block page_description %}Manage GitHub integration, OAuth, repositories, and sync{% endblock %}

{% block content %}
<!-- Integration Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fab fa-github me-2"></i>GitHub Integration Status</h5>
            </div>
            <div class="card-body">
                <div id="githubStatus">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Checking GitHub integration status...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- OAuth Authentication -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-key me-2"></i>OAuth Authentication</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6>Connect to GitHub</h6>
                        <p class="text-muted">Authenticate with your GitHub account to enable repository integration.</p>
                        <div class="mb-3">
                            <label class="form-label">Authentication Token</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="authToken" placeholder="Enter your JWT token">
                                <button class="btn btn-outline-secondary" type="button" onclick="toggleTokenVisibility()">
                                    <i class="fas fa-eye" id="tokenEye"></i>
                                </button>
                            </div>
                            <div class="form-text">Get your token by completing the OAuth flow below.</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-grid gap-2">
                            <button class="btn btn-dark" onclick="startGitHubOAuth()">
                                <i class="fab fa-github me-2"></i>Connect to GitHub
                            </button>
                            <button class="btn btn-outline-info" onclick="getOAuthUrl()">
                                <i class="fas fa-link me-2"></i>Get OAuth URL
                            </button>
                            <button class="btn btn-outline-success" onclick="checkGitHubStatus()">
                                <i class="fas fa-sync me-2"></i>Check Status
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- GitHub Repositories -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-code-branch me-2"></i>GitHub Repositories</h5>
                    <button class="btn btn-outline-primary" onclick="loadGitHubRepos()">
                        <i class="fas fa-sync me-2"></i>Refresh Repositories
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="githubRepos">
                    <div class="text-center text-muted">
                        <i class="fas fa-info-circle me-2"></i>Connect to GitHub to view repositories
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Repository Sync -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-sync me-2"></i>Repository Synchronization</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Sync GitHub Repository to TMS</h6>
                        <div class="mb-3">
                            <label class="form-label">Repository Full Name</label>
                            <input type="text" class="form-control" id="repoFullNameSync" placeholder="e.g., username/repository">
                        </div>
                        <button class="btn btn-success" onclick="syncGitHubRepo()">
                            <i class="fas fa-download me-2"></i>Sync to TMS
                        </button>
                    </div>
                    <div class="col-md-6">
                        <h6>Recent Sync Activities</h6>
                        <div id="syncActivities">
                            <div class="text-muted">No sync activities yet</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Issue Management -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bug me-2"></i>Issue Management</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>Get Repository Issues</h6>
                        <div class="mb-3">
                            <label class="form-label">Repository</label>
                            <input type="text" class="form-control" id="repoFullNameIssues" placeholder="username/repository">
                        </div>
                        <button class="btn btn-info" onclick="getRepoIssues()">
                            <i class="fas fa-list me-2"></i>Get Issues
                        </button>
                    </div>
                    <div class="col-md-4">
                        <h6>Sync Issue to Task</h6>
                        <div class="mb-2">
                            <label class="form-label">Repository</label>
                            <input type="text" class="form-control" id="repoFullNameIssueSync" placeholder="username/repository">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Issue Number</label>
                            <input type="number" class="form-control" id="issueNumber" placeholder="123">
                        </div>
                        <button class="btn btn-warning" onclick="syncIssueToTask()">
                            <i class="fas fa-arrow-right me-2"></i>Sync to Task
                        </button>
                    </div>
                    <div class="col-md-4">
                        <h6>Create GitHub Issue</h6>
                        <div class="mb-2">
                            <label class="form-label">Repository</label>
                            <input type="text" class="form-control" id="repoFullNameCreate" placeholder="username/repository">
                        </div>
                        <div class="mb-2">
                            <label class="form-label">Title</label>
                            <input type="text" class="form-control" id="issueTitle" placeholder="Issue title">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Body</label>
                            <textarea class="form-control" id="issueBody" rows="2" placeholder="Issue description"></textarea>
                        </div>
                        <button class="btn btn-secondary" onclick="createGitHubIssue()">
                            <i class="fas fa-plus me-2"></i>Create Issue
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Results Display -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Results</h5>
                    <button class="btn btn-sm btn-outline-secondary" onclick="clearResults()">
                        <i class="fas fa-trash me-2"></i>Clear
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="resultsDisplay">
                    <div class="text-center text-muted">
                        <i class="fas fa-info-circle me-2"></i>Results will appear here
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let authToken = '';
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        // Load saved token
        const savedToken = localStorage.getItem('tms_auth_token');
        if (savedToken) {
            document.getElementById('authToken').value = savedToken;
            authToken = savedToken;
            checkGitHubStatus();
        }
        
        // Auto-save token
        document.getElementById('authToken').addEventListener('input', function() {
            authToken = this.value;
            localStorage.setItem('tms_auth_token', authToken);
        });
    });
    
    // Toggle token visibility
    function toggleTokenVisibility() {
        const tokenInput = document.getElementById('authToken');
        const eyeIcon = document.getElementById('tokenEye');
        
        if (tokenInput.type === 'password') {
            tokenInput.type = 'text';
            eyeIcon.className = 'fas fa-eye-slash';
        } else {
            tokenInput.type = 'password';
            eyeIcon.className = 'fas fa-eye';
        }
    }
    
    // Start GitHub OAuth
    function startGitHubOAuth() {
        showAlert('Redirecting to GitHub OAuth...', 'info');
        window.open('/auth/github', '_blank');
    }
    
    // Get OAuth URL
    async function getOAuthUrl() {
        try {
            const response = await axios.get('/auth/github/url');
            
            if (response.data.success) {
                const url = response.data.data.authorization_url;
                
                // Show modal with URL
                const modal = document.createElement('div');
                modal.innerHTML = `
                    <div class="modal fade" id="oauthModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">GitHub OAuth URL</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <p>Copy this URL and open it in a new tab to complete OAuth:</p>
                                    <div class="input-group mb-3">
                                        <input type="text" class="form-control" value="${url}" readonly>
                                        <button class="btn btn-outline-secondary" onclick="copyToClipboard('${url}')">
                                            <i class="fas fa-copy"></i> Copy
                                        </button>
                                    </div>
                                    <div class="d-grid">
                                        <a href="${url}" target="_blank" class="btn btn-dark">
                                            <i class="fab fa-github me-2"></i>Open GitHub OAuth
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
                
                const bootstrapModal = new bootstrap.Modal(document.getElementById('oauthModal'));
                bootstrapModal.show();
                
                // Clean up modal after close
                document.getElementById('oauthModal').addEventListener('hidden.bs.modal', function() {
                    document.body.removeChild(modal);
                });
            }
        } catch (error) {
            showAlert('Error getting OAuth URL: ' + error.message, 'danger');
        }
    }
    
    // Check GitHub status
    async function checkGitHubStatus() {
        if (!authToken) {
            document.getElementById('githubStatus').innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Please enter your authentication token first
                </div>
            `;
            return;
        }

        try {
            const response = await axios.get('/projects/github/status', {
                headers: { 'Authorization': `Bearer ${authToken}` }
            });

            if (response.data.success) {
                const data = response.data.data;

                if (data.connected) {
                    document.getElementById('githubStatus').innerHTML = `
                        <div class="alert alert-success">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6><i class="fas fa-check-circle me-2"></i>Connected to GitHub</h6>
                                    <p class="mb-1"><strong>Username:</strong> ${data.user?.login || 'Unknown'}</p>
                                    <p class="mb-1"><strong>Name:</strong> ${data.user?.name || 'Not provided'}</p>
                                    <p class="mb-0"><strong>Public Repos:</strong> ${data.user?.public_repos || 0}</p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <span class="badge bg-success fs-6">Active</span>
                                </div>
                            </div>
                        </div>
                    `;

                    // Auto-load repositories if connected
                    loadGitHubRepos();
                } else {
                    document.getElementById('githubStatus').innerHTML = `
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Not Connected</h6>
                            <p class="mb-0">GitHub integration is not active. Please complete OAuth authentication.</p>
                        </div>
                    `;
                }
            }
        } catch (error) {
            document.getElementById('githubStatus').innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-times-circle me-2"></i>Connection Error</h6>
                    <p class="mb-0">Error: ${error.response?.data?.message || error.message}</p>
                </div>
            `;
        }
    }
    
    // Load GitHub repositories
    async function loadGitHubRepos() {
        if (!authToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }
        
        document.getElementById('githubRepos').innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status"></div>
                <p class="mt-2">Loading GitHub repositories...</p>
            </div>
        `;
        
        try {
            const response = await axios.get('/projects/github/repositories', {
                headers: { 'Authorization': `Bearer ${authToken}` }
            });
            
            if (response.data.success) {
                const repos = response.data.data.repositories;
                
                if (repos.length === 0) {
                    document.getElementById('githubRepos').innerHTML = `
                        <div class="text-center text-muted">
                            <i class="fas fa-code-branch me-2"></i>No repositories found
                        </div>
                    `;
                    return;
                }
                
                const reposHtml = repos.map(repo => `
                    <div class="card mb-2">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h6 class="mb-1">${repo.name}</h6>
                                    <p class="mb-1"><strong>Full Name:</strong> <code>${repo.full_name}</code></p>
                                    <p class="mb-1 text-muted small">${repo.description || 'No description'}</p>
                                    <div class="d-flex gap-2">
                                        <span class="badge bg-${repo.private ? 'warning' : 'success'}">${repo.private ? 'Private' : 'Public'}</span>
                                        ${repo.language ? `<span class="badge bg-info">${repo.language}</span>` : ''}
                                        <span class="badge bg-secondary">${repo.stargazers_count} ⭐</span>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <button class="btn btn-sm btn-outline-primary me-2" onclick="viewRepoIssues('${repo.full_name}')">
                                        <i class="fas fa-bug"></i> Issues
                                    </button>
                                    <button class="btn btn-sm btn-success" onclick="syncRepo('${repo.full_name}')">
                                        <i class="fas fa-sync"></i> Sync
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('');
                
                document.getElementById('githubRepos').innerHTML = reposHtml;
            }
        } catch (error) {
            document.getElementById('githubRepos').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error loading repositories: ${error.response?.data?.message || error.message}
                </div>
            `;
        }
    }
    
    // Sync GitHub repository
    async function syncGitHubRepo() {
        const repoFullName = document.getElementById('repoFullNameSync').value;
        if (!repoFullName) {
            showAlert('Please enter a repository full name', 'warning');
            return;
        }
        
        await syncRepo(repoFullName);
    }
    
    async function syncRepo(repoFullName) {
        if (!authToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }
        
        try {
            showAlert(`Syncing repository ${repoFullName}...`, 'info');
            
            const response = await axios.post(`/projects/github/repositories/${encodeURIComponent(repoFullName)}/sync`, {}, {
                headers: { 'Authorization': `Bearer ${authToken}` }
            });
            
            if (response.data.success) {
                showAlert(`Repository ${repoFullName} synced successfully!`, 'success');
                
                // Add to sync activities
                addSyncActivity('Repository Sync', repoFullName, 'success');
                
                displayResults(response.data);
            }
        } catch (error) {
            showAlert(`Error syncing repository: ${error.response?.data?.message || error.message}`, 'danger');
            addSyncActivity('Repository Sync', repoFullName, 'error');
        }
    }
    
    // Get repository issues
    async function getRepoIssues() {
        const repoFullName = document.getElementById('repoFullNameIssues').value;
        if (!repoFullName) {
            showAlert('Please enter a repository full name', 'warning');
            return;
        }
        
        await viewRepoIssues(repoFullName);
    }
    
    async function viewRepoIssues(repoFullName) {
        if (!authToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }
        
        try {
            showAlert(`Loading issues for ${repoFullName}...`, 'info');
            
            const response = await axios.get(`/projects/github/repositories/${encodeURIComponent(repoFullName)}/issues`, {
                headers: { 'Authorization': `Bearer ${authToken}` }
            });
            
            if (response.data.success) {
                showAlert(`Loaded ${response.data.data.issues.length} issues`, 'success');
                displayResults(response.data);
            }
        } catch (error) {
            showAlert(`Error loading issues: ${error.response?.data?.message || error.message}`, 'danger');
        }
    }
    
    // Sync issue to task
    async function syncIssueToTask() {
        const repoFullName = document.getElementById('repoFullNameIssueSync').value;
        const issueNumber = document.getElementById('issueNumber').value;
        
        if (!repoFullName || !issueNumber) {
            showAlert('Please enter both repository name and issue number', 'warning');
            return;
        }
        
        if (!authToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }
        
        try {
            showAlert(`Syncing issue #${issueNumber} to task...`, 'info');
            
            const response = await axios.post(`/projects/github/repositories/${encodeURIComponent(repoFullName)}/issues/${issueNumber}/sync`, {}, {
                headers: { 'Authorization': `Bearer ${authToken}` }
            });
            
            if (response.data.success) {
                showAlert(`Issue #${issueNumber} synced to task successfully!`, 'success');
                addSyncActivity('Issue Sync', `${repoFullName}#${issueNumber}`, 'success');
                displayResults(response.data);
            }
        } catch (error) {
            showAlert(`Error syncing issue: ${error.response?.data?.message || error.message}`, 'danger');
            addSyncActivity('Issue Sync', `${repoFullName}#${issueNumber}`, 'error');
        }
    }
    
    // Create GitHub issue
    async function createGitHubIssue() {
        const repoFullName = document.getElementById('repoFullNameCreate').value;
        const title = document.getElementById('issueTitle').value;
        const body = document.getElementById('issueBody').value;
        
        if (!repoFullName || !title) {
            showAlert('Please enter repository name and issue title', 'warning');
            return;
        }
        
        if (!authToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }
        
        try {
            showAlert('Creating GitHub issue...', 'info');
            
            const response = await axios.post(`/projects/github/repositories/${encodeURIComponent(repoFullName)}/issues`, {
                title: title,
                body: body
            }, {
                headers: { 'Authorization': `Bearer ${authToken}` }
            });
            
            if (response.data.success) {
                showAlert('GitHub issue created successfully!', 'success');
                
                // Clear form
                document.getElementById('issueTitle').value = '';
                document.getElementById('issueBody').value = '';
                
                displayResults(response.data);
            }
        } catch (error) {
            showAlert(`Error creating issue: ${error.response?.data?.message || error.message}`, 'danger');
        }
    }
    
    // Add sync activity
    function addSyncActivity(type, item, status) {
        const container = document.getElementById('syncActivities');
        const timestamp = new Date().toLocaleString();
        
        const activityHtml = `
            <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                <div>
                    <strong>${type}</strong>: ${item}
                    <br><small class="text-muted">${timestamp}</small>
                </div>
                <span class="badge bg-${status === 'success' ? 'success' : 'danger'}">
                    ${status === 'success' ? 'Success' : 'Error'}
                </span>
            </div>
        `;
        
        if (container.innerHTML.includes('No sync activities')) {
            container.innerHTML = activityHtml;
        } else {
            container.insertAdjacentHTML('afterbegin', activityHtml);
        }
    }
    
    // Display results
    function displayResults(data) {
        const container = document.getElementById('resultsDisplay');
        container.innerHTML = `
            <div class="json-viewer">
                <pre><code class="language-json">${JSON.stringify(data, null, 2)}</code></pre>
            </div>
        `;
        
        // Highlight code
        if (window.Prism) {
            Prism.highlightAll();
        }
    }
    
    // Clear results
    function clearResults() {
        document.getElementById('resultsDisplay').innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-info-circle me-2"></i>Results will appear here
            </div>
        `;
    }
</script>
{% endblock %}
