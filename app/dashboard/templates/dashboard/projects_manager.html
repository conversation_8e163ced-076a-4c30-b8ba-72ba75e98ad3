{% extends "dashboard/base.html" %}

{% block page_title %}Projects Manager{% endblock %}
{% block page_description %}Manage projects, create new projects, and sync with external platforms{% endblock %}

{% block content %}
<!-- Create New Project -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-plus me-2"></i>Create New Project</h5>
            </div>
            <div class="card-body">
                <form id="createProjectForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Project Name *</label>
                                <input type="text" class="form-control" id="projectName" placeholder="Enter project name" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Description</label>
                                <textarea class="form-control" id="projectDescription" rows="3" placeholder="Project description"></textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Priority</label>
                                <select class="form-select" id="projectPriority">
                                    <option value="low">Low</option>
                                    <option value="medium" selected>Medium</option>
                                    <option value="high">High</option>
                                    <option value="urgent">Urgent</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Status</label>
                                <select class="form-select" id="projectStatus">
                                    <option value="planning">Planning</option>
                                    <option value="active" selected>Active</option>
                                    <option value="on_hold">On Hold</option>
                                    <option value="completed">Completed</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="projectStartDate">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">End Date</label>
                                <input type="date" class="form-control" id="projectEndDate">
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Project
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Projects List -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-folder me-2"></i>All Projects</h5>
                    <div>
                        <button class="btn btn-outline-primary" onclick="loadProjects()">
                            <i class="fas fa-sync me-2"></i>Refresh
                        </button>
                        <div class="btn-group ms-2">
                            <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-filter me-2"></i>Filter
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="filterProjects('all')">All Projects</a></li>
                                <li><a class="dropdown-item" href="#" onclick="filterProjects('active')">Active</a></li>
                                <li><a class="dropdown-item" href="#" onclick="filterProjects('completed')">Completed</a></li>
                                <li><a class="dropdown-item" href="#" onclick="filterProjects('github')">GitHub Synced</a></li>
                                <li><a class="dropdown-item" href="#" onclick="filterProjects('jira')">Jira Synced</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div id="projectsList">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading projects...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- GitHub Integration -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fab fa-github me-2"></i>GitHub Integration</h5>
            </div>
            <div class="card-body">
                <h6>Import from GitHub</h6>
                <p class="text-muted">Import repositories as projects from your GitHub account.</p>
                <div class="mb-3">
                    <button class="btn btn-dark" onclick="loadGitHubRepos()">
                        <i class="fab fa-github me-2"></i>Load Repositories
                    </button>
                </div>
                <div id="githubRepos"></div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fab fa-atlassian me-2"></i>Jira Integration</h5>
            </div>
            <div class="card-body">
                <h6>Import from Jira</h6>
                <p class="text-muted">Import Jira projects into TMS.</p>
                <div class="mb-3">
                    <button class="btn btn-primary" onclick="loadJiraProjects()">
                        <i class="fab fa-atlassian me-2"></i>Load Projects
                    </button>
                </div>
                <div id="jiraProjects"></div>
            </div>
        </div>
    </div>
</div>

<!-- Project Statistics -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Project Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h4 class="text-primary" id="totalProjects">-</h4>
                            <p class="mb-0">Total Projects</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h4 class="text-success" id="activeProjects">-</h4>
                            <p class="mb-0">Active Projects</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h4 class="text-info" id="githubProjects">-</h4>
                            <p class="mb-0">GitHub Synced</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h4 class="text-warning" id="jiraProjects">-</h4>
                            <p class="mb-0">Jira Synced</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Project Details Modal -->
<div class="modal fade" id="projectModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="projectModalTitle">Project Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="projectModalBody">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="editProjectBtn">Edit Project</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentToken = '';
    let allProjects = [];
    let currentFilter = 'all';
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        // Load saved token
        const savedToken = localStorage.getItem('tms_auth_token');
        if (savedToken) {
            currentToken = savedToken;
        }
        
        loadProjects();
        loadProjectStats();
    });
    
    // Create project form
    document.getElementById('createProjectForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        if (!currentToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }
        
        const projectData = {
            name: document.getElementById('projectName').value,
            description: document.getElementById('projectDescription').value,
            priority: document.getElementById('projectPriority').value,
            status: document.getElementById('projectStatus').value,
            start_date: document.getElementById('projectStartDate').value || null,
            end_date: document.getElementById('projectEndDate').value || null
        };
        
        try {
            showAlert('Creating project...', 'info');
            
            const response = await axios.post('/projects/', projectData, {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });
            
            if (response.data.success) {
                showAlert('Project created successfully!', 'success');
                document.getElementById('createProjectForm').reset();
                loadProjects();
                loadProjectStats();
            }
        } catch (error) {
            showAlert('Error creating project: ' + (error.response?.data?.message || error.message), 'danger');
        }
    });
    
    // Load projects
    async function loadProjects() {
        if (!currentToken) {
            document.getElementById('projectsList').innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Please authenticate first to view projects
                </div>
            `;
            return;
        }

        try {
            const response = await axios.get('/projects/', {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });

            if (response.data.success) {
                // API returns { data: { items: [...], total_items: N } }
                allProjects = response.data.data.items || [];
                displayProjects(allProjects);
            }
        } catch (error) {
            document.getElementById('projectsList').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error loading projects: ${error.response?.data?.message || error.message}
                </div>
            `;
        }
    }
    
    // Display projects
    function displayProjects(projects) {
        if (projects.length === 0) {
            document.getElementById('projectsList').innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-folder-open me-2"></i>No projects found
                </div>
            `;
            return;
        }
        
        const projectsHtml = projects.map(project => `
            <div class="card mb-3 project-card" data-status="${project.status}">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h6 class="mb-1">${project.name}</h6>
                            <p class="mb-1 text-muted">${project.description || 'No description'}</p>
                            <div class="d-flex gap-2 mb-2">
                                <span class="badge bg-${getStatusColor(project.status)}">${project.status}</span>
                                <span class="badge bg-${getPriorityColor(project.priority)}">${project.priority}</span>
                                ${project.is_github_synced ? '<span class="badge bg-dark"><i class="fab fa-github me-1"></i>GitHub</span>' : ''}
                                ${project.is_jira_synced ? '<span class="badge bg-primary"><i class="fab fa-atlassian me-1"></i>Jira</span>' : ''}
                            </div>
                            <small class="text-muted">
                                Created: ${new Date(project.created_at).toLocaleDateString()}
                                ${project.start_date ? ` | Start: ${new Date(project.start_date).toLocaleDateString()}` : ''}
                                ${project.end_date ? ` | End: ${new Date(project.end_date).toLocaleDateString()}` : ''}
                            </small>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-primary" onclick="viewProject(${project.id})">
                                    <i class="fas fa-eye"></i> View
                                </button>
                                <button class="btn btn-sm btn-outline-success" onclick="viewProjectTasks(${project.id})">
                                    <i class="fas fa-tasks"></i> Tasks
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteProject(${project.id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
        
        document.getElementById('projectsList').innerHTML = projectsHtml;
    }
    
    // Helper functions
    function getStatusColor(status) {
        const colors = {
            'planning': 'secondary',
            'active': 'success',
            'on_hold': 'warning',
            'completed': 'info'
        };
        return colors[status] || 'secondary';
    }
    
    function getPriorityColor(priority) {
        const colors = {
            'low': 'success',
            'medium': 'warning',
            'high': 'danger',
            'urgent': 'dark'
        };
        return colors[priority] || 'secondary';
    }
    
    // Filter projects
    function filterProjects(filter) {
        currentFilter = filter;
        let filteredProjects = allProjects;
        
        switch (filter) {
            case 'active':
                filteredProjects = allProjects.filter(p => p.status === 'active');
                break;
            case 'completed':
                filteredProjects = allProjects.filter(p => p.status === 'completed');
                break;
            case 'github':
                filteredProjects = allProjects.filter(p => p.is_github_synced);
                break;
            case 'jira':
                filteredProjects = allProjects.filter(p => p.is_jira_synced);
                break;
        }
        
        displayProjects(filteredProjects);
    }
    
    // View project details
    async function viewProject(projectId) {
        if (!currentToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }
        
        try {
            const response = await axios.get(`/projects/${projectId}`, {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });
            
            if (response.data.success) {
                const project = response.data.data;
                
                document.getElementById('projectModalTitle').textContent = project.name;
                document.getElementById('projectModalBody').innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Basic Information</h6>
                            <p><strong>Name:</strong> ${project.name}</p>
                            <p><strong>Description:</strong> ${project.description || 'No description'}</p>
                            <p><strong>Status:</strong> <span class="badge bg-${getStatusColor(project.status)}">${project.status}</span></p>
                            <p><strong>Priority:</strong> <span class="badge bg-${getPriorityColor(project.priority)}">${project.priority}</span></p>
                        </div>
                        <div class="col-md-6">
                            <h6>Dates</h6>
                            <p><strong>Created:</strong> ${new Date(project.created_at).toLocaleString()}</p>
                            <p><strong>Start Date:</strong> ${project.start_date ? new Date(project.start_date).toLocaleDateString() : 'Not set'}</p>
                            <p><strong>End Date:</strong> ${project.end_date ? new Date(project.end_date).toLocaleDateString() : 'Not set'}</p>
                            <p><strong>Last Updated:</strong> ${new Date(project.updated_at).toLocaleString()}</p>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>Integration Status</h6>
                            <p><strong>GitHub:</strong> ${project.is_github_synced ? `✅ Synced (${project.github_repo_name})` : '❌ Not synced'}</p>
                            <p><strong>Jira:</strong> ${project.is_jira_synced ? `✅ Synced (${project.jira_project_key})` : '❌ Not synced'}</p>
                        </div>
                    </div>
                `;
                
                const modal = new bootstrap.Modal(document.getElementById('projectModal'));
                modal.show();
            }
        } catch (error) {
            showAlert('Error loading project details: ' + (error.response?.data?.message || error.message), 'danger');
        }
    }
    
    // View project tasks
    function viewProjectTasks(projectId) {
        window.location.href = `/dashboard/tasks-manager?project_id=${projectId}`;
    }
    
    // Delete project
    async function deleteProject(projectId) {
        if (!currentToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }
        
        if (!confirm('Are you sure you want to delete this project?')) {
            return;
        }
        
        try {
            const response = await axios.delete(`/projects/${projectId}`, {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });
            
            if (response.data.success) {
                showAlert('Project deleted successfully!', 'success');
                loadProjects();
                loadProjectStats();
            }
        } catch (error) {
            showAlert('Error deleting project: ' + (error.response?.data?.message || error.message), 'danger');
        }
    }
    
    // Load project statistics
    async function loadProjectStats() {
        try {
            const response = await axios.get('/dashboard/api/stats');
            const stats = response.data;
            
            document.getElementById('totalProjects').textContent = stats.total_projects || 0;
            
            // Calculate other stats from allProjects
            if (allProjects.length > 0) {
                const activeCount = allProjects.filter(p => p.status === 'active').length;
                const githubCount = allProjects.filter(p => p.is_github_synced).length;
                const jiraCount = allProjects.filter(p => p.is_jira_synced).length;
                
                document.getElementById('activeProjects').textContent = activeCount;
                document.getElementById('githubProjects').textContent = githubCount;
                document.getElementById('jiraProjects').textContent = jiraCount;
            }
        } catch (error) {
            console.error('Error loading project stats:', error);
        }
    }
    
    // Load GitHub repositories
    async function loadGitHubRepos() {
        if (!currentToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }
        
        try {
            showAlert('Loading GitHub repositories...', 'info');
            
            const response = await axios.get('/projects/github/repositories', {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });
            
            if (response.data.success) {
                const repos = response.data.data.repositories;
                
                if (repos.length === 0) {
                    document.getElementById('githubRepos').innerHTML = `
                        <div class="text-muted">No repositories found</div>
                    `;
                    return;
                }
                
                const reposHtml = repos.slice(0, 5).map(repo => `
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                        <div>
                            <strong>${repo.name}</strong>
                            <br><small class="text-muted">${repo.description || 'No description'}</small>
                        </div>
                        <button class="btn btn-sm btn-outline-primary" onclick="importGitHubRepo('${repo.full_name}')">
                            Import
                        </button>
                    </div>
                `).join('');
                
                document.getElementById('githubRepos').innerHTML = reposHtml;
                showAlert(`Loaded ${repos.length} repositories`, 'success');
            }
        } catch (error) {
            document.getElementById('githubRepos').innerHTML = `
                <div class="text-danger">Error: ${error.response?.data?.message || error.message}</div>
            `;
        }
    }
    
    // Load Jira projects
    async function loadJiraProjects() {
        if (!currentToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }
        
        try {
            showAlert('Loading Jira projects...', 'info');
            
            const response = await axios.get('/webhooks/jira/projects', {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });
            
            if (response.data.success) {
                const projects = response.data.data.projects;
                
                if (projects.length === 0) {
                    document.getElementById('jiraProjects').innerHTML = `
                        <div class="text-muted">No projects found</div>
                    `;
                    return;
                }
                
                const projectsHtml = projects.slice(0, 5).map(project => `
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                        <div>
                            <strong>${project.name}</strong> (${project.key})
                            <br><small class="text-muted">${project.description || 'No description'}</small>
                        </div>
                        <button class="btn btn-sm btn-outline-primary" onclick="importJiraProject('${project.key}')">
                            Import
                        </button>
                    </div>
                `).join('');
                
                document.getElementById('jiraProjects').innerHTML = projectsHtml;
                showAlert(`Loaded ${projects.length} projects`, 'success');
            }
        } catch (error) {
            document.getElementById('jiraProjects').innerHTML = `
                <div class="text-danger">Error: ${error.response?.data?.message || error.message}</div>
            `;
        }
    }
    
    // Import GitHub repository
    async function importGitHubRepo(repoFullName) {
        if (!currentToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }
        
        try {
            showAlert(`Importing ${repoFullName}...`, 'info');
            
            const response = await axios.post(`/projects/github/repositories/${encodeURIComponent(repoFullName)}/sync`, {}, {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });
            
            if (response.data.success) {
                showAlert(`Repository ${repoFullName} imported successfully!`, 'success');
                loadProjects();
                loadProjectStats();
            }
        } catch (error) {
            showAlert(`Error importing repository: ${error.response?.data?.message || error.message}`, 'danger');
        }
    }
    
    // Import Jira project
    async function importJiraProject(projectKey) {
        if (!currentToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }
        
        try {
            showAlert(`Importing ${projectKey}...`, 'info');
            
            const response = await axios.post(`/webhooks/jira/projects/${projectKey}/sync`, {}, {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });
            
            if (response.data.success) {
                showAlert(`Project ${projectKey} imported successfully!`, 'success');
                loadProjects();
                loadProjectStats();
            }
        } catch (error) {
            showAlert(`Error importing project: ${error.response?.data?.message || error.message}`, 'danger');
        }
    }
</script>
{% endblock %}
