{% extends "dashboard/base.html" %}

{% block page_title %}Authentication Manager{% endblock %}
{% block page_description %}Manage user authentication, login, and OAuth integrations{% endblock %}

{% block content %}
<!-- Login Section -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-sign-in-alt me-2"></i>User Login</h5>
            </div>
            <div class="card-body">
                <form id="loginForm">
                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <input type="email" class="form-control" id="loginEmail" placeholder="<EMAIL>">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Password</label>
                        <input type="password" class="form-control" id="loginPassword" placeholder="Password">
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt me-2"></i>Login
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-plus me-2"></i>User Registration</h5>
            </div>
            <div class="card-body">
                <form id="registerForm">
                    <div class="mb-3">
                        <label class="form-label">Username</label>
                        <input type="text" class="form-control" id="registerUsername" placeholder="username">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <input type="email" class="form-control" id="registerEmail" placeholder="<EMAIL>">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Password</label>
                        <input type="password" class="form-control" id="registerPassword" placeholder="Password">
                    </div>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-user-plus me-2"></i>Register
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Current Authentication Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-check me-2"></i>Current Authentication Status</h5>
            </div>
            <div class="card-body">
                <div id="authStatus">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Checking...</span>
                        </div>
                        <p class="mt-2">Checking authentication status...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- OAuth Integrations -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fab fa-github me-2"></i>GitHub OAuth</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Connect your GitHub account for repository integration.</p>
                <div class="d-grid gap-2">
                    <button class="btn btn-dark" onclick="startGitHubOAuth()">
                        <i class="fab fa-github me-2"></i>Connect GitHub
                    </button>
                    <button class="btn btn-outline-info" onclick="getGitHubOAuthUrl()">
                        <i class="fas fa-link me-2"></i>Get OAuth URL
                    </button>
                    <button class="btn btn-outline-success" onclick="checkGitHubStatus()">
                        <i class="fas fa-sync me-2"></i>Check Status
                    </button>
                </div>
                <div id="githubStatus" class="mt-3"></div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fab fa-atlassian me-2"></i>Jira OAuth</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Connect your Jira account for issue management.</p>
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="startJiraOAuth()">
                        <i class="fab fa-atlassian me-2"></i>Connect Jira
                    </button>
                    <button class="btn btn-outline-info" onclick="getJiraOAuthUrl()">
                        <i class="fas fa-link me-2"></i>Get OAuth URL
                    </button>
                    <button class="btn btn-outline-success" onclick="checkJiraStatus()">
                        <i class="fas fa-sync me-2"></i>Check Status
                    </button>
                </div>
                <div id="jiraStatus" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>

<!-- Token Management -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-key me-2"></i>Token Management</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label class="form-label">JWT Token</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="jwtToken" placeholder="Enter your JWT token">
                                <button class="btn btn-outline-secondary" type="button" onclick="toggleTokenVisibility()">
                                    <i class="fas fa-eye" id="tokenEye"></i>
                                </button>
                            </div>
                            <div class="form-text">This token will be used for authenticated API requests.</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="saveToken()">
                                <i class="fas fa-save me-2"></i>Save Token
                            </button>
                            <button class="btn btn-outline-danger" onclick="clearToken()">
                                <i class="fas fa-trash me-2"></i>Clear Token
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Profile -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user me-2"></i>User Profile</h5>
            </div>
            <div class="card-body">
                <div id="userProfile">
                    <div class="text-center text-muted">
                        <i class="fas fa-info-circle me-2"></i>Login to view profile information
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentToken = '';
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        // Load saved token
        const savedToken = localStorage.getItem('tms_auth_token');
        if (savedToken) {
            document.getElementById('jwtToken').value = savedToken;
            currentToken = savedToken;
            checkAuthStatus();
        } else {
            checkAuthStatus();
        }
        
        // Auto-save token
        document.getElementById('jwtToken').addEventListener('input', function() {
            currentToken = this.value;
        });
    });
    
    // Login form
    document.getElementById('loginForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const email = document.getElementById('loginEmail').value;
        const password = document.getElementById('loginPassword').value;
        
        if (!email || !password) {
            showAlert('Please enter both email and password', 'warning');
            return;
        }
        
        try {
            showAlert('Logging in...', 'info');
            
            const response = await axios.post('/auth/login', {
                email: email,
                password: password
            });
            
            if (response.data.success) {
                const token = response.data.data.access_token;
                document.getElementById('jwtToken').value = token;
                currentToken = token;
                localStorage.setItem('tms_auth_token', token);
                
                showAlert('Login successful!', 'success');
                checkAuthStatus();
                
                // Clear form
                document.getElementById('loginForm').reset();
            }
        } catch (error) {
            showAlert('Login failed: ' + (error.response?.data?.message || error.message), 'danger');
        }
    });
    
    // Register form
    document.getElementById('registerForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const username = document.getElementById('registerUsername').value;
        const email = document.getElementById('registerEmail').value;
        const password = document.getElementById('registerPassword').value;
        
        if (!username || !email || !password) {
            showAlert('Please fill in all fields', 'warning');
            return;
        }
        
        try {
            showAlert('Registering...', 'info');
            
            const response = await axios.post('/auth/register', {
                username: username,
                email: email,
                password: password
            });
            
            if (response.data.success) {
                showAlert('Registration successful! You can now login.', 'success');
                
                // Clear form
                document.getElementById('registerForm').reset();
            }
        } catch (error) {
            showAlert('Registration failed: ' + (error.response?.data?.message || error.message), 'danger');
        }
    });
    
    // Check auth status
    async function checkAuthStatus() {
        if (!currentToken) {
            document.getElementById('authStatus').innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Not authenticated. Please login or enter a JWT token.
                </div>
            `;
            document.getElementById('userProfile').innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>Login to view profile information
                </div>
            `;
            return;
        }
        
        try {
            const response = await axios.get('/users/profile', {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });
            
            if (response.data.success) {
                const user = response.data.data;
                
                document.getElementById('authStatus').innerHTML = `
                    <div class="alert alert-success">
                        <div class="row">
                            <div class="col-md-8">
                                <h6><i class="fas fa-check-circle me-2"></i>Authenticated</h6>
                                <p class="mb-1"><strong>User:</strong> ${user.username}</p>
                                <p class="mb-0"><strong>Email:</strong> ${user.email}</p>
                            </div>
                            <div class="col-md-4 text-end">
                                <span class="badge bg-success fs-6">Active</span>
                            </div>
                        </div>
                    </div>
                `;
                
                document.getElementById('userProfile').innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Basic Information</h6>
                            <p><strong>ID:</strong> ${user.id}</p>
                            <p><strong>Username:</strong> ${user.username}</p>
                            <p><strong>Email:</strong> ${user.email}</p>
                            <p><strong>Created:</strong> ${new Date(user.created_at).toLocaleString()}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>Integration Status</h6>
                            <p><strong>GitHub:</strong> ${user.github_username ? `Connected (${user.github_username})` : 'Not connected'}</p>
                            <p><strong>Jira:</strong> ${user.jira_display_name ? `Connected (${user.jira_display_name})` : 'Not connected'}</p>
                        </div>
                    </div>
                `;
            }
        } catch (error) {
            document.getElementById('authStatus').innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-times-circle me-2"></i>Authentication Failed</h6>
                    <p class="mb-0">Error: ${error.response?.data?.message || error.message}</p>
                </div>
            `;
            
            document.getElementById('userProfile').innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-exclamation-triangle me-2"></i>Unable to load profile
                </div>
            `;
        }
    }
    
    // Token management
    function toggleTokenVisibility() {
        const tokenInput = document.getElementById('jwtToken');
        const eyeIcon = document.getElementById('tokenEye');
        
        if (tokenInput.type === 'password') {
            tokenInput.type = 'text';
            eyeIcon.className = 'fas fa-eye-slash';
        } else {
            tokenInput.type = 'password';
            eyeIcon.className = 'fas fa-eye';
        }
    }
    
    function saveToken() {
        const token = document.getElementById('jwtToken').value;
        if (token) {
            currentToken = token;
            localStorage.setItem('tms_auth_token', token);
            showAlert('Token saved successfully!', 'success');
            checkAuthStatus();
        } else {
            showAlert('Please enter a token first', 'warning');
        }
    }
    
    function clearToken() {
        document.getElementById('jwtToken').value = '';
        currentToken = '';
        localStorage.removeItem('tms_auth_token');
        showAlert('Token cleared', 'info');
        checkAuthStatus();
    }
    
    // OAuth functions
    function startGitHubOAuth() {
        showAlert('Redirecting to GitHub OAuth...', 'info');
        window.open('/auth/github', '_blank');
    }
    
    function startJiraOAuth() {
        showAlert('Redirecting to Jira OAuth...', 'info');
        window.open('/auth/jira', '_blank');
    }
    
    async function getGitHubOAuthUrl() {
        try {
            const response = await axios.get('/auth/github/url');
            if (response.data.success) {
                showOAuthModal('GitHub', response.data.data.authorization_url);
            }
        } catch (error) {
            showAlert('Error getting GitHub OAuth URL: ' + error.message, 'danger');
        }
    }
    
    async function getJiraOAuthUrl() {
        try {
            const response = await axios.get('/auth/jira/url');
            if (response.data.success) {
                showOAuthModal('Jira', response.data.data.authorization_url);
            }
        } catch (error) {
            showAlert('Error getting Jira OAuth URL: ' + error.message, 'danger');
        }
    }
    
    function showOAuthModal(platform, url) {
        const modal = document.createElement('div');
        modal.innerHTML = `
            <div class="modal fade" id="oauthModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${platform} OAuth URL</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>Copy this URL and open it in a new tab to complete OAuth:</p>
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" value="${url}" readonly>
                                <button class="btn btn-outline-secondary" onclick="copyToClipboard('${url}')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                            </div>
                            <div class="d-grid">
                                <a href="${url}" target="_blank" class="btn btn-primary">
                                    <i class="fab fa-${platform.toLowerCase()} me-2"></i>Open ${platform} OAuth
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
        
        const bootstrapModal = new bootstrap.Modal(document.getElementById('oauthModal'));
        bootstrapModal.show();
        
        // Clean up modal after close
        document.getElementById('oauthModal').addEventListener('hidden.bs.modal', function() {
            document.body.removeChild(modal);
        });
    }
    
    async function checkGitHubStatus() {
        if (!currentToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }
        
        try {
            const response = await axios.get('/projects/github/repositories', {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });
            
            document.getElementById('githubStatus').innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>GitHub connected
                </div>
            `;
        } catch (error) {
            document.getElementById('githubStatus').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle me-2"></i>GitHub not connected
                </div>
            `;
        }
    }
    
    async function checkJiraStatus() {
        if (!currentToken) {
            showAlert('Please authenticate first', 'warning');
            return;
        }
        
        try {
            const response = await axios.get('/webhooks/jira/status', {
                headers: { 'Authorization': `Bearer ${currentToken}` }
            });
            
            document.getElementById('jiraStatus').innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>Jira connected
                </div>
            `;
        } catch (error) {
            document.getElementById('jiraStatus').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle me-2"></i>Jira not connected
                </div>
            `;
        }
    }
</script>
{% endblock %}
