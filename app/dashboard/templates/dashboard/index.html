{% extends "dashboard/base.html" %}

{% block page_title %}Dashboard Overview{% endblock %}
{% block page_description %}System statistics and quick actions{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                <h4 class="card-title" id="totalUsers">-</h4>
                <p class="card-text text-muted">Total Users</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-folder fa-2x text-success mb-2"></i>
                <h4 class="card-title" id="totalProjects">-</h4>
                <p class="card-text text-muted">Projects</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-tasks fa-2x text-warning mb-2"></i>
                <h4 class="card-title" id="totalTasks">-</h4>
                <p class="card-text text-muted">Tasks</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-plug fa-2x text-info mb-2"></i>
                <h4 class="card-title" id="totalIntegrations">-</h4>
                <p class="card-text text-muted">Integrations</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="d-grid">
                            <button class="btn btn-outline-primary" onclick="testAPI()">
                                <i class="fas fa-flask me-2"></i>Test API Connection
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="d-grid">
                            <a href="{{ url_for('dashboard.api_explorer') }}" class="btn btn-outline-success">
                                <i class="fas fa-search me-2"></i>Explore APIs
                            </a>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="d-grid">
                            <a href="/apidocs/" target="_blank" class="btn btn-outline-info">
                                <i class="fas fa-book me-2"></i>View Swagger Docs
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Integration Status -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fab fa-github me-2"></i>GitHub Integration</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <p class="mb-1">Active Integrations: <span id="githubIntegrations" class="badge bg-success">-</span></p>
                        <small class="text-muted">OAuth, Sync, Webhooks</small>
                    </div>
                    <a href="{{ url_for('dashboard.github_integration') }}" class="btn btn-sm btn-outline-primary">
                        Manage
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fab fa-atlassian me-2"></i>Jira Integration</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <p class="mb-1">Active Integrations: <span id="jiraIntegrations" class="badge bg-success">-</span></p>
                        <small class="text-muted">OAuth, Sync, Webhooks</small>
                    </div>
                    <a href="{{ url_for('dashboard.jira_integration') }}" class="btn btn-sm btn-outline-primary">
                        Manage
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-clock me-2"></i>System Status</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>API Endpoints</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check-circle text-success me-2"></i>Authentication: <span class="badge bg-success">Active</span></li>
                            <li><i class="fas fa-check-circle text-success me-2"></i>Projects: <span class="badge bg-success">Active</span></li>
                            <li><i class="fas fa-check-circle text-success me-2"></i>Tasks: <span class="badge bg-success">Active</span></li>
                            <li><i class="fas fa-check-circle text-success me-2"></i>GitHub: <span class="badge bg-success">Active</span></li>
                            <li><i class="fas fa-check-circle text-success me-2"></i>Jira: <span class="badge bg-success">Active</span></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Services</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-database text-success me-2"></i>Database: <span class="badge bg-success">Connected</span></li>
                            <li><i class="fas fa-server text-success me-2"></i>Flask App: <span class="badge bg-success">Running</span></li>
                            <li><i class="fas fa-globe text-success me-2"></i>Nginx: <span class="badge bg-success">Running</span></li>
                            <li><i class="fas fa-book text-success me-2"></i>Swagger: <span class="badge bg-success">Available</span></li>
                        </ul>
                    </div>
                </div>
                <div class="mt-3">
                    <small class="text-muted">Last updated: <span id="lastUpdated">-</span></small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Load dashboard statistics
    async function loadStats() {
        try {
            const response = await axios.get('/dashboard/api/stats');
            const stats = response.data;
            
            document.getElementById('totalUsers').textContent = stats.total_users || 0;
            document.getElementById('totalProjects').textContent = stats.total_projects || 0;
            document.getElementById('totalTasks').textContent = stats.total_tasks || 0;
            document.getElementById('githubIntegrations').textContent = stats.github_integrations || 0;
            document.getElementById('jiraIntegrations').textContent = stats.jira_integrations || 0;
            document.getElementById('totalIntegrations').textContent = 
                (stats.github_integrations || 0) + (stats.jira_integrations || 0);
            
            if (stats.last_updated) {
                const date = new Date(stats.last_updated);
                document.getElementById('lastUpdated').textContent = date.toLocaleString();
            }
            
        } catch (error) {
            console.error('Error loading stats:', error);
            showAlert('Error loading statistics', 'warning');
        }
    }

    // Load integration status for current user
    async function loadIntegrationStatus() {
        const token = localStorage.getItem('authToken');
        if (!token) {
            // Show not authenticated status
            document.getElementById('githubIntegrations').textContent = '0';
            document.getElementById('jiraIntegrations').textContent = '0';
            return;
        }

        try {
            const response = await axios.get('/dashboard/api/integrations/status', {
                headers: { 'Authorization': `Bearer ${token}` }
            });

            if (response.data.success) {
                const status = response.data.data;

                // Update GitHub status
                document.getElementById('githubIntegrations').textContent = status.github.connected ? '1' : '0';
                document.getElementById('githubIntegrations').className =
                    status.github.connected ? 'badge bg-success' : 'badge bg-secondary';

                // Update Jira status
                document.getElementById('jiraIntegrations').textContent = status.jira.connected ? '1' : '0';
                document.getElementById('jiraIntegrations').className =
                    status.jira.connected ? 'badge bg-success' : 'badge bg-secondary';
            }
        } catch (error) {
            console.error('Error loading integration status:', error);
            document.getElementById('githubIntegrations').textContent = '?';
            document.getElementById('jiraIntegrations').textContent = '?';
        }
    }

    // Check URL parameters for auth status
    function checkAuthStatus() {
        const urlParams = new URLSearchParams(window.location.search);
        const jiraAuth = urlParams.get('jira_auth');
        const githubAuth = urlParams.get('github_auth');
        const message = urlParams.get('message');

        if (jiraAuth === 'success') {
            showAlert('Jira authentication successful!', 'success');
            // Clean URL and reload integration status
            window.history.replaceState({}, document.title, window.location.pathname);
            setTimeout(() => loadIntegrationStatus(), 1000);
        } else if (jiraAuth === 'error') {
            showAlert(message || 'Jira authentication failed', 'danger');
            window.history.replaceState({}, document.title, window.location.pathname);
        }

        if (githubAuth === 'success') {
            showAlert('GitHub authentication successful!', 'success');
            window.history.replaceState({}, document.title, window.location.pathname);
            setTimeout(() => loadIntegrationStatus(), 1000);
        } else if (githubAuth === 'error') {
            showAlert(message || 'GitHub authentication failed', 'danger');
            window.history.replaceState({}, document.title, window.location.pathname);
        }
    }

    // Test API connection
    async function testAPI() {
        try {
            showAlert('Testing API connection...', 'info');
            
            const response = await axios.get('/');
            
            if (response.data && response.data.message) {
                showAlert('✅ API connection successful!', 'success');
            } else {
                showAlert('⚠️ API responded but with unexpected format', 'warning');
            }
            
        } catch (error) {
            console.error('API test failed:', error);
            showAlert('❌ API connection failed', 'danger');
        }
    }
    
    // Refresh button
    document.getElementById('refreshBtn').addEventListener('click', function() {
        loadStats();
        loadIntegrationStatus();
        showAlert('Dashboard refreshed', 'success');
    });

    // Load stats on page load
    document.addEventListener('DOMContentLoaded', function() {
        checkAuthStatus();
        loadStats();
        loadIntegrationStatus();
    });

    // Auto-refresh every 30 seconds
    setInterval(() => {
        loadStats();
        loadIntegrationStatus();
    }, 30000);
</script>
{% endblock %}
