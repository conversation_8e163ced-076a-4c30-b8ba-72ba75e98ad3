{% extends "dashboard/base.html" %}

{% block page_title %}Jira Integration{% endblock %}
{% block page_description %}Manage Jira integration, OAuth, projects, and sync{% endblock %}

{% block content %}
<!-- Integration Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fab fa-atlassian me-2"></i>Jira Integration Status</h5>
            </div>
            <div class="card-body">
                <div id="jiraStatus">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Checking Jira integration status...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- OAuth Authentication -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-key me-2"></i>OAuth Authentication</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6>Connect to Jira</h6>
                        <p class="text-muted">Authenticate with your Jira account to enable integration features.</p>
                        <div class="mb-3">
                            <label class="form-label">Authentication Token</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="authToken" placeholder="Enter your JWT token">
                                <button class="btn btn-outline-secondary" type="button" onclick="toggleTokenVisibility()">
                                    <i class="fas fa-eye" id="tokenEye"></i>
                                </button>
                            </div>
                            <div class="form-text">Get your token by completing the OAuth flow below.</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="startJiraOAuth()">
                                <i class="fab fa-atlassian me-2"></i>Connect to Jira
                            </button>
                            <button class="btn btn-outline-info" onclick="getOAuthUrl()">
                                <i class="fas fa-link me-2"></i>Get OAuth URL
                            </button>
                            <button class="btn btn-outline-success" onclick="checkJiraStatus()">
                                <i class="fas fa-sync me-2"></i>Check Status
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Jira Projects -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-folder me-2"></i>Jira Projects</h5>
                    <button class="btn btn-outline-primary" onclick="loadJiraProjects()">
                        <i class="fas fa-sync me-2"></i>Refresh Projects
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="jiraProjects">
                    <div class="text-center text-muted">
                        <i class="fas fa-info-circle me-2"></i>Connect to Jira to view projects
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Project Sync -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-sync me-2"></i>Project Synchronization</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Sync Jira Project to TMS</h6>
                        <div class="mb-3">
                            <label class="form-label">Jira Project Key</label>
                            <input type="text" class="form-control" id="projectKeySync" placeholder="e.g., PROJ, TEST">
                        </div>
                        <button class="btn btn-success" onclick="syncJiraProject()">
                            <i class="fas fa-download me-2"></i>Sync to TMS
                        </button>
                    </div>
                    <div class="col-md-6">
                        <h6>Recent Sync Activities</h6>
                        <div id="syncActivities">
                            <div class="text-muted">No sync activities yet</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Issue Management -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bug me-2"></i>Issue Management</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>Get Project Issues</h6>
                        <div class="mb-3">
                            <label class="form-label">Project Key</label>
                            <input type="text" class="form-control" id="projectKeyIssues" placeholder="PROJ">
                        </div>
                        <button class="btn btn-info" onclick="getProjectIssues()">
                            <i class="fas fa-list me-2"></i>Get Issues
                        </button>
                    </div>
                    <div class="col-md-4">
                        <h6>Sync Issue to Task</h6>
                        <div class="mb-2">
                            <label class="form-label">Issue Key</label>
                            <input type="text" class="form-control" id="issueKeySync" placeholder="PROJ-123">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">TMS Project ID</label>
                            <input type="number" class="form-control" id="tmsProjectId" placeholder="1">
                        </div>
                        <button class="btn btn-warning" onclick="syncIssueToTask()">
                            <i class="fas fa-arrow-right me-2"></i>Sync to Task
                        </button>
                    </div>
                    <div class="col-md-4">
                        <h6>Search Issues (JQL)</h6>
                        <div class="mb-3">
                            <label class="form-label">JQL Query</label>
                            <input type="text" class="form-control" id="jqlQuery" placeholder="project = PROJ AND status = 'To Do'">
                        </div>
                        <button class="btn btn-secondary" onclick="searchJiraIssues()">
                            <i class="fas fa-search me-2"></i>Search
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Results Display -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Results</h5>
                    <button class="btn btn-sm btn-outline-secondary" onclick="clearResults()">
                        <i class="fas fa-trash me-2"></i>Clear
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="resultsDisplay">
                    <div class="text-center text-muted">
                        <i class="fas fa-info-circle me-2"></i>Results will appear here
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let authToken = '';

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        // Load saved token
        const savedToken = localStorage.getItem('tms_auth_token');
        if (savedToken) {
            document.getElementById('authToken').value = savedToken;
            authToken = savedToken;
            checkJiraStatus();
        }

        // Auto-save token
        document.getElementById('authToken').addEventListener('input', function() {
            authToken = this.value;
            localStorage.setItem('tms_auth_token', authToken);
        });
    });

    // Toggle token visibility
    function toggleTokenVisibility() {
        const tokenInput = document.getElementById('authToken');
        const eyeIcon = document.getElementById('tokenEye');

        if (tokenInput.type === 'password') {
            tokenInput.type = 'text';
            eyeIcon.className = 'fas fa-eye-slash';
        } else {
            tokenInput.type = 'password';
            eyeIcon.className = 'fas fa-eye';
        }
    }

    // Start Jira OAuth
    function startJiraOAuth() {
        showAlert('Redirecting to Jira OAuth...', 'info');
        window.open('/auth/jira', '_blank');
    }

    // Get OAuth URL
    async function getOAuthUrl() {
        try {
            const response = await axios.get('/auth/jira/url');

            if (response.data.success) {
                const url = response.data.data.authorization_url;

                // Show modal with URL
                const modal = document.createElement('div');
                modal.innerHTML = `
                    <div class="modal fade" id="oauthModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Jira OAuth URL</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <p>Copy this URL and open it in a new tab to complete OAuth:</p>
                                    <div class="input-group mb-3">
                                        <input type="text" class="form-control" value="${url}" readonly>
                                        <button class="btn btn-outline-secondary" onclick="copyToClipboard('${url}')">
                                            <i class="fas fa-copy"></i> Copy
                                        </button>
                                    </div>
                                    <div class="d-grid">
                                        <a href="${url}" target="_blank" class="btn btn-primary">
                                            <i class="fab fa-atlassian me-2"></i>Open Jira OAuth
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);

                const bootstrapModal = new bootstrap.Modal(document.getElementById('oauthModal'));
                bootstrapModal.show();

                // Clean up modal after close
                document.getElementById('oauthModal').addEventListener('hidden.bs.modal', function() {
                    document.body.removeChild(modal);
                });
            }
        } catch (error) {
            showAlert('Error getting OAuth URL: ' + error.message, 'danger');
        }
    }

    // Check Jira status
    async function checkJiraStatus() {
        if (!authToken) {
            document.getElementById('jiraStatus').innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Please enter your authentication token first
                </div>
            `;
            return;
        }

        try {
            const response = await axios.get('/webhooks/jira/status', {
                headers: { 'Authorization': `Bearer ${authToken}` }
            });

            if (response.data.success) {
                const data = response.data.data;
                document.getElementById('jiraStatus').innerHTML = `
                    <div class="alert alert-success">
                        <div class="row">
                            <div class="col-md-8">
                                <h6><i class="fas fa-check-circle me-2"></i>Connected to Jira</h6>
                                <p class="mb-1"><strong>Platform:</strong> ${data.platform}</p>
                                <p class="mb-1"><strong>Cloud ID:</strong> ${data.cloud_id}</p>
                                <p class="mb-0"><strong>User ID:</strong> ${data.platform_user_id}</p>
                            </div>
                            <div class="col-md-4 text-end">
                                <span class="badge bg-success fs-6">Active</span>
                            </div>
                        </div>
                    </div>
                `;

                // Auto-load projects if connected
                loadJiraProjects();
            }
        } catch (error) {
            document.getElementById('jiraStatus').innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-times-circle me-2"></i>Not Connected</h6>
                    <p class="mb-0">Error: ${error.response?.data?.message || error.message}</p>
                </div>
            `;
        }
    }
</script>
{% endblock %}
