{% extends "dashboard/base.html" %}

{% block page_title %}API Explorer{% endblock %}
{% block page_description %}Browse and explore all available API endpoints{% endblock %}

{% block content %}
<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="input-group">
            <span class="input-group-text"><i class="fas fa-search"></i></span>
            <input type="text" class="form-control" id="searchInput" placeholder="Search endpoints...">
        </div>
    </div>
    <div class="col-md-4">
        <select class="form-select" id="methodFilter">
            <option value="">All Methods</option>
            <option value="GET">GET</option>
            <option value="POST">POST</option>
            <option value="PUT">PUT</option>
            <option value="DELETE">DELETE</option>
            <option value="PATCH">PATCH</option>
        </select>
    </div>
</div>

<!-- API Endpoints by Category -->
{% for tag, endpoints in endpoints_by_tag.items() %}
<div class="card mb-4 category-section" data-category="{{ tag }}">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-folder-open me-2"></i>{{ tag }}
                <span class="badge bg-secondary ms-2">{{ endpoints|length }}</span>
            </h5>
            <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" 
                    data-bs-target="#collapse{{ loop.index }}" aria-expanded="true">
                <i class="fas fa-chevron-down"></i>
            </button>
        </div>
    </div>
    <div class="collapse show" id="collapse{{ loop.index }}">
        <div class="card-body">
            {% for endpoint in endpoints %}
            <div class="card endpoint-card {{ endpoint.method.lower() }} mb-3" 
                 data-method="{{ endpoint.method }}" 
                 data-path="{{ endpoint.path }}"
                 data-summary="{{ endpoint.summary }}">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method-badge method-{{ endpoint.method.lower() }} me-2">
                                    {{ endpoint.method }}
                                </span>
                                <code class="text-dark">{{ endpoint.path }}</code>
                                {% if endpoint.security %}
                                <i class="fas fa-lock text-warning ms-2" title="Requires authentication"></i>
                                {% endif %}
                            </div>
                            <h6 class="card-title mb-1">{{ endpoint.summary }}</h6>
                            {% if endpoint.description %}
                            <p class="card-text text-muted small">{{ endpoint.description[:100] }}{% if endpoint.description|length > 100 %}...{% endif %}</p>
                            {% endif %}
                        </div>
                        <div class="ms-3">
                            <button class="btn btn-sm btn-outline-primary" 
                                    onclick="showEndpointDetails('{{ endpoint.method }}', '{{ endpoint.path }}', {{ endpoint|tojson|e }})">
                                <i class="fas fa-eye"></i> Details
                            </button>
                            <button class="btn btn-sm btn-primary" 
                                    onclick="testEndpoint('{{ endpoint.method }}', '{{ endpoint.path }}')">
                                <i class="fas fa-play"></i> Test
                            </button>
                        </div>
                    </div>
                    
                    {% if endpoint.parameters %}
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-cog me-1"></i>
                            Parameters: 
                            {% for param in endpoint.parameters[:3] %}
                                <span class="badge bg-light text-dark">{{ param.name }}</span>
                            {% endfor %}
                            {% if endpoint.parameters|length > 3 %}
                                <span class="badge bg-light text-dark">+{{ endpoint.parameters|length - 3 }} more</span>
                            {% endif %}
                        </small>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endfor %}

<!-- Endpoint Details Modal -->
<div class="modal fade" id="endpointModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="endpointModalTitle">Endpoint Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="endpointModalBody">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="testFromModal">Test Endpoint</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentEndpoint = null;
    
    // Search functionality
    document.getElementById('searchInput').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        filterEndpoints();
    });
    
    // Method filter
    document.getElementById('methodFilter').addEventListener('change', function() {
        filterEndpoints();
    });
    
    function filterEndpoints() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const methodFilter = document.getElementById('methodFilter').value;
        
        const endpoints = document.querySelectorAll('.endpoint-card');
        let visibleCount = 0;
        
        endpoints.forEach(endpoint => {
            const method = endpoint.dataset.method;
            const path = endpoint.dataset.path.toLowerCase();
            const summary = endpoint.dataset.summary.toLowerCase();
            
            const matchesSearch = !searchTerm || 
                path.includes(searchTerm) || 
                summary.includes(searchTerm) ||
                method.toLowerCase().includes(searchTerm);
            
            const matchesMethod = !methodFilter || method === methodFilter;
            
            if (matchesSearch && matchesMethod) {
                endpoint.style.display = 'block';
                visibleCount++;
            } else {
                endpoint.style.display = 'none';
            }
        });
        
        // Show/hide category sections
        document.querySelectorAll('.category-section').forEach(section => {
            const visibleEndpoints = section.querySelectorAll('.endpoint-card[style="display: block;"], .endpoint-card:not([style])');
            if (visibleEndpoints.length === 0) {
                section.style.display = 'none';
            } else {
                section.style.display = 'block';
            }
        });
    }
    
    // Show endpoint details
    function showEndpointDetails(method, path, endpoint) {
        currentEndpoint = { method, path, ...endpoint };
        
        const modal = new bootstrap.Modal(document.getElementById('endpointModal'));
        document.getElementById('endpointModalTitle').innerHTML = 
            `<span class="method-badge method-${method.toLowerCase()} me-2">${method}</span> ${path}`;
        
        let parametersHtml = '';
        if (endpoint.parameters && endpoint.parameters.length > 0) {
            parametersHtml = `
                <h6>Parameters:</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th>In</th>
                                <th>Required</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${endpoint.parameters.map(param => `
                                <tr>
                                    <td><code>${param.name}</code></td>
                                    <td>${param.type || param.schema?.type || 'object'}</td>
                                    <td><span class="badge bg-info">${param.in}</span></td>
                                    <td>${param.required ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-muted"></i>'}</td>
                                    <td>${param.description || '-'}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }
        
        let responsesHtml = '';
        if (endpoint.responses) {
            responsesHtml = `
                <h6>Responses:</h6>
                <div class="accordion" id="responsesAccordion">
                    ${Object.entries(endpoint.responses).map(([code, response], index) => `
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button ${index > 0 ? 'collapsed' : ''}" type="button" 
                                        data-bs-toggle="collapse" data-bs-target="#response${code}">
                                    <span class="badge bg-${code.startsWith('2') ? 'success' : code.startsWith('4') ? 'warning' : 'danger'} me-2">${code}</span>
                                    ${response.description || 'Response'}
                                </button>
                            </h2>
                            <div id="response${code}" class="accordion-collapse collapse ${index === 0 ? 'show' : ''}" 
                                 data-bs-parent="#responsesAccordion">
                                <div class="accordion-body">
                                    <p>${response.description}</p>
                                    ${response.schema ? `<pre><code class="language-json">${JSON.stringify(response.schema, null, 2)}</code></pre>` : ''}
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }
        
        document.getElementById('endpointModalBody').innerHTML = `
            <div class="mb-3">
                <h6>Summary:</h6>
                <p>${endpoint.summary}</p>
            </div>
            
            ${endpoint.description ? `
                <div class="mb-3">
                    <h6>Description:</h6>
                    <p>${endpoint.description}</p>
                </div>
            ` : ''}
            
            ${endpoint.security && endpoint.security.length > 0 ? `
                <div class="mb-3">
                    <h6>Security:</h6>
                    <span class="badge bg-warning"><i class="fas fa-lock me-1"></i>Authentication Required</span>
                </div>
            ` : ''}
            
            ${parametersHtml}
            ${responsesHtml}
        `;
        
        modal.show();
        
        // Highlight code
        if (window.Prism) {
            Prism.highlightAll();
        }
    }
    
    // Test endpoint
    function testEndpoint(method, path) {
        // Redirect to API tester with pre-filled data
        const url = new URL('/dashboard/api-tester', window.location.origin);
        url.searchParams.set('method', method);
        url.searchParams.set('path', path);
        window.location.href = url.toString();
    }
    
    // Test from modal
    document.getElementById('testFromModal').addEventListener('click', function() {
        if (currentEndpoint) {
            testEndpoint(currentEndpoint.method, currentEndpoint.path);
        }
    });
</script>
{% endblock %}
