"""
Dashboard routes for API management interface
"""

from flask import render_template, request, jsonify, session, redirect, url_for, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity, verify_jwt_in_request
import requests
import json
import yaml
from datetime import datetime

from app.dashboard import dashboard_bp
from app.models.user import User
from app.models.integration import Integration
from app.models.project import Project
from app.models.task import Task


@dashboard_bp.route('/')
def index():
    """Main dashboard page"""
    return render_template('dashboard/index.html')


@dashboard_bp.route('/api-explorer')
def api_explorer():
    """API Explorer page - browse all endpoints"""
    try:
        # Load Swagger spec
        with open('app/swagger/openapi.yaml', 'r') as f:
            swagger_spec = yaml.safe_load(f)
        
        # Organize endpoints by tags
        endpoints_by_tag = {}
        paths = swagger_spec.get('paths', {})
        
        for path, methods in paths.items():
            for method, spec in methods.items():
                if method.upper() in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']:
                    tags = spec.get('tags', ['Other'])
                    tag = tags[0] if tags else 'Other'
                    
                    if tag not in endpoints_by_tag:
                        endpoints_by_tag[tag] = []
                    
                    endpoints_by_tag[tag].append({
                        'path': path,
                        'method': method.upper(),
                        'summary': spec.get('summary', ''),
                        'description': spec.get('description', ''),
                        'parameters': spec.get('parameters', []),
                        'security': spec.get('security', []),
                        'responses': spec.get('responses', {})
                    })
        
        return render_template('dashboard/api_explorer.html', 
                             endpoints_by_tag=endpoints_by_tag,
                             base_url=request.host_url.rstrip('/'))
    
    except Exception as e:
        current_app.logger.error(f"Error loading API explorer: {str(e)}")
        return render_template('dashboard/error.html', error=str(e))


@dashboard_bp.route('/auth-manager')
def auth_manager():
    """Authentication management page"""
    return render_template('dashboard/auth_manager.html')


@dashboard_bp.route('/github-integration')
def github_integration():
    """GitHub integration management"""
    return render_template('dashboard/github_integration.html')


@dashboard_bp.route('/jira-integration')
def jira_integration():
    """Jira integration management"""
    return render_template('dashboard/jira_integration.html')


@dashboard_bp.route('/projects-manager')
def projects_manager():
    """Projects management interface"""
    return render_template('dashboard/projects_manager.html')


@dashboard_bp.route('/tasks-manager')
def tasks_manager():
    """Tasks management interface"""
    return render_template('dashboard/tasks_manager.html')


@dashboard_bp.route('/api-tester')
def api_tester():
    """Interactive API testing interface"""
    return render_template('dashboard/api_tester.html')


@dashboard_bp.route('/webhooks-manager')
def webhooks_manager():
    """Webhooks management interface"""
    return render_template('dashboard/webhooks_manager.html')


# API Proxy endpoints for dashboard
@dashboard_bp.route('/api/proxy', methods=['GET', 'POST', 'PUT', 'DELETE', 'PATCH'])
def api_proxy():
    """Proxy API requests from dashboard"""
    try:
        # Get request details
        method = request.method
        endpoint = request.args.get('endpoint', '')
        
        if not endpoint:
            return jsonify({'error': 'Endpoint parameter required'}), 400
        
        # Build full URL
        base_url = request.host_url.rstrip('/')
        full_url = f"{base_url}{endpoint}"
        
        # Get headers
        headers = {}
        auth_token = request.headers.get('Authorization')
        if auth_token:
            headers['Authorization'] = auth_token
        
        if request.content_type:
            headers['Content-Type'] = request.content_type
        
        # Get request data
        data = None
        if method in ['POST', 'PUT', 'PATCH']:
            if request.is_json:
                data = request.get_json()
            else:
                data = request.get_data()
        
        # Make request
        response = requests.request(
            method=method,
            url=full_url,
            headers=headers,
            json=data if request.is_json else None,
            data=data if not request.is_json else None,
            timeout=30
        )
        
        # Return response
        try:
            response_data = response.json()
        except:
            response_data = {'text': response.text}
        
        return jsonify({
            'status_code': response.status_code,
            'headers': dict(response.headers),
            'data': response_data
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@dashboard_bp.route('/api/swagger-spec')
def get_swagger_spec():
    """Get Swagger specification"""
    try:
        with open('app/swagger/openapi.yaml', 'r') as f:
            swagger_spec = yaml.safe_load(f)
        return jsonify(swagger_spec)
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@dashboard_bp.route('/api/stats')
def get_dashboard_stats():
    """Get dashboard statistics"""
    try:
        stats = {
            'total_users': User.query.count(),
            'total_projects': Project.query.count(),
            'total_tasks': Task.query.count(),
            'github_integrations': Integration.query.filter_by(platform='github', is_active=True).count(),
            'jira_integrations': Integration.query.filter_by(platform='jira', is_active=True).count(),
            'last_updated': datetime.utcnow().isoformat()
        }
        return jsonify(stats)
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@dashboard_bp.route('/api/integrations/status')
@jwt_required()
def get_integrations_status():
    """Get integration status for current user"""
    try:
        user_id = int(get_jwt_identity())

        # Get GitHub integration
        github_integration = Integration.query.filter_by(
            user_id=user_id,
            platform='github',
            is_active=True
        ).first()

        # Get Jira integration
        jira_integration = Integration.query.filter_by(
            user_id=user_id,
            platform='jira',
            is_active=True
        ).first()

        status = {
            'github': {
                'connected': github_integration is not None,
                'platform_user_id': github_integration.platform_user_id if github_integration else None,
                'last_updated': github_integration.updated_at.isoformat() if github_integration else None
            },
            'jira': {
                'connected': jira_integration is not None,
                'platform_user_id': jira_integration.platform_user_id if jira_integration else None,
                'cloud_id': jira_integration.settings.get('cloud_id') if jira_integration and jira_integration.settings else None,
                'last_updated': jira_integration.updated_at.isoformat() if jira_integration else None
            }
        }

        return jsonify({
            'success': True,
            'data': status
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@dashboard_bp.route('/api/projects')
@jwt_required()
def get_user_projects():
    """Get projects for current user - simplified for dropdowns"""
    try:
        from flask_jwt_extended import get_jwt_identity

        current_user_id = int(get_jwt_identity())

        # Get projects where user is creator or owner
        projects = Project.query.filter(
            (Project.created_by == current_user_id) | (Project.user_id == current_user_id),
            Project.deleted_at.is_(None)
        ).all()

        projects_data = []
        for project in projects:
            projects_data.append({
                'id': project.id,
                'name': project.name,
                'description': project.description,
                'status': project.status,
                'priority': project.priority,
                'created_at': project.created_at.isoformat() if project.created_at else None
            })

        return jsonify({
            'success': True,
            'data': {
                'projects': projects_data,
                'count': len(projects_data)
            },
            'message': 'Projects retrieved successfully'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500
