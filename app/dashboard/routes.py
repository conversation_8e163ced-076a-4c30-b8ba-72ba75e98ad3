"""
Dashboard routes for API management interface
"""

from flask import render_template, request, jsonify, session, redirect, url_for, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity, verify_jwt_in_request
import requests
import json
import yaml
from datetime import datetime

from app.dashboard import dashboard_bp
from app.models.user import User
from app.models.integration import Integration
from app.models.project import Project
from app.models.task import Task


@dashboard_bp.route('/')
def index():
    """Main dashboard page"""
    return render_template('dashboard/index.html')


@dashboard_bp.route('/api-explorer')
def api_explorer():
    """API Explorer page - browse all endpoints"""
    try:
        # Load Swagger spec
        with open('app/swagger/openapi.yaml', 'r') as f:
            swagger_spec = yaml.safe_load(f)
        
        # Organize endpoints by tags
        endpoints_by_tag = {}
        paths = swagger_spec.get('paths', {})
        
        for path, methods in paths.items():
            for method, spec in methods.items():
                if method.upper() in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']:
                    tags = spec.get('tags', ['Other'])
                    tag = tags[0] if tags else 'Other'
                    
                    if tag not in endpoints_by_tag:
                        endpoints_by_tag[tag] = []
                    
                    endpoints_by_tag[tag].append({
                        'path': path,
                        'method': method.upper(),
                        'summary': spec.get('summary', ''),
                        'description': spec.get('description', ''),
                        'parameters': spec.get('parameters', []),
                        'security': spec.get('security', []),
                        'responses': spec.get('responses', {})
                    })
        
        return render_template('dashboard/api_explorer.html', 
                             endpoints_by_tag=endpoints_by_tag,
                             base_url=request.host_url.rstrip('/'))
    
    except Exception as e:
        current_app.logger.error(f"Error loading API explorer: {str(e)}")
        return render_template('dashboard/error.html', error=str(e))


@dashboard_bp.route('/auth-manager')
def auth_manager():
    """Authentication management page"""
    return render_template('dashboard/auth_manager.html')


@dashboard_bp.route('/github-integration')
def github_integration():
    """GitHub integration management"""
    return render_template('dashboard/github_integration.html')


@dashboard_bp.route('/jira-integration')
def jira_integration():
    """Jira integration management"""
    return render_template('dashboard/jira_integration.html')


@dashboard_bp.route('/projects-manager')
def projects_manager():
    """Projects management interface"""
    return render_template('dashboard/projects_manager.html')


@dashboard_bp.route('/tasks-manager')
def tasks_manager():
    """Tasks management interface"""
    return render_template('dashboard/tasks_manager.html')


@dashboard_bp.route('/api-tester')
def api_tester():
    """Interactive API testing interface"""
    return render_template('dashboard/api_tester.html')


@dashboard_bp.route('/webhooks-manager')
def webhooks_manager():
    """Webhooks management interface"""
    return render_template('dashboard/webhooks_manager.html')


# API Proxy endpoints for dashboard
@dashboard_bp.route('/api/proxy', methods=['GET', 'POST', 'PUT', 'DELETE', 'PATCH'])
def api_proxy():
    """Proxy API requests from dashboard"""
    try:
        # Get request details
        method = request.method
        endpoint = request.args.get('endpoint', '')
        
        if not endpoint:
            return jsonify({'error': 'Endpoint parameter required'}), 400
        
        # Build full URL
        base_url = request.host_url.rstrip('/')
        full_url = f"{base_url}{endpoint}"
        
        # Get headers
        headers = {}
        auth_token = request.headers.get('Authorization')
        if auth_token:
            headers['Authorization'] = auth_token
        
        if request.content_type:
            headers['Content-Type'] = request.content_type
        
        # Get request data
        data = None
        if method in ['POST', 'PUT', 'PATCH']:
            if request.is_json:
                data = request.get_json()
            else:
                data = request.get_data()
        
        # Make request
        response = requests.request(
            method=method,
            url=full_url,
            headers=headers,
            json=data if request.is_json else None,
            data=data if not request.is_json else None,
            timeout=30
        )
        
        # Return response
        try:
            response_data = response.json()
        except:
            response_data = {'text': response.text}
        
        return jsonify({
            'status_code': response.status_code,
            'headers': dict(response.headers),
            'data': response_data
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@dashboard_bp.route('/api/swagger-spec')
def get_swagger_spec():
    """Get Swagger specification"""
    try:
        with open('app/swagger/openapi.yaml', 'r') as f:
            swagger_spec = yaml.safe_load(f)
        return jsonify(swagger_spec)
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@dashboard_bp.route('/api/stats')
def get_dashboard_stats():
    """Get dashboard statistics"""
    try:
        stats = {
            'total_users': User.query.count(),
            'total_projects': Project.query.count(),
            'total_tasks': Task.query.count(),
            'github_integrations': Integration.query.filter_by(platform='github', is_active=True).count(),
            'jira_integrations': Integration.query.filter_by(platform='jira', is_active=True).count(),
            'last_updated': datetime.utcnow().isoformat()
        }
        return jsonify(stats)
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@dashboard_bp.route('/api/integrations/status')
@jwt_required()
def get_integrations_status():
    """Get integration status for current user"""
    try:
        user_id = int(get_jwt_identity())

        # Get GitHub integration
        github_integration = Integration.query.filter_by(
            user_id=user_id,
            platform='github',
            is_active=True
        ).first()

        # Get Jira integration
        jira_integration = Integration.query.filter_by(
            user_id=user_id,
            platform='jira',
            is_active=True
        ).first()

        status = {
            'github': {
                'connected': github_integration is not None,
                'platform_user_id': github_integration.platform_user_id if github_integration else None,
                'last_updated': github_integration.updated_at.isoformat() if github_integration else None
            },
            'jira': {
                'connected': jira_integration is not None,
                'platform_user_id': jira_integration.platform_user_id if jira_integration else None,
                'cloud_id': jira_integration.settings.get('cloud_id') if jira_integration and jira_integration.settings else None,
                'last_updated': jira_integration.updated_at.isoformat() if jira_integration else None
            }
        }

        return jsonify({
            'success': True,
            'data': status,
            'message': 'Integration status retrieved successfully'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@dashboard_bp.route('/api/projects')
@jwt_required()
def get_user_projects():
    """Get projects for current user - simplified for dropdowns"""
    try:
        from flask_jwt_extended import get_jwt_identity

        current_user_id = int(get_jwt_identity())

        # Get projects where user is creator or owner
        projects = Project.query.filter(
            (Project.created_by == current_user_id) | (Project.user_id == current_user_id),
            Project.deleted_at.is_(None)
        ).all()

        projects_data = []
        for project in projects:
            projects_data.append({
                'id': project.id,
                'name': project.name,
                'description': project.description,
                'status': project.status,
                'priority': project.priority,
                'created_at': project.created_at.isoformat() if project.created_at else None
            })

        return jsonify({
            'success': True,
            'data': {
                'projects': projects_data,
                'count': len(projects_data)
            },
            'message': 'Projects retrieved successfully'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@dashboard_bp.route('/api/jira/test-permissions')
@jwt_required()
def test_jira_permissions():
    """Test Jira API permissions with current user's integration"""
    try:
        from flask_jwt_extended import get_jwt_identity
        from app.webhooks.jira.api_client import JiraAPIClient

        user_id = int(get_jwt_identity())

        # Get Jira integration
        jira_integration = Integration.query.filter_by(
            user_id=user_id,
            platform='jira',
            is_active=True
        ).first()

        if not jira_integration:
            return jsonify({
                'success': False,
                'error': 'No active Jira integration found'
            }), 404

        # Get cloud_id from settings
        cloud_id = jira_integration.settings.get('cloud_id') if jira_integration.settings else None
        if not cloud_id:
            return jsonify({
                'success': False,
                'error': 'Jira cloud_id not found in integration settings'
            }), 400

        # Initialize Jira API client
        jira_client = JiraAPIClient(jira_integration.access_token, cloud_id)

        # Test various API calls to check permissions
        test_results = {}

        try:
            # Test 1: Get current user
            user_info = jira_client.get_current_user()
            test_results['current_user'] = {
                'success': True,
                'data': {
                    'accountId': user_info.get('accountId'),
                    'displayName': user_info.get('displayName'),
                    'emailAddress': user_info.get('emailAddress')
                }
            }
        except Exception as e:
            test_results['current_user'] = {'success': False, 'error': str(e)}

        try:
            # Test 2: Get projects
            projects = jira_client.get_projects()
            test_results['projects'] = {
                'success': True,
                'count': len(projects),
                'sample': projects[:3] if projects else []
            }
        except Exception as e:
            test_results['projects'] = {'success': False, 'error': str(e)}

        try:
            # Test 3: Get issues from first project (if any)
            if test_results.get('projects', {}).get('success') and test_results['projects'].get('count', 0) > 0:
                first_project = test_results['projects']['sample'][0] if test_results['projects']['sample'] else None
                if first_project and first_project.get('key'):
                    issues = jira_client.get_issues(first_project['key'], max_results=5)
                    test_results['issues'] = {
                        'success': True,
                        'project_key': first_project['key'],
                        'total': issues.get('total', 0),
                        'count': len(issues.get('issues', [])),
                        'sample': [
                            {
                                'key': issue.get('key'),
                                'summary': issue.get('fields', {}).get('summary'),
                                'status': issue.get('fields', {}).get('status', {}).get('name')
                            }
                            for issue in issues.get('issues', [])[:3]
                        ]
                    }
                else:
                    test_results['issues'] = {'success': False, 'error': 'No project key available'}
            else:
                test_results['issues'] = {'success': False, 'error': 'No projects available to test issues'}
        except Exception as e:
            test_results['issues'] = {'success': False, 'error': str(e)}

        return jsonify({
            'success': True,
            'data': {
                'integration_info': {
                    'user_id': user_id,
                    'platform_user_id': jira_integration.platform_user_id,
                    'cloud_id': cloud_id,
                    'is_active': jira_integration.is_active,
                    'updated_at': jira_integration.updated_at.isoformat()
                },
                'permission_tests': test_results
            },
            'message': 'Jira permissions tested successfully'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error testing Jira permissions: {str(e)}'
        }), 500


@dashboard_bp.route('/api/debug/integrations')
def debug_integrations():
    """Debug endpoint to check all integrations (no auth required)"""
    try:
        integrations = Integration.query.all()
        users = User.query.all()

        integration_data = []
        for integration in integrations:
            integration_data.append({
                'id': integration.id,
                'user_id': integration.user_id,
                'platform': integration.platform,
                'platform_user_id': integration.platform_user_id,
                'is_active': integration.is_active,
                'settings': integration.settings,
                'has_access_token': bool(integration.access_token),
                'token_length': len(integration.access_token) if integration.access_token else 0,
                'created_at': integration.created_at.isoformat() if integration.created_at else None,
                'updated_at': integration.updated_at.isoformat() if integration.updated_at else None
            })

        user_data = []
        for user in users:
            user_data.append({
                'id': user.id,
                'email': user.email,
                'jira_account_id': user.jira_account_id,
                'github_username': user.github_username,
                'auth_provider': user.auth_provider
            })

        return jsonify({
            'success': True,
            'data': {
                'integrations': integration_data,
                'users': user_data,
                'total_integrations': len(integration_data),
                'total_users': len(user_data)
            },
            'message': 'Debug data retrieved successfully'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error getting debug data: {str(e)}'
        }), 500


@dashboard_bp.route('/api/test/create-jira-integration')
def test_create_jira_integration():
    """Test endpoint to create a fake Jira integration for testing"""
    try:
        from app.auth.jira_services import create_or_update_jira_user

        # Fake Jira user data
        fake_jira_user = {
            'accountId': 'test-jira-account-123',
            'displayName': 'Test Jira User',
            'emailAddress': '<EMAIL>',
            'avatarUrls': {'48x48': 'https://example.com/avatar.png'}
        }

        # Fake tokens
        fake_access_token = 'fake-jira-access-token-for-testing'
        fake_cloud_id = 'fake-cloud-id-123'

        # Create integration
        response = create_or_update_jira_user(fake_jira_user, fake_access_token, fake_cloud_id)

        if response.success:
            jwt_token = response.data.get('access_token') if response.data else None
            return jsonify({
                'success': True,
                'data': {
                    'jwt_token': jwt_token,
                    'user_data': response.data.get('user') if response.data else None,
                    'message': 'Test Jira integration created successfully'
                },
                'redirect_url': f"/dashboard/?jira_auth=success&token={jwt_token}&message=Test Jira integration created"
            })
        else:
            return jsonify({
                'success': False,
                'error': response.message
            }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error creating test integration: {str(e)}'
        }), 500
