"""
Custom exceptions for the application
"""

class ValidationError(Exception):
    """Exception raised for validation errors"""
    pass

class JiraIntegrationError(Exception):
    """Exception raised for Jira integration errors"""
    pass

class ConfigurationError(Exception):
    """Exception raised for configuration errors"""
    pass

class GitHubIntegrationError(Exception):
    """Exception raised for GitHub integration errors"""
    pass

class WebhookError(Exception):
    """Exception raised for webhook errors"""
    pass