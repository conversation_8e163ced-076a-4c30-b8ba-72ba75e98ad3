from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from .services import upload_attachment, get_attachments_for_task, delete_attachment

from app.attachments import attachments_bp


@attachments_bp.route("/upload", methods=["POST"])
@jwt_required()
def upload_file():
    """
    Upload a file to a specific task
    ---
    tags:
      - Attachments
    consumes:
      - multipart/form-data
    security:
      - BearerAuth: []
    parameters:
      - name: task_id
        in: formData
        type: integer
        required: true
      - name: file
        in: formData
        type: file
        required: true
    responses:
      201:
        description: Attachment uploaded successfully
      400:
        description: Missing fields
      404:
        description: Task not found
    """
    current_user = get_jwt_identity()
    task_id = request.form.get("task_id")
    file = request.files.get("file")

    result = upload_attachment(task_id, file, current_user)
    return jsonify(result.to_dict()), result.code


@attachments_bp.route("/task/<int:task_id>", methods=["GET"])
@jwt_required()
def get_attachments(task_id):
    """
    Get all attachments for a task
    ---
    tags:
      - Attachments
    security:
      - BearerAuth: []
    parameters:
      - name: task_id
        in: path
        type: integer
        required: true
    responses:
      200:
        description: List of attachments
      404:
        description: Task not found
    """
    result = get_attachments_for_task(task_id)
    return jsonify(result.to_dict()), result.code


@attachments_bp.route("/<int:attachment_id>", methods=["DELETE"])
@jwt_required()
def delete_attachment_view(attachment_id):
    """
    Soft delete an attachment
    ---
    tags:
      - Attachments
    security:
      - BearerAuth: []
    parameters:
      - name: attachment_id
        in: path
        type: integer
        required: true
    responses:
      200:
        description: Attachment deleted
      404:
        description: Attachment not found
    """
    current_user = get_jwt_identity()
    result = delete_attachment(attachment_id, current_user)
    return jsonify(result.to_dict()), result.code
