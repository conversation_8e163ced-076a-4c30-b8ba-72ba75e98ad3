import os
from werkzeug.utils import secure_filename
from app.helpers.extensions import db
from app.models import Attachment, Task
from app.models.api_response import ApiResponse
from datetime import datetime

UPLOAD_FOLDER = "uploads"  # You can set this via config too

def upload_attachment(task_id, file, user_id):
    if not task_id or not file:
        return ApiResponse.failure("Missing task_id or file", code=400)

    task = db.session.get(Task, task_id)
    if not task:
        return ApiResponse.failure("Task not found", code=404)

    filename = secure_filename(file.filename)
    os.makedirs(UPLOAD_FOLDER, exist_ok=True)
    file_path = os.path.join(UPLOAD_FOLDER, filename)
    file.save(file_path)

    attachment = Attachment(
        task_id=task.id,
        file_url=file_path,
        file_name=filename,
        file_size=os.path.getsize(file_path),
        file_type=file.mimetype,
        uploaded_by=user_id
    )
    db.session.add(attachment)
    db.session.commit()

    return ApiResponse.success("Attachment uploaded", data=attachment.to_dict())

def get_attachments_for_task(task_id):
    task = db.session.get(Task, task_id)
    if not task:
        return ApiResponse.failure("Task not found", code=404)

    attachments = Attachment.query.filter_by(task_id=task_id, deleted_at=None).all()
    return ApiResponse.success(data=[a.to_dict() for a in attachments])

def delete_attachment(attachment_id, user_id):
    attachment = db.session.get(Attachment, attachment_id)
    if not attachment or attachment.deleted_at:
        return ApiResponse.failure("Attachment not found", code=404)

    attachment.deleted_at = datetime.utcnow()
    db.session.commit()
    return ApiResponse.success("Attachment deleted")
