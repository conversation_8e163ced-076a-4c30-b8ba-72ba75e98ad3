"""
Utility functions for activity logging
"""
from flask import request, g
from functools import wraps

from app.history.services import log_activity as log_activity_service


def with_activity_log(action, entity_type, description_template=None):
    """
    Decorator to log activity for route functions
    
    Args:
        action: Action name (create_task, update_project, etc.)
        entity_type: Entity type (task, project, user, system)
        description_template: Template string for description with {key} placeholders
        
    Example:
        @with_activity_log(
            action="create_task", 
            entity_type="task",
            description_template="Created task {task_name}"
        )
        def create_task():
            # Function implementation
            # Must set g.log_data = {"task_name": "My Task", "task_id": 123, ...}
            # Must set g.log_user_id = user.id
            pass
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Call the original function
            result = func(*args, **kwargs)
            
            try:
                # Check if log data and user ID were set
                if hasattr(g, 'log_data') and hasattr(g, 'log_user_id'):
                    log_data = g.log_data
                    user_id = g.log_user_id
                    
                    # Generate description
                    description = description_template
                    if description_template and isinstance(log_data, dict):
                        try:
                            description = description_template.format(**log_data)
                        except KeyError:
                            description = description_template
                    
                    # Get entity ID and project/task IDs if available
                    entity_id = log_data.get('entity_id') 
                    project_id = log_data.get('project_id')
                    task_id = log_data.get('task_id')
                    
                    # Log the activity
                    log_activity_service(
                        user_id=user_id,
                        action=action,
                        entity_type=entity_type,
                        description=description,
                        entity_id=entity_id,
                        project_id=project_id,
                        task_id=task_id,
                        details=log_data
                    )
            except Exception as e:
                # Don't fail the request if logging fails
                print(f"Failed to log activity: {str(e)}")
                
            return result
        return wrapper
    return decorator


def log_activity(
    user_id, action, entity_type, description, 
    entity_id=None, project_id=None, task_id=None, details=None
):
    """
    Proxy function to the actual log_activity service
    
    This is a convenience function to make it easier to import 
    and use the log_activity function directly.
    """
    return log_activity_service(
        user_id=user_id,
        action=action,
        entity_type=entity_type,
        description=description,
        entity_id=entity_id,
        project_id=project_id,
        task_id=task_id,
        details=details
    )
