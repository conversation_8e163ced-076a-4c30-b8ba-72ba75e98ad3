"""
History module for activity logging and audit trails
"""
from flask import Blueprint

# Import the Blueprint
from app.history.routes import history_bp

# Import the activity logger singleton
from app.history.logger import activity_logger
from app.history.utils import with_activity_log

# Make the Blueprint and logger available for import
__all__ = ['history_bp', 'activity_logger', 'with_activity_log']
