"""
Activity Logger Singleton
Provides a centralized logging service for the application
"""
from typing import Dict, Any, Optional
from app.history.services import log_activity as _log_activity


class ActivityLogger:
    """
    Singleton class for activity logging across the application
    """
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ActivityLogger, cls).__new__(cls)
        return cls._instance
    
    def log(
        self,
        user_id: int,
        action: str,
        entity_type: str,
        description: str,
        entity_id: Optional[int] = None,
        project_id: Optional[int] = None,
        task_id: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Log an activity
        
        Args:
            user_id: ID of the user performing the action
            action: Type of action (create_task, update_project, login, etc.)
            entity_type: Type of entity affected (task, project, user, system)
            description: Human-readable description of the activity
            entity_id: ID of the affected entity (optional)
            project_id: ID of the related project (optional)
            task_id: ID of the related task (optional)
            details: Additional structured data about the activity (optional)
        """
        return _log_activity(
            user_id=user_id,
            action=action,
            entity_type=entity_type,
            description=description,
            entity_id=entity_id,
            project_id=project_id,
            task_id=task_id,
            details=details
        )



activity_logger = ActivityLogger()
