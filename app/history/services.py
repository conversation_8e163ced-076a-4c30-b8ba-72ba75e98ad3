"""
History service for managing activity logs
"""
from datetime import datetime
from typing import List, Dict, Optional, Any
from flask import request
from sqlalchemy import desc
from app.models.system import ActivityLog
from app.helpers.extensions import db
from app.models.api_response import ApiResponse


def log_activity(
    user_id: int,
    action: str,
    entity_type: str,
    description: str,
    entity_id: Optional[int] = None,
    project_id: Optional[int] = None,
    task_id: Optional[int] = None,
    details: Optional[Dict[str, Any]] = None
) -> ActivityLog:
    """
    Log a user activity
    
    Args:
        user_id: ID of the user performing the action
        action: Type of action (create_task, update_project, login, etc.)
        entity_type: Type of entity affected (task, project, user, system)
        description: Human-readable description of the activity
        entity_id: ID of the affected entity (optional)
        project_id: ID of the related project (optional)
        task_id: ID of the related task (optional)
        details: Additional structured data about the activity (optional)
        
    Returns:
        Created ActivityLog instance
    """
    # Get request context information
    ip_address = request.remote_addr if request else None
    user_agent = request.user_agent.string if request and request.user_agent else None
    
    # Create activity log entry
    log = ActivityLog(
        user_id=user_id,
        action=action,
        entity_type=entity_type,
        entity_id=entity_id,
        project_id=project_id,
        task_id=task_id,
        description=description,
        details=details,
        ip_address=ip_address,
        user_agent=user_agent,
        created_at=datetime.utcnow()
    )
    
    # Add to database
    db.session.add(log)
    db.session.commit()
    
    return log


def get_logs_by_user_id(
    user_id: int,
    page: int = 1,
    per_page: int = 20
):
    """
    Get activity logs for a specific user with pagination
    
    Args:
        user_id: User ID to filter by
        page: Page number (1-indexed)
        per_page: Number of items per page
        
    Returns:
        ApiResponse with logs data and pagination info
    """
    try:
        # Calculate offset for pagination
        offset = (page - 1) * per_page
        
        # Query with pagination
        logs = ActivityLog.query.filter_by(user_id=user_id) \
            .order_by(desc(ActivityLog.created_at)) \
            .offset(offset) \
            .limit(per_page) \
            .all()
            
        # Get total count for pagination
        total_count = ActivityLog.query.filter_by(user_id=user_id).count()
        
        # Convert to dict and prepare response
        logs_data = [log.to_dict() for log in logs]
        
        return ApiResponse.paginated(
            items=logs_data,
            page=page,
            per_page=per_page,
            total_items=total_count,
            message="Activity logs retrieved successfully"
        )
        
    except Exception as e:
        return ApiResponse.failure(f"Failed to retrieve activity logs: {str(e)}", code=500)


def get_logs_by_project_id(
    project_id: int,
    page: int = 1,
    per_page: int = 20
):
    """
    Get activity logs for a specific project with pagination
    
    Args:
        project_id: Project ID to filter by
        page: Page number (1-indexed)
        per_page: Number of items per page
        
    Returns:
        ApiResponse with logs data and pagination info
    """
    try:
        # Calculate offset for pagination
        offset = (page - 1) * per_page
        
        # Query with pagination
        logs = ActivityLog.query.filter_by(project_id=project_id) \
            .order_by(desc(ActivityLog.created_at)) \
            .offset(offset) \
            .limit(per_page) \
            .all()
            
        # Get total count for pagination
        total_count = ActivityLog.query.filter_by(project_id=project_id).count()
        
        # Convert to dict and prepare response
        logs_data = [log.to_dict() for log in logs]
        
        return ApiResponse.paginated(
            items=logs_data,
            page=page,
            per_page=per_page,
            total_items=total_count,
            message="Activity logs retrieved successfully"
        )
        
    except Exception as e:
        return ApiResponse.failure(f"Failed to retrieve activity logs: {str(e)}", code=500)


def get_logs_by_task_id(
    task_id: int,
    page: int = 1,
    per_page: int = 20
):
    """
    Get activity logs for a specific task with pagination
    
    Args:
        task_id: Task ID to filter by
        page: Page number (1-indexed)
        per_page: Number of items per page
        
    Returns:
        ApiResponse with logs data and pagination info
    """
    try:
        # Calculate offset for pagination
        offset = (page - 1) * per_page
        
        # Query with pagination
        logs = ActivityLog.query.filter_by(task_id=task_id) \
            .order_by(desc(ActivityLog.created_at)) \
            .offset(offset) \
            .limit(per_page) \
            .all()
            
        # Get total count for pagination
        total_count = ActivityLog.query.filter_by(task_id=task_id).count()
        
        # Convert to dict and prepare response
        logs_data = [log.to_dict() for log in logs]
        
        return ApiResponse.paginated(
            items=logs_data,
            page=page,
            per_page=per_page,
            total_items=total_count,
            message="Activity logs retrieved successfully"
        )
        
    except Exception as e:
        return ApiResponse.failure(f"Failed to retrieve activity logs: {str(e)}", code=500)


def get_recent_logs(limit: int = 20):
    """
    Get recent activity logs across the system
    
    Args:
        limit: Maximum number of logs to retrieve
        
    Returns:
        ApiResponse with recent logs data using the pagination format
    """
    try:
        logs = ActivityLog.query \
            .order_by(desc(ActivityLog.created_at)) \
            .limit(limit) \
            .all()
            
        logs_data = [log.to_dict() for log in logs]
        
        total_count = len(logs_data)
        return ApiResponse.paginated(
            items=logs_data,
            page=1,
            per_page=limit,
            total_items=total_count,
            message="Recent activity logs retrieved successfully"
        )
        
    except Exception as e:
        return ApiResponse.failure(f"Failed to retrieve recent activity logs: {str(e)}", code=500)
