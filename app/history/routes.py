"""
Routes for history module
"""
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.models.constants import DEFAULT_PAGE, DEFAULT_PER_PAGE, MAX_PER_PAGE
from app.history.services import (
    get_logs_by_user_id,
    get_logs_by_project_id,
    get_logs_by_task_id,
    get_recent_logs
)
from app.auth.services import get_user_by_id
from app.models.api_response import ApiResponse

# Create Blueprint
history_bp = Blueprint("history", __name__, url_prefix="/history")


@history_bp.route("/user/<int:user_id>", methods=["GET"])
@jwt_required()
def get_user_activity(user_id):
    """
    Get activity logs for a specific user
    
    Args:
        user_id: User ID to get logs for
        
    Query Parameters:
        page: Page number (default: 1)
        per_page: Items per page (default: 20)
    """
    # Check permissions - users can only see their own logs unless they're admins
    current_user_id = int(get_jwt_identity())
    current_user = get_user_by_id(current_user_id)
    
    # Check if user has permission to view these logs
    if current_user_id != user_id:
        has_permission = False
        if current_user:
            try:
                has_permission = current_user.is_admin()
            except Exception:
                # If is_admin() fails, default to no permission
                has_permission = False
                
        if not has_permission:
            response = ApiResponse.failure("Permission denied", code=403)
            return jsonify(response.to_dict()), response.code
    
    # Get pagination parameters

    page = request.args.get('page', DEFAULT_PAGE, type=int)
    per_page = request.args.get('per_page', DEFAULT_PER_PAGE, type=int)
    
    # Validate pagination parameters
    if page < 1 or per_page < 1 or per_page > MAX_PER_PAGE:
        response = ApiResponse.failure("Invalid pagination parameters", code=400)
        return jsonify(response.to_dict()), response.code
    
    # Get logs
    response = get_logs_by_user_id(user_id, page, per_page)
    return jsonify(response.to_dict()), response.code


@history_bp.route("/project/<int:project_id>", methods=["GET"])
@jwt_required()
def get_project_activity(project_id):
    """
    Get activity logs for a specific project
    
    Args:
        project_id: Project ID to get logs for
        
    Query Parameters:
        page: Page number (default: 1)
        per_page: Items per page (default: 20)
    """
    # TODO: Add project permission check
    
    # Get pagination parameters
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    # Validate pagination parameters
    if page < 1 or per_page < 1 or per_page > 100:
        response = ApiResponse.failure("Invalid pagination parameters", code=400)
        return jsonify(response.to_dict()), response.code
    
    # Get logs
    response = get_logs_by_project_id(project_id, page, per_page)
    return jsonify(response.to_dict()), response.code


@history_bp.route("/task/<int:task_id>", methods=["GET"])
@jwt_required()
def get_task_activity(task_id):
    """
    Get activity logs for a specific task
    
    Args:
        task_id: Task ID to get logs for
        
    Query Parameters:
        page: Page number (default: 1)
        per_page: Items per page (default: 20)
    """
    # TODO: Add task permission check
    
    # Get pagination parameters
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    # Validate pagination parameters
    if page < 1 or per_page < 1 or per_page > 100:
        response = ApiResponse.failure("Invalid pagination parameters", code=400)
        return jsonify(response.to_dict()), response.code
    
    # Get logs
    response = get_logs_by_task_id(task_id, page, per_page)
    return jsonify(response.to_dict()), response.code


@history_bp.route("/recent", methods=["GET"])
@jwt_required()
def get_recent_activity():
    """
    Get recent activity logs across the system
    
    Query Parameters:
        limit: Maximum number of logs to retrieve (default: 20)
    """
    # TODO: Add admin permission check
    
    # Get limit parameter
    limit = request.args.get('limit', 20, type=int)
    
    # Validate limit parameter
    if limit < 1 or limit > 100:
        response = ApiResponse.failure("Invalid limit parameter", code=400)
        return jsonify(response.to_dict()), response.code
    
    # Get logs
    response = get_recent_logs(limit)
    return jsonify(response.to_dict()), response.code
