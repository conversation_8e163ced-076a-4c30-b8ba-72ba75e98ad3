"""
Pagination helper for API endpoints
"""
from typing import List, TypeVar, Generic, Dict, Any

T = TypeVar('T')

class Pagination:
    """
    Standardized pagination model for API responses
    
    Attributes:
        page: Current page number (1-indexed)
        per_page: Number of items per page
        total_items: Total number of items across all pages
        total_pages: Total number of pages
    """
    def __init__(self, page: int, per_page: int, total_items: int):
        """
        Initialize pagination object
        
        Args:
            page: Current page number (1-indexed)
            per_page: Number of items per page
            total_items: Total number of items
        """
        self.page = page
        self.per_page = per_page
        self.total_items = total_items
        self.total_pages = self._calculate_total_pages(total_items, per_page)
    
    @staticmethod
    def _calculate_total_pages(total_items: int, per_page: int) -> int:
        """Calculate total pages using ceil division"""
        return (total_items + per_page - 1) // per_page if per_page > 0 else 0
    
    def to_dict(self) -> Dict[str, int]:
        """Convert pagination object to dictionary"""
        return {
            "page": self.page,
            "per_page": self.per_page,
            "total_items": self.total_items,
            "total_pages": self.total_pages
        }


class PaginatedResponse(Generic[T]):
    """
    Generic response container for paginated data
    
    Attributes:
        items: List of paginated items
        pagination: Pagination information
    """
    def __init__(self, items: List[T], pagination: Pagination):
        """
        Initialize paginated response
        
        Args:
            items: List of paginated items
            pagination: Pagination information
        """
        self.items = items
        self.pagination = pagination
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert paginated response to dictionary"""
        return {
            "items": self.items,
            "pagination": self.pagination.to_dict()
        }
