from datetime import datetime
from app.models.task import Task
from app.helpers.extensions import db
from app.models.api_response import ApiResponse


def get_all_tasks(current_user_id, assignee_id=None):
    """
    Get all tasks (unassigned or assigned to a specific user), excluding deleted ones.
    """
    query = Task.query

    if assignee_id is not None:
        query = query.filter(Task.assignee_id == assignee_id)
    else:
        query = query.filter(Task.assignee_id.is_(None))

    query = query.filter(Task.deleted_at.is_(None))
    tasks = [task.to_dict() for task in query.all()]
    return ApiResponse.success(data=tasks)


def create_task(data, user_id):
    """
    Create a new task.
    """

    # Validate required fields according to openapi.yaml
    if not data or not data.get("title"):
        return ApiResponse.failure(
            "Title is required.",
            data={"error": "Missing required field: title"},
            code=400,
        )

    due_date = data.get("due_date")
    if due_date:
        try:
            due_date = datetime.fromisoformat(due_date)
        except ValueError:
            return ApiResponse.failure(
                "Invalid due_date format. Must be ISO 8601.", code=400
            )

    task = Task(
        title=data["title"],
        description=data.get("description"),
        status=data.get("status", "To Do"),
        est_time=data.get("est_time"),
        due_date=due_date,
        priority=data.get("priority", "Medium"),
        assignee_id=data.get("assignee_id"),
        project_id=data.get("project_id"),
        created_by=user_id,
    )

    db.session.add(task)
    db.session.commit()

    return ApiResponse.success(
        "Created task successfully", data={"id": task.id}, code=201
    )


def update_task(task_id, data, user_id):
    """
    Update an existing task. Only creator can update.
    """
    user_id = int(user_id)
    task = Task.query.get_or_404(task_id)

    if task.created_by != user_id:
        return ApiResponse.failure("Only task creator can update this task", code=403)

    for key, value in data.items():
        if key == "due_date" and value:
            try:
                value = datetime.fromisoformat(value)
            except ValueError:
                return ApiResponse.failure(
                    "Invalid due_date format. Must be ISO 8601.", code=400
                )
        setattr(task, key, value)

    db.session.commit()
    return ApiResponse.success("Task updated", data=task.to_dict())


def delete_task(task_id, user_id):
    """
    Soft delete a task. Only creator can delete.
    """
    user_id = int(user_id)
    task = Task.query.get_or_404(task_id)

    if task.created_by != user_id:
        return ApiResponse.failure("Only task creator can delete this task", code=403)

    task.deleted_at = datetime.utcnow()
    db.session.commit()
    return ApiResponse.success("Task deleted")
