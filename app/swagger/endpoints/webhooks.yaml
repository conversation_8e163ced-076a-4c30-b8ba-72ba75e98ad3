webhooks_create:
  tags:
    - Webhooks
  summary: Create GitHub webhooks for repositories
  description: Create webhooks for multiple GitHub repositories to enable automatic sync
  security:
    - BearerAuth: []
  requestBody:
    required: true
    content:
      application/json:
        schema:
          type: object
          required:
            - repository_urls
          properties:
            repository_urls:
              type: array
              items:
                type: string
                format: uri
              example:
                - "https://github.com/owner/repo1"
                - "https://github.com/owner/repo2"
              description: List of GitHub repository URLs
            webhook_url:
              type: string
              format: uri
              example: "https://your-domain.com/webhooks/github"
              description: Custom webhook URL (optional, defaults to current domain)
  responses:
    200:
      description: Webhooks created successfully
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ApiResponse'
              - type: object
                properties:
                  data:
                    type: object
                    properties:
                      created_webhooks:
                        type: array
                        items:
                          type: object
                          properties:
                            webhook_id:
                              type: integer
                            github_webhook_id:
                              type: string
                            repository:
                              type: string
                            events:
                              type: array
                              items:
                                type: string
                      errors:
                        type: array
                        items:
                          type: string
    400:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
    401:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
    500:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'

webhooks_list:
  tags:
    - Webhooks
  summary: List user's GitHub webhooks
  description: Get all active webhooks for the authenticated user
  security:
    - BearerAuth: []
  responses:
    200:
      description: Webhooks retrieved successfully
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ApiResponse'
              - type: object
                properties:
                  data:
                    type: object
                    properties:
                      webhooks:
                        type: array
                        items:
                          $ref: '#/components/schemas/GitHubWebhook'
    401:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
    500:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'

webhooks_delete:
  tags:
    - Webhooks
  summary: Delete a GitHub webhook
  description: Delete a specific webhook by ID
  security:
    - BearerAuth: []
  parameters:
    - name: webhook_id
      in: path
      required: true
      schema:
        type: integer
      description: Webhook ID to delete
  responses:
    200:
      description: Webhook deleted successfully
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
    404:
      description: Webhook not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
    401:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
    500:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'

webhooks_events:
  tags:
    - Webhooks
  summary: Get webhook events
  description: Get events for a specific webhook with pagination
  security:
    - BearerAuth: []
  parameters:
    - name: webhook_id
      in: path
      required: true
      schema:
        type: integer
      description: Webhook ID
    - name: page
      in: query
      schema:
        type: integer
        default: 1
      description: Page number
    - name: per_page
      in: query
      schema:
        type: integer
        default: 20
        maximum: 100
      description: Items per page
  responses:
    200:
      description: Webhook events retrieved successfully
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ApiResponse'
              - type: object
                properties:
                  data:
                    type: object
                    properties:
                      events:
                        type: array
                        items:
                          $ref: '#/components/schemas/WebhookEvent'
                      pagination:
                        type: object
                        properties:
                          page:
                            type: integer
                          per_page:
                            type: integer
                          total:
                            type: integer
                          pages:
                            type: integer
                          has_next:
                            type: boolean
                          has_prev:
                            type: boolean
    404:
      description: Webhook not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
    401:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
    500:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'

github_webhook_endpoint:
  tags:
    - Webhooks
  summary: GitHub webhook endpoint
  description: Endpoint for receiving webhook events from GitHub (called by GitHub, not users)
  requestBody:
    required: true
    content:
      application/json:
        schema:
          type: object
          description: GitHub webhook payload (varies by event type)
  responses:
    200:
      description: Webhook processed successfully
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
              data:
                type: object
    400:
      description: Bad request
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
    401:
      description: Invalid signature
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
    404:
      description: Webhook not found
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
    500:
      description: Internal server error
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
