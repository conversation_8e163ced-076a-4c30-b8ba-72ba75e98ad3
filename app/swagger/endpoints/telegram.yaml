# Telegram Integration Endpoints

/telegram/webhook:
  post:
    tags:
      - Telegram Integration
    summary: Telegram webhook endpoint
    description: |
      Endpoint for receiving webhook updates from Telegram Bot API.
      This endpoint is called by Telegram, not by users directly.
      
      **Note**: This endpoint does not require authentication as it's called by Telegram.
    security: []
    parameters:
      - name: X-Telegram-Bot-Api-Secret-Token
        in: header
        description: Secret token for webhook verification (optional)
        required: false
        type: string
      - in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/TelegramWebhookUpdate'
    responses:
      200:
        description: Webhook processed successfully
        schema:
          type: object
          properties:
            ok:
              type: boolean
              example: true
      400:
        description: Bad request - Invalid payload
        schema:
          type: object
          properties:
            error:
              type: string
              example: "No JSON payload"
      401:
        description: Unauthorized - Invalid webhook token
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Invalid webhook token"

/telegram/link:
  post:
    tags:
      - Telegram Integration
    summary: Link Telegram account to TMS account
    description: |
      Link user's Telegram account to their TMS account.
      This enables the user to receive notifications via Telegram.
      
      **How to get Telegram user data**:
      1. Start a conversation with your Telegram bot
      2. Send any message to the bot
      3. Use Telegram Bot API to get updates and extract user information
      4. Use the extracted data to call this endpoint
    security:
      - BearerAuth: []
    parameters:
      - in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/TelegramLinkRequest'
    responses:
      200:
        description: Telegram account linked successfully
        schema:
          $ref: '#/definitions/TelegramLinkResponse'
      400:
        description: Bad request - Invalid input or account already linked
        schema:
          $ref: '#/definitions/ErrorResponse'
      401:
        description: Unauthorized - Invalid or missing JWT token
        schema:
          $ref: '#/definitions/ErrorResponse'
      500:
        description: Internal server error
        schema:
          $ref: '#/definitions/ErrorResponse'

/telegram/unlink:
  post:
    tags:
      - Telegram Integration
    summary: Unlink Telegram account from TMS account
    description: |
      Remove the link between user's Telegram account and TMS account.
      This will stop all Telegram notifications and delete all subscription data.
    security:
      - BearerAuth: []
    responses:
      200:
        description: Telegram account unlinked successfully
        schema:
          $ref: '#/definitions/SuccessResponse'
      404:
        description: No Telegram account linked
        schema:
          $ref: '#/definitions/ErrorResponse'
      401:
        description: Unauthorized - Invalid or missing JWT token
        schema:
          $ref: '#/definitions/ErrorResponse'
      500:
        description: Internal server error
        schema:
          $ref: '#/definitions/ErrorResponse'

/telegram/status:
  get:
    tags:
      - Telegram Integration
    summary: Get Telegram integration status
    description: |
      Get the current user's Telegram integration status and active subscriptions.
      Returns information about linked account and notification preferences.
    security:
      - BearerAuth: []
    responses:
      200:
        description: Telegram status retrieved successfully
        schema:
          $ref: '#/definitions/TelegramStatusResponse'
      401:
        description: Unauthorized - Invalid or missing JWT token
        schema:
          $ref: '#/definitions/ErrorResponse'
      500:
        description: Internal server error
        schema:
          $ref: '#/definitions/ErrorResponse'

/telegram/subscribe:
  post:
    tags:
      - Telegram Integration
    summary: Manage notification subscriptions
    description: |
      Subscribe or unsubscribe from different types of notifications.
      
      **Available notification types**:
      - `github_notifications`: Receive notifications for GitHub webhook events (push, issues, PRs, etc.)
      
      **Prerequisites**:
      - Telegram account must be linked to TMS account
      - User must have started a conversation with the bot
    security:
      - BearerAuth: []
    parameters:
      - in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/TelegramSubscriptionRequest'
    responses:
      200:
        description: Subscription updated successfully
        schema:
          $ref: '#/definitions/TelegramSubscriptionResponse'
      400:
        description: Bad request - Invalid notification type or no Telegram account linked
        schema:
          $ref: '#/definitions/ErrorResponse'
      401:
        description: Unauthorized - Invalid or missing JWT token
        schema:
          $ref: '#/definitions/ErrorResponse'
      500:
        description: Internal server error
        schema:
          $ref: '#/definitions/ErrorResponse'
