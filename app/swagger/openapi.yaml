swagger: "2.0"
info:
  title: Task Management System API
  version: 1.0.0
  description: |
    API for user registration, login, and task management.

    ## Authentication
    This API uses JWT Bearer tokens for authentication. To access protected endpoints:
    1. <PERSON><PERSON> using `/auth/login` to get an access token
    2. Include the token in the Authorization header: `Bear<PERSON> {token}`

    ## Testing
    You can test the API endpoints directly from this documentation interface.

basePath: /
schemes:
  - http
  - https
produces:
  - application/json
consumes:
  - application/json

securityDefinitions:
  BearerAuth:
    type: apiKey
    name: Authorization
    in: header
    description: "JWT Authorization header using the Bearer scheme. Example: 'Bearer {token}'"

security:
  - BearerAuth: []

paths:
  # Health Check
  /:
    get:
      tags:
        - Health
      summary: API Health Check
      description: Check if the API is running
      security: []
      responses:
        200:
          description: API is running
          schema:
            type: object
            properties:
              message:
                type: string
                example: "API is running"

  /auth/login:
    post:
      tags:
        - Authentication
      summary: User login
      description: Authenticate user and return JWT token
      security: []
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/UserLogin'
      responses:
        200:
          description: Login successful, JWT token returned
          schema:
            $ref: '#/definitions/LoginResponse'
        400:
          description: Bad request - Missing required fields or invalid email format
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Invalid credentials
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/logout:
    post:
      tags:
        - Authentication
      summary: Logout user
      description: Revoke the current JWT token
      security:
        - BearerAuth: []
      responses:
        200:
          description: Logged out successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/register:
    post:
      tags:
        - Authentication
      summary: Register a new user
      description: Create a new user account
      security: []
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/UserRegister'
      responses:
        201:
          description: User created successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        400:
          description: Invalid input or user already exists
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/me:
    get:
      tags:
        - Authentication
      summary: Get current user info
      description: Retrieve information about the currently authenticated user
      security:
        - BearerAuth: []
      responses:
        200:
          description: User information retrieved successfully
          schema:
            $ref: '#/definitions/UserResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/github:
    get:
      tags:
        - Authentication
      summary: Initiate GitHub OAuth login
      description: |
        Redirect user to GitHub for OAuth authentication.

        **Note**: This endpoint returns a 302 redirect. In Swagger UI, you'll see an error because it cannot follow redirects automatically.

        **To test properly**:
        1. Copy the URL: `http://tms.uit.local:8084/auth/github`
        2. Open it in a new browser tab
        3. Complete the GitHub OAuth flow

        **Alternative**: Use the "Try it out" button below, then copy the redirect URL from the response headers.
      security: []
      responses:
        302:
          description: Redirect to GitHub OAuth authorization page
          headers:
            Location:
              type: string
              description: GitHub OAuth authorization URL
              example: "https://github.com/login/oauth/authorize?client_id=..."
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/github/callback:
    get:
      tags:
        - Authentication
      summary: GitHub OAuth callback (Login & Account Linking)
      description: |
        Handle GitHub OAuth callback for both authentication and account linking.

        **Two modes of operation:**
        1. **Login/Registration**: When initiated via `/auth/github/url` - creates new account or logs in existing user
        2. **Account Linking**: When initiated via `/auth/github/link-oauth` - links GitHub account to existing authenticated user

        The callback automatically detects which mode based on session state and processes accordingly.
        This endpoint is called automatically by GitHub after user authorization.
      security: []
      parameters:
        - name: code
          in: query
          description: Authorization code from GitHub
          required: true
          type: string
        - name: state
          in: query
          description: State parameter for CSRF protection
          required: false
          type: string
        - name: error
          in: query
          description: Error parameter if OAuth failed
          required: false
          type: string
      responses:
        200:
          description: GitHub authentication successful
          schema:
            $ref: '#/definitions/GitHubAuthResponse'
        400:
          description: Bad request - Invalid parameters or OAuth error
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/github/link:
    post:
      tags:
        - Authentication
      summary: Link GitHub account to current user (only for local user)
      description: Link a GitHub account to the currently authenticated user
      security:
        - BearerAuth: []
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/GitHubLinkRequest'
      responses:
        200:
          description: GitHub account linked successfully
          schema:
            $ref: '#/definitions/UserResponse'
        400:
          description: Bad request - Invalid token or account already linked
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/github/unlink:
    post:
      tags:
        - Authentication
      summary: Unlink GitHub account from current user (only for local user)
      description: Remove GitHub account link from the currently authenticated user
      security:
        - BearerAuth: []
      responses:
        200:
          description: GitHub account unlinked successfully
          schema:
            $ref: '#/definitions/UserResponse'
        400:
          description: Bad request - No GitHub account linked or cannot unlink
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/github/url:
    get:
      tags:
        - Authentication
      summary: Get GitHub OAuth authorization URL
      description: |
        Get the GitHub OAuth authorization URL without redirecting.
        This is useful for testing in Swagger UI or getting the URL programmatically.

        **Usage**:
        1. Call this endpoint to get the authorization URL
        2. Copy the URL and open it in a browser
        3. Complete GitHub OAuth flow
        4. Use the JWT token returned from the callback
      security: []
      responses:
        200:
          description: GitHub OAuth authorization URL
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: true
              message:
                type: string
                example: "GitHub authorization URL generated"
              data:
                type: object
                properties:
                  authorization_url:
                    type: string
                    example: "https://github.com/login/oauth/authorize?client_id=..."
                  instructions:
                    type: array
                    items:
                      type: string
                    example:
                      - "Copy the authorization_url above"
                      - "Open it in a new browser tab"
                      - "Authorize the application on GitHub"
                      - "You will be redirected back with a JWT token"
              code:
                type: integer
                example: 200
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/forgot-password:
    post:
      tags:
        - Authentication
      summary: Request password reset
      description: |
        Send a password reset email to the user's email address.

        **Security Note**: This endpoint always returns success to prevent email enumeration attacks.
        The reset email is only sent if the account exists and uses local authentication.

        **Features**:
        - Secure token generation
        - 30-minute token expiration
        - Single-use tokens
        - HTML email with reset link
      security: []
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/ForgotPasswordRequest'
      responses:
        200:
          description: Password reset request processed (always returns success for security)
          schema:
            $ref: '#/definitions/SuccessResponse'
        400:
          description: Bad request - Invalid input
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/reset-password:
    post:
      tags:
        - Authentication
      summary: Reset password using token
      description: |
        Reset user password using the token received via email.

        **Features**:
        - Token validation and expiration check
        - Password strength validation
        - Automatic session invalidation
        - Single-use token consumption
      security: []
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/ResetPasswordRequest'
      responses:
        200:
          description: Password reset successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        400:
          description: Bad request - Invalid token, expired token, or weak password
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/change-password:
    post:
      tags:
        - Authentication
      summary: Change password for authenticated user
      description: |
        Change the password for the currently authenticated user.

        **Requirements**:
        - Valid JWT token
        - Correct current password
        - Strong new password
        - New password must be different from current

        **Security**:
        - Only available for local authentication users
        - Verifies current password before change
      security:
        - BearerAuth: []
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/ChangePasswordRequest'
      responses:
        200:
          description: Password changed successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        400:
          description: Bad request - Invalid input, wrong current password, or weak new password
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/update-profile:
    put:
      tags:
        - Authentication
      summary: Update user profile
      description: |
        Update the profile information for the currently authenticated user.

        **Requirements**:
        - Valid JWT token

        **Allowed Fields**:
        - username: User's display name (3-100 characters)
        - email: Valid email address (must be unique)
        - phone: Phone number
        - bio: User biography/description

        **Validation**:
        - Email format validation
        - Email uniqueness check
        - Only provided fields will be updated
        - Returns updated user data with roles

        **Note**: This endpoint allows partial updates - you can update any combination of the allowed fields.
      security:
        - BearerAuth: []
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/UserProfileUpdate'
      responses:
        200:
          description: Profile updated successfully
          schema:
            $ref: '#/definitions/UserProfileResponse'
        400:
          description: Bad request - Invalid input or email already exists
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/reset-session:
    post:
      tags:
        - Authentication
      summary: Reset all user sessions
      description: |
        Invalidate all JWT tokens for the current user, effectively logging them out from all devices.

        **Use Cases**:
        - Security breach response
        - Lost device scenarios
        - Force logout from all sessions

        **Effect**:
        - All existing JWT tokens become invalid
        - User must login again on all devices
        - Current session is also invalidated
      security:
        - BearerAuth: []
      responses:
        200:
          description: All sessions reset successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/github/link-oauth:
    get:
      tags:
        - Authentication
      summary: Initiate GitHub OAuth for account linking
      description: |
        Generate GitHub OAuth authorization URL to automatically link GitHub account to current user.
        This is the recommended way to link GitHub accounts as it handles OAuth flow automatically.

        **Usage**:
        1. Call this endpoint while authenticated (with JWT token)
        2. Copy the authorization_url from response
        3. Open it in a browser to authorize on GitHub
        4. GitHub will redirect back and automatically link the account
      security:
        - BearerAuth: []
      responses:
        200:
          description: Authorization URL generated successfully
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: true
              message:
                type: string
                example: "GitHub OAuth authorization URL generated for account linking"
              data:
                type: object
                properties:
                  authorization_url:
                    type: string
                    format: uri
                    example: "https://github.com/login/oauth/authorize?client_id=xxx&redirect_uri=xxx&scope=user:email+repo&response_type=code&state=xxx"
                  instructions:
                    type: array
                    items:
                      type: string
                    example:
                      - "Copy the authorization_url above"
                      - "Open it in a new browser tab"
                      - "Authorize the application on GitHub"
                      - "You will be redirected back and your GitHub account will be linked"
              code:
                type: integer
                example: 200
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  # Task management endpoints
  /tasks/list_tasks:
    get:
      tags:
        - Tasks
      summary: Get list of tasks
      description: Retrieve all tasks, optionally filtered by assignee. Returns unassigned tasks by default.
      security:
        - BearerAuth: []
      parameters:
        - name: assignee_id
          in: query
          description: Filter tasks by assignee ID. If not provided, returns unassigned tasks.
          required: false
          type: integer
          example: 1
      responses:
        200:
          description: List of tasks retrieved successfully
          schema:
            $ref: '#/definitions/TaskListResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'

  /tasks/create_task_view:
    post:
      tags:
        - Tasks
      summary: Create a new task
      description: Create a new task with the provided details
      security:
        - BearerAuth: []
      parameters:
        - in: body
          name: body
          required: true
          schema:
            $ref: '#/definitions/TaskCreate'
      responses:
        201:
          description: Task created successfully
          schema:
            $ref: '#/definitions/TaskCreateResponse'
        400:
          description: Bad request - Invalid input data
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'

  /tasks/update_task_view/{task_id}:
    put:
      tags:
        - Tasks
      summary: Update an existing task
      description: Update task details. Only the task creator can update the task.
      security:
        - BearerAuth: []
      parameters:
        - name: task_id
          in: path
          description: ID of the task to update
          required: true
          type: integer
          example: 1
        - in: body
          name: body
          required: true
          schema:
            $ref: '#/definitions/TaskUpdate'
      responses:
        200:
          description: Task updated successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        400:
          description: Bad request - Invalid input data
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        403:
          description: Forbidden - Permission denied (only creator can update)
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: Task not found
          schema:
            $ref: '#/definitions/ErrorResponse'

  /tasks/delete_task_view/{task_id}:
    delete:
      tags:
        - Tasks
      summary: Delete a task
      description: Soft delete a task. Only the task creator can delete the task.
      security:
        - BearerAuth: []
      parameters:
        - name: task_id
          in: path
          description: ID of the task to delete
          required: true
          type: integer
          example: 1
      responses:
        200:
          description: Task deleted successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        403:
          description: Forbidden - Permission denied (only creator can delete)
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: Task not found
          schema:
            $ref: '#/definitions/ErrorResponse'

  # Projects API
  /projects/:
    get:
      tags:
        - Projects
      summary: Get all projects for user
      description: Retrieve all projects that the user has access to (created or member of)
      security:
        - BearerAuth: []
      parameters:
        - name: include_deleted
          in: query
          description: Include soft-deleted projects
          required: false
          type: boolean
          default: false
      responses:
        200:
          description: Projects retrieved successfully
          schema:
            $ref: '#/definitions/ProjectListResponse'
        500:
          description: Server error
          schema:
            $ref: '#/definitions/ErrorResponse'

    post:
      tags:
        - Projects
      summary: Create a new project
      description: Create a new project with the provided details
      security:
        - BearerAuth: []
      parameters:
        - in: body
          name: body
          required: true
          schema:
            $ref: '#/definitions/ProjectCreate'
      responses:
        201:
          description: Project created successfully
          schema:
            $ref: '#/definitions/ProjectResponse'
        400:
          description: Bad request - Invalid input data
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized
          schema:
            $ref: '#/definitions/ErrorResponse'

  /projects/{project_id}:
    get:
      tags:
        - Projects
      summary: Get project by ID
      description: Retrieve a specific project by its ID
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: integer
      responses:
        200:
          description: Project retrieved successfully
          schema:
            $ref: '#/definitions/ProjectResponse'
        403:
          description: Access denied
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: Project not found
          schema:
            $ref: '#/definitions/ErrorResponse'

    put:
      tags:
        - Projects
      summary: Update project
      description: Update project details. Only project owner can update.
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: integer
        - in: body
          name: body
          required: true
          schema:
            $ref: '#/definitions/ProjectUpdate'
      responses:
        200:
          description: Project updated successfully
          schema:
            $ref: '#/definitions/ProjectResponse'
        400:
          description: Bad request
          schema:
            $ref: '#/definitions/ErrorResponse'
        403:
          description: Permission denied
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: Project not found
          schema:
            $ref: '#/definitions/ErrorResponse'

    delete:
      tags:
        - Projects
      summary: Delete project
      description: Soft delete a project. Use hard_delete=true for permanent deletion.
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: integer
        - name: hard_delete
          in: query
          description: Permanently delete the project
          required: false
          type: boolean
          default: false
      responses:
        200:
          description: Project deleted successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        403:
          description: Permission denied
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: Project not found
          schema:
            $ref: '#/definitions/ErrorResponse'

  /projects/{project_id}/restore:
    post:
      tags:
        - Projects
      summary: Restore deleted project
      description: Restore a soft-deleted project
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: integer
      responses:
        200:
          description: Project restored successfully
          schema:
            $ref: '#/definitions/ProjectResponse'
        403:
          description: Permission denied
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: Project not found
          schema:
            $ref: '#/definitions/ErrorResponse'

  # GitHub Sync Project And Task
  /projects/github/repositories:
    get:
      tags:
        - GitHub Sync (Projects-Tasks)
      summary: Fetch GitHub repositories
      description: Fetch all GitHub repositories for the authenticated user
      security:
        - BearerAuth: []
      responses:
        200:
          description: Repositories fetched successfully
          schema:
            $ref: '#/definitions/GitHubRepositoriesResponse'
        400:
          description: No GitHub integration found
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /projects/github/sync:
    post:
      tags:
        - GitHub Sync (Projects-Tasks)
      summary: |
        Sync GitHub repositories to projects
        This API help user to clone infomation of projects from remote to local as the same time.
      description: Sync selected GitHub repositories to local projects
      security:
        - BearerAuth: []
      parameters:
        - in: body
          name: body
          required: true
          schema:
            $ref: '#/definitions/GitHubSyncRequest'
      responses:
        200:
          description: Repositories synced successfully
          schema:
            $ref: '#/definitions/GitHubSyncResponse'
        400:
          description: Invalid input or no GitHub integration found
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /projects/{project_id}/github/sync-complete:
    post:
      tags:
        - GitHub Sync (Projects-Tasks)
      summary: Complete project sync
      description: |
        Update project info and sync all issues.
        This api help user actively to synchronize everything from their github project to local.
        (Include project information and issues)
        It look like refresh feature.
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: path
          description: |
            ID of the project to completely sync 
            (This use local project id instead of github project id)
          required: true
          type: integer
          example: 1
      responses:
        200:
          description: Project synced successfully
          schema:
            $ref: '#/definitions/GitHubCompleteSyncResponse'
        400:
          description: Invalid project or no GitHub integration found
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: Project not found
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /tasks/github/sync/{project_id}:
    post:
      tags:
        - GitHub Sync (Projects-Tasks)
      summary: Sync GitHub issues to tasks
      description: Sync GitHub issues from a repository to local tasks
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: path
          description: ID of the project to sync issues for
          required: true
          type: integer
          example: 1
      responses:
        200:
          description: Issues synced successfully
          schema:
            $ref: '#/definitions/GitHubIssuesSyncResponse'
        400:
          description: Invalid project or no GitHub integration found
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: Project not found
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  # GitHub Webhooks
  /webhooks/github:
    post:
      tags:
        - GitHub Sync (Projects-Tasks)
      summary: GitHub webhook endpoint
      description: Endpoint for receiving webhook events from GitHub (called by GitHub, not users)
      security: []
      parameters:
        - in: body
          name: body
          required: true
          schema:
            type: object
            description: GitHub webhook payload (varies by event type)
      responses:
        200:
          description: Webhook processed successfully
          schema:
            type: object
            properties:
              message:
                type: string
              data:
                type: object
        400:
          description: Bad request
          schema:
            type: object
            properties:
              error:
                type: string
        401:
          description: Invalid signature
          schema:
            type: object
            properties:
              error:
                type: string
        404:
          description: Webhook not found
          schema:
            type: object
            properties:
              error:
                type: string
        500:
          description: Internal server error
          schema:
            type: object
            properties:
              error:
                type: string

  /webhooks/create:
    post:
      tags:
        - GitHub Sync (Projects-Tasks)
      summary: Create GitHub webhooks for repositories
      description: Create webhooks for multiple GitHub repositories to enable automatic sync
      security:
        - BearerAuth: []
      parameters:
        - in: body
          name: body
          required: true
          schema:
            $ref: '#/definitions/WebhookCreateRequest'
      responses:
        200:
          description: Webhooks created successfully
          schema:
            $ref: '#/definitions/WebhookCreateResponse'
        400:
          description: Bad request
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /webhooks/list:
    get:
      tags:
        - GitHub Sync (Projects-Tasks)
      summary: List user's GitHub webhooks
      description: Get all active webhooks for the authenticated user
      security:
        - BearerAuth: []
      responses:
        200:
          description: Webhooks retrieved successfully
          schema:
            $ref: '#/definitions/WebhookListResponse'
        401:
          description: Unauthorized
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /webhooks/{github_webhook_id}:
    delete:
      tags:
        - GitHub Sync (Projects-Tasks)
      summary: Delete a GitHub webhook
      description: Delete a specific webhook by ID
      security:
        - BearerAuth: []
      parameters:
        - name: github_webhook_id
          in: path
          required: true
          type: integer
          description: Webhook ID to delete
      responses:
        200:
          description: Webhook deleted successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        404:
          description: Webhook not found
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /webhooks/events/{github_webhook_id}:
    get:
      tags:
        - GitHub Sync (Projects-Tasks)
      summary: Get webhook events
      description: Get events for a specific webhook with pagination
      security:
        - BearerAuth: []
      parameters:
        - name: github_webhook_id
          in: path
          required: true
          type: integer
          description: Webhook ID
        - name: page
          in: query
          type: integer
          default: 1
          description: Page number
        - name: per_page
          in: query
          type: integer
          default: 20
          maximum: 100
          description: Items per page
      responses:
        200:
          description: Webhook events retrieved successfully
          schema:
            $ref: '#/definitions/WebhookEventsResponse'
        404:
          description: Webhook not found
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  # Jira OAuth Authentication
  /auth/jira/url:
    get:
      tags:
        - Authentication
      summary: Get Jira OAuth authorization URL
      description: |
        Get the Jira OAuth authorization URL without redirecting.
        This is useful for testing in Swagger UI or getting the URL programmatically.

        **Usage**:
        1. Call this endpoint to get the authorization URL
        2. Copy the URL and open it in a browser
        3. Complete Jira OAuth flow
        4. Use the JWT token returned from the callback
      security: []
      responses:
        200:
          description: Jira OAuth authorization URL
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: true
              message:
                type: string
                example: "Jira authorization URL generated"
              data:
                type: object
                properties:
                  authorization_url:
                    type: string
                    example: "https://auth.atlassian.com/authorize?audience=api.atlassian.com&client_id=..."
                  instructions:
                    type: array
                    items:
                      type: string
                    example:
                      - "Copy the authorization_url above"
                      - "Open it in a new browser tab"
                      - "Authorize the application on Jira"
                      - "You will be redirected back with a JWT token"
              code:
                type: integer
                example: 200
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/jira:
    get:
      tags:
        - Authentication
      summary: Initiate Jira OAuth login
      description: |
        Redirect user to Jira for OAuth authentication.

        **Note**: This endpoint returns a 302 redirect. In Swagger UI, you'll see an error because it cannot follow redirects automatically.

        **To test properly**:
        1. Copy the URL: `http://localhost:8084/auth/jira`
        2. Open it in a new browser tab
        3. Complete the Jira OAuth flow
      security: []
      responses:
        302:
          description: Redirect to Jira OAuth authorization page
          headers:
            Location:
              type: string
              description: Jira OAuth authorization URL
              example: "https://auth.atlassian.com/authorize?audience=api.atlassian.com&client_id=..."
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/jira/callback:
    get:
      tags:
        - Authentication
      summary: Jira OAuth callback
      description: |
        Handle Jira OAuth callback for authentication.
        This endpoint is called automatically by Jira after user authorization.
      security: []
      parameters:
        - name: code
          in: query
          description: Authorization code from Jira
          required: true
          type: string
        - name: state
          in: query
          description: State parameter for CSRF protection
          required: false
          type: string
        - name: error
          in: query
          description: Error parameter if OAuth failed
          required: false
          type: string
      responses:
        200:
          description: Jira authentication successful
          schema:
            $ref: '#/definitions/JiraAuthResponse'
        400:
          description: Bad request - Invalid parameters or OAuth error
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  # Jira Integration API
  /webhooks/jira/status:
    get:
      tags:
        - Jira Integration
      summary: Get Jira integration status
      description: Check if the current user has an active Jira integration
      security:
        - BearerAuth: []
      responses:
        200:
          description: Jira integration status retrieved
          schema:
            $ref: '#/definitions/JiraStatusResponse'
        401:
          description: Unauthorized
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /webhooks/jira/projects:
    get:
      tags:
        - Jira Integration
      summary: Get Jira projects
      description: Get all Jira projects accessible to the authenticated user
      security:
        - BearerAuth: []
      responses:
        200:
          description: Jira projects retrieved successfully
          schema:
            $ref: '#/definitions/JiraProjectsResponse'
        400:
          description: No active Jira integration found
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /webhooks/jira/projects/{project_key}/sync:
    post:
      tags:
        - Jira Integration
      summary: Sync Jira project to TMS
      description: Sync a Jira project to TMS as a new project
      security:
        - BearerAuth: []
      parameters:
        - name: project_key
          in: path
          description: Jira project key (e.g., PROJ, TEST)
          required: true
          type: string
          example: "PROJ"
      responses:
        200:
          description: Jira project synced successfully
          schema:
            $ref: '#/definitions/JiraProjectSyncResponse'
        400:
          description: Bad request or project already synced
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /webhooks/jira/projects/{project_key}/issues:
    get:
      tags:
        - Jira Integration
      summary: Get Jira project issues
      description: Get all issues from a specific Jira project
      security:
        - BearerAuth: []
      parameters:
        - name: project_key
          in: path
          description: Jira project key
          required: true
          type: string
          example: "PROJ"
        - name: status
          in: query
          description: Filter by issue status
          required: false
          type: string
          example: "To Do"
        - name: assignee
          in: query
          description: Filter by assignee account ID
          required: false
          type: string
        - name: limit
          in: query
          description: Maximum number of issues to return
          required: false
          type: integer
          default: 50
          maximum: 100
      responses:
        200:
          description: Jira issues retrieved successfully
          schema:
            $ref: '#/definitions/JiraIssuesResponse'
        400:
          description: Bad request
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /webhooks/jira/issues/{issue_key}/sync:
    post:
      tags:
        - Jira Integration
      summary: Sync Jira issue to TMS task
      description: Sync a specific Jira issue to TMS as a new task
      security:
        - BearerAuth: []
      parameters:
        - name: issue_key
          in: path
          description: Jira issue key (e.g., PROJ-123)
          required: true
          type: string
          example: "PROJ-123"
        - in: body
          name: body
          required: true
          schema:
            type: object
            required:
              - project_id
            properties:
              project_id:
                type: integer
                description: TMS project ID to sync the issue to
                example: 1
      responses:
        200:
          description: Jira issue synced successfully
          schema:
            $ref: '#/definitions/JiraIssueSyncResponse'
        400:
          description: Bad request
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /webhooks/jira/tasks/{task_id}/sync:
    post:
      tags:
        - Jira Integration
      summary: Sync TMS task to Jira issue
      description: Sync a TMS task to Jira as a new issue
      security:
        - BearerAuth: []
      parameters:
        - name: task_id
          in: path
          description: TMS task ID
          required: true
          type: integer
          example: 1
        - in: body
          name: body
          required: true
          schema:
            type: object
            required:
              - jira_project_key
            properties:
              jira_project_key:
                type: string
                description: Jira project key to create the issue in
                example: "PROJ"
      responses:
        200:
          description: Task synced to Jira successfully
          schema:
            $ref: '#/definitions/JiraTaskSyncResponse'
        400:
          description: Bad request
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /webhooks/jira/issues/{issue_key}:
    get:
      tags:
        - Jira Integration
      summary: Get Jira issue details
      description: Get detailed information about a specific Jira issue
      security:
        - BearerAuth: []
      parameters:
        - name: issue_key
          in: path
          description: Jira issue key
          required: true
          type: string
          example: "PROJ-123"
      responses:
        200:
          description: Jira issue details retrieved successfully
          schema:
            $ref: '#/definitions/JiraIssueResponse'
        400:
          description: Bad request
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: Issue not found
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /webhooks/jira/issues/{issue_key}/comments:
    post:
      tags:
        - Jira Integration
      summary: Add comment to Jira issue
      description: Add a comment to a specific Jira issue
      security:
        - BearerAuth: []
      parameters:
        - name: issue_key
          in: path
          description: Jira issue key
          required: true
          type: string
          example: "PROJ-123"
        - in: body
          name: body
          required: true
          schema:
            type: object
            required:
              - comment
            properties:
              comment:
                type: string
                description: Comment text to add
                example: "This is a comment from TMS"
      responses:
        200:
          description: Comment added successfully
          schema:
            $ref: '#/definitions/JiraCommentResponse'
        400:
          description: Bad request
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: Issue not found
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /webhooks/jira/search:
    get:
      tags:
        - Jira Integration
      summary: Search Jira issues with JQL
      description: Search Jira issues using Jira Query Language (JQL)
      security:
        - BearerAuth: []
      parameters:
        - name: jql
          in: query
          description: JQL query string
          required: true
          type: string
          example: "project = PROJ AND status = 'To Do'"
        - name: max_results
          in: query
          description: Maximum number of results
          required: false
          type: integer
          default: 50
          maximum: 100
      responses:
        200:
          description: Search results retrieved successfully
          schema:
            $ref: '#/definitions/JiraSearchResponse'
        400:
          description: Bad request - Invalid JQL
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /webhooks/jira/webhook:
    post:
      tags:
        - Jira Integration
      summary: Jira webhook endpoint
      description: |
        Endpoint for receiving webhook events from Jira (called by Jira, not users).
        This endpoint processes Jira webhook events and syncs data to TMS.
      security: []
      parameters:
        - name: user_id
          in: query
          description: User ID for webhook processing
          required: true
          type: integer
          example: 1
        - in: body
          name: body
          required: true
          schema:
            type: object
            description: Jira webhook payload (varies by event type)
            properties:
              webhookEvent:
                type: string
                example: "jira:issue_created"
                description: Type of webhook event
              issue:
                type: object
                description: Jira issue data
      responses:
        200:
          description: Webhook processed successfully
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: true
              message:
                type: string
                example: "Issue created event processed"
        400:
          description: Bad request - Missing required data
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string
                example: "Missing required webhook data"
        500:
          description: Internal server error
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string

  # Project Members API
  /members/projects/{project_id}:
    get:
      tags:
        - Project Members
      summary: Get project members
      description: Get all members of a specific project
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: integer
      responses:
        200:
          description: Project members retrieved successfully
          schema:
            $ref: '#/definitions/ProjectMembersResponse'
        403:
          description: Access denied
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: Project not found
          schema:
            $ref: '#/definitions/ErrorResponse'

    post:
      tags:
        - Project Members
      summary: Add member to project
      description: Add a new member to the project with specified role
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: integer
        - in: body
          name: body
          required: true
          schema:
            $ref: '#/definitions/AddMemberRequest'
      responses:
        201:
          description: Member added successfully
          schema:
            $ref: '#/definitions/MemberResponse'
        400:
          description: Bad request - Invalid input or user already member
          schema:
            $ref: '#/definitions/ErrorResponse'
        403:
          description: Permission denied
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: Project or user not found
          schema:
            $ref: '#/definitions/ErrorResponse'

  /members/projects/{project_id}/users/{user_id}:
    put:
      tags:
        - Project Members
      summary: Update member role
      description: Update a member's role in the project
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: integer
        - name: user_id
          in: path
          description: ID of the member to update
          required: true
          type: integer
        - in: body
          name: body
          required: true
          schema:
            $ref: '#/definitions/UpdateMemberRoleRequest'
      responses:
        200:
          description: Member role updated successfully
          schema:
            $ref: '#/definitions/MemberUpdateResponse'
        400:
          description: Bad request
          schema:
            $ref: '#/definitions/ErrorResponse'
        403:
          description: Permission denied
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: Project or member not found
          schema:
            $ref: '#/definitions/ErrorResponse'

    delete:
      tags:
        - Project Members
      summary: Remove member from project
      description: Remove a member from the project
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: integer
        - name: user_id
          in: path
          description: ID of the member to remove
          required: true
          type: integer
      responses:
        200:
          description: Member removed successfully
          schema:
            $ref: '#/definitions/MemberRemoveResponse'
        400:
          description: Cannot remove project creator
          schema:
            $ref: '#/definitions/ErrorResponse'
        403:
          description: Permission denied
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: Project or member not found
          schema:
            $ref: '#/definitions/ErrorResponse'

  /members/my-projects:
    get:
      tags:
        - Project Members
      summary: Get user's projects
      description: Get all projects where current user is a member or creator
      security:
        - BearerAuth: []
      responses:
        200:
          description: User's projects retrieved successfully
          schema:
            $ref: '#/definitions/UserProjectsResponse'
        500:
          description: Server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  # Statistics API
  /statistics/dashboard:
    get:
      tags:
        - Statistics
      summary: Get dashboard statistics
      description: Get dashboard statistics for current user including projects overview and task summary
      security:
        - BearerAuth: []
      responses:
        200:
          description: Dashboard statistics retrieved successfully
          schema:
            $ref: '#/definitions/DashboardStatsResponse'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /statistics/user:
    get:
      tags:
        - Statistics
      summary: Get user statistics
      description: Get detailed statistics for current user
      security:
        - BearerAuth: []
      responses:
        200:
          description: User statistics retrieved successfully
          schema:
            $ref: '#/definitions/UserStatsResponse'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /statistics/user/{user_id}:
    get:
      tags:
        - Statistics
      summary: Get specific user statistics
      description: Get statistics for a specific user (admin feature)
      security:
        - BearerAuth: []
      parameters:
        - name: user_id
          in: path
          description: ID of the user to get statistics for
          required: true
          type: integer
      responses:
        200:
          description: User statistics retrieved successfully
          schema:
            $ref: '#/definitions/UserStatsResponse'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /statistics/project/{project_id}:
    get:
      tags:
        - Statistics
      summary: Get project statistics
      description: Get comprehensive statistics for a specific project
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: path
          description: ID of the project to get statistics for
          required: true
          type: integer
      responses:
        200:
          description: Project statistics retrieved successfully
          schema:
            $ref: '#/definitions/ProjectStatsResponse'
        403:
          description: Access denied
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: Project not found
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Server error
          schema:
            $ref: '#/definitions/ErrorResponse'
          
  /statistics/reliability:
    get:
      tags:
        - Statistics
      summary: Get current user reliability metrics
      description: Get reliability metrics for the current authenticated user
      security:
        - BearerAuth: []
      parameters:
        - name: period
          in: query
          description: Time period in days to calculate reliability (default 30 days)
          required: false
          type: integer
          default: 30
      responses:
        200:
          description: Reliability metrics retrieved successfully
          schema:
            $ref: '#/definitions/ReliabilityResponse'
        500:
          description: Server error
          schema:
            $ref: '#/definitions/ErrorResponse'
            
  /statistics/reliability/{user_id}:
    get:
      tags:
        - Statistics
      summary: Get specific user reliability metrics
      description: Get reliability metrics for a specific user
      security:

  # Activity History Endpoints
  /history/user/{user_id}:
    get:
      tags:
        - History
      summary: Get user activity logs
      description: Get activity logs for a specific user (users can only see their own logs unless they're admins)
      security:
        - BearerAuth: []
      parameters:
        - name: user_id
          in: path
          description: User ID to get activity logs for
          required: true
          type: integer
        - name: page
          in: query
          description: Page number (default 1)
          required: false
          type: integer
          default: 1
        - name: per_page
          in: query
          description: Results per page (default 20, max 100)
          required: false
          type: integer
          default: 20
      responses:
        200:
          description: Activity logs retrieved successfully
          schema:
            $ref: '#/definitions/ActivityLogsResponse'
        400:
          description: Invalid parameters
          schema:
            $ref: '#/definitions/ErrorResponse'
        403:
          description: Access denied
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /history/project/{project_id}:
    get:
      tags:
        - History
      summary: Get project activity logs
      description: Get activity logs for a specific project
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: path
          description: Project ID to get activity logs for
          required: true
          type: integer
        - name: page
          in: query
          description: Page number (default 1)
          required: false
          type: integer
          default: 1
        - name: per_page
          in: query
          description: Results per page (default 20, max 100)
          required: false
          type: integer
          default: 20
      responses:
        200:
          description: Activity logs retrieved successfully
          schema:
            $ref: '#/definitions/ActivityLogsResponse'
        400:
          description: Invalid parameters
          schema:
            $ref: '#/definitions/ErrorResponse'
        403:
          description: Access denied
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /history/task/{task_id}:
    get:
      tags:
        - History
      summary: Get task activity logs
      description: Get activity logs for a specific task
      security:
        - BearerAuth: []
      parameters:
        - name: task_id
          in: path
          description: Task ID to get activity logs for
          required: true
          type: integer
        - name: page
          in: query
          description: Page number (default 1)
          required: false
          type: integer
          default: 1
        - name: per_page
          in: query
          description: Results per page (default 20, max 100)
          required: false
          type: integer
          default: 20
      responses:
        200:
          description: Activity logs retrieved successfully
          schema:
            $ref: '#/definitions/ActivityLogsResponse'
        400:
          description: Invalid parameters
          schema:
            $ref: '#/definitions/ErrorResponse'
        403:
          description: Access denied
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /history/recent:
    get:
      tags:
        - History
      summary: Get recent activity logs
      description: Get recent activity logs across the system (requires admin permissions)
      security:
        - BearerAuth: []
      parameters:
        - name: limit
          in: query
          description: Maximum number of logs to retrieve (default 20, max 100)
          required: false
          type: integer
          default: 20
      responses:
        200:
          description: Recent activity logs retrieved successfully
          schema:
            $ref: '#/definitions/RecentActivityLogsResponse'
        400:
          description: Invalid parameters
          schema:
            $ref: '#/definitions/ErrorResponse'
        403:
          description: Access denied
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /statistics/reliability/{user_id}:
    get:
      tags:
        - Statistics
      summary: Get specific user reliability metrics
      description: Get reliability metrics for a specific user
      security:
        - BearerAuth: []
      parameters:
        - name: user_id
          in: path
          description: ID of the user to get reliability metrics for
          required: true
          type: integer
        - name: period
          in: query
          description: Time period in days to calculate reliability (default 30 days)
          required: false
          type: integer
          default: 30
      responses:
        200:
          description: Reliability metrics retrieved successfully
          schema:
            $ref: '#/definitions/ReliabilityResponse'
        500:
          description: Server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  # Telegram Integration Endpoints
  /telegram/webhook:
    post:
      tags:
        - Telegram Integration
      summary: Telegram webhook endpoint
      description: |
        Endpoint for receiving webhook updates from Telegram Bot API.
        This endpoint is called by Telegram, not by users directly.

        **Note**: This endpoint does not require authentication as it's called by Telegram.
      security: []
      parameters:
        - name: X-Telegram-Bot-Api-Secret-Token
          in: header
          description: Secret token for webhook verification (optional)
          required: false
          type: string
        - in: body
          name: body
          required: true
          schema:
            $ref: '#/definitions/TelegramWebhookUpdate'
      responses:
        200:
          description: Webhook processed successfully
          schema:
            type: object
            properties:
              ok:
                type: boolean
                example: true
        400:
          description: Bad request - Invalid payload
          schema:
            type: object
            properties:
              error:
                type: string
                example: "No JSON payload"
        401:
          description: Unauthorized - Invalid webhook token
          schema:
            type: object
            properties:
              error:
                type: string
                example: "Invalid webhook token"

  /telegram/link:
    post:
      tags:
        - Telegram Integration
      summary: Link Telegram account to TMS account
      description: |
        Link user's Telegram account to their TMS account.
        This enables the user to receive notifications via Telegram.

        **How to link account**:

        **Option 1: Using Telegram Bot Commands (Recommended)**
        1. Start a conversation with your Telegram bot
        2. Send `/link <EMAIL> your_password` to the bot
        3. Bot will automatically link your account

        **Option 2: Using API directly**
        1. Start a conversation with your Telegram bot
        2. Send any message to the bot
        3. Use Telegram Bot API to get updates and extract user information
        4. Use the extracted data to call this endpoint
      security:
        - BearerAuth: []
      parameters:
        - in: body
          name: body
          required: true
          schema:
            $ref: '#/definitions/TelegramLinkRequest'
      responses:
        200:
          description: Telegram account linked successfully
          schema:
            $ref: '#/definitions/TelegramLinkResponse'
        400:
          description: Bad request - Invalid input or account already linked
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /telegram/unlink:
    post:
      tags:
        - Telegram Integration
      summary: Unlink Telegram account from TMS account
      description: |
        Remove the link between user's Telegram account and TMS account.
        This will stop all Telegram notifications and delete all subscription data.
      security:
        - BearerAuth: []
      responses:
        200:
          description: Telegram account unlinked successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        404:
          description: No Telegram account linked
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /telegram/status:
    get:
      tags:
        - Telegram Integration
      summary: Get Telegram integration status
      description: |
        Get the current user's Telegram integration status and active subscriptions.
        Returns information about linked account and notification preferences.
      security:
        - BearerAuth: []
      responses:
        200:
          description: Telegram status retrieved successfully
          schema:
            $ref: '#/definitions/TelegramStatusResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /telegram/subscribe:
    post:
      tags:
        - Telegram Integration
      summary: Manage notification subscriptions
      description: |
        Subscribe or unsubscribe from different types of notifications.

        **Available notification types**:
        - `github_notifications`: Receive notifications for GitHub webhook events (push, issues, PRs, etc.)

        **Prerequisites**:
        - Telegram account must be linked to TMS account
        - User must have started a conversation with the bot
      security:
        - BearerAuth: []
      parameters:
        - in: body
          name: body
          required: true
          schema:
            $ref: '#/definitions/TelegramSubscriptionRequest'
      responses:
        200:
          description: Subscription updated successfully
          schema:
            $ref: '#/definitions/TelegramSubscriptionResponse'
        400:
          description: Bad request - Invalid notification type or no Telegram account linked
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  # Telegram Webhook Management Endpoints
  /telegram/webhook/setup:
    post:
      tags:
        - Telegram Administration
      summary: Setup Telegram webhook
      description: |
        Setup webhook for Telegram bot to receive updates.

        **Requirements:**
        - Authenticated user (JWT token required)
        - HTTPS URL for webhook endpoint
        - Valid bot token configured in environment

        **Features:**
        - Automatically generates secure secret token if not provided
        - Validates webhook URL format
        - Configures optimal webhook settings
        - Returns secret token for environment configuration

        **Important Notes:**
        - Webhook URL must use HTTPS protocol
        - Only one webhook can be active per bot
        - Setting new webhook will replace existing one
        - Save the returned secret token to your environment variables

        **Example Usage:**
        ```json
        {
          "webhook_url": "https://your-domain.com/telegram/webhook"
        }
        ```
      security:
        - BearerAuth: []
      parameters:
        - in: body
          name: body
          required: true
          schema:
            $ref: '#/definitions/TelegramWebhookSetupRequest'
      responses:
        200:
          description: Webhook setup successfully
          schema:
            $ref: '#/definitions/TelegramWebhookSetupResponse'
        400:
          description: Bad request - Invalid webhook URL or Telegram API error
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error - Bot token not configured
          schema:
            $ref: '#/definitions/ErrorResponse'

  /telegram/webhook/info:
    get:
      tags:
        - Telegram Administration
      summary: Get webhook information
      description: |
        Get current webhook configuration and status from Telegram.

        **Information includes:**
        - Current webhook URL
        - Pending updates count
        - Error status and messages
        - Connection settings
        - Last synchronization status

        **Use this endpoint to:**
        - Verify webhook is properly configured
        - Check for webhook errors
        - Monitor webhook health
        - Debug webhook issues
      security:
        - BearerAuth: []
      responses:
        200:
          description: Webhook information retrieved successfully
          schema:
            $ref: '#/definitions/TelegramWebhookInfoResponse'
        400:
          description: Bad request - Telegram API error
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error - Bot token not configured
          schema:
            $ref: '#/definitions/ErrorResponse'

  /telegram/webhook/delete:
    post:
      tags:
        - Telegram Administration
      summary: Delete Telegram webhook
      description: |
        Remove webhook configuration from Telegram bot.

        **Effects:**
        - Removes webhook URL from Telegram
        - Drops all pending updates
        - Bot will stop receiving real-time updates
        - Switches bot to polling mode (if implemented)

        **Use cases:**
        - Switching from webhook to polling
        - Temporarily disabling bot
        - Changing webhook URL (delete old, setup new)
        - Troubleshooting webhook issues

        **Note:** After deletion, you'll need to setup webhook again to receive updates.
      security:
        - BearerAuth: []
      responses:
        200:
          description: Webhook deleted successfully
          schema:
            $ref: '#/definitions/TelegramWebhookDeleteResponse'
        400:
          description: Bad request - Telegram API error
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error - Bot token not configured
          schema:
            $ref: '#/definitions/ErrorResponse'

  /telegram/bot/info:
    get:
      tags:
        - Telegram Administration
      summary: Get bot information
      description: |
        Get information about the configured Telegram bot.

        **Information includes:**
        - Bot ID and username
        - Bot capabilities and permissions
        - Group and inline query settings
        - Business and web app features

        **Use this endpoint to:**
        - Verify bot token is valid
        - Check bot configuration
        - Get bot username for user instructions
        - Verify bot permissions
      security:
        - BearerAuth: []
      responses:
        200:
          description: Bot information retrieved successfully
          schema:
            $ref: '#/definitions/TelegramBotInfoResponse'
        400:
          description: Bad request - Invalid bot token or Telegram API error
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error - Bot token not configured
          schema:
            $ref: '#/definitions/ErrorResponse'

  # User Management Endpoints

  /users/{user_id}:
    put:
      tags:
        - User Management
      summary: Update user information
      description: |
        Update user information by user ID.

        **Permission Rules**:
        - **Admin**: Can update any user except other admins
        - **Project Owner**: Can only update project_member or project_viewer users
        - **Project Manager**: Can only update project_member or project_viewer users

        **Allowed Fields**:
        - username: User's display name (3-100 characters)
        - email: Valid email address (must be unique)
        - phone: Phone number
        - bio: User biography/description

        **Validation**:
        - Email format validation
        - Email uniqueness check
        - Only provided fields will be updated
      security:
        - BearerAuth: []
      parameters:
        - name: user_id
          in: path
          description: User ID
          required: true
          type: integer
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/UserProfileUpdate'
      responses:
        200:
          description: User updated successfully
          schema:
            $ref: '#/definitions/UserProfileResponse'
        400:
          description: Bad request - Invalid input or email already exists
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        403:
          description: Forbidden - Insufficient permissions
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

    delete:
      tags:
        - User Management
      summary: Delete user (Admin only)
      description: |
        Delete a user by user ID.

        **Admin Only**: This endpoint requires admin privileges.

        **Restrictions**:
        - Cannot delete your own account
        - Cannot delete other admin users
        - Will also delete user's roles and integrations

        **Warning**: This action is irreversible!
      security:
        - BearerAuth: []
      parameters:
        - name: user_id
          in: path
          description: User ID to delete
          required: true
          type: integer
      responses:
        200:
          description: User deleted successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        400:
          description: Bad request - Cannot delete own account
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        403:
          description: Forbidden - Admin access required or cannot delete admin users
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /users:
    get:
      tags:
        - User Management
      summary: Get all users (Admin only)
      description: |
        Retrieve a paginated list of all users in the system.

        **Admin, Project Owner, and Project Manager Only**: This endpoint requires admin, project_owner, or project_manager privileges.

        **Features**:
        - Pagination support
        - Search by username or email
        - Includes user roles and permissions
        - Filtering capabilities
      security:
        - BearerAuth: []
      parameters:
        - name: page
          in: query
          description: "Page number (default: 1)"
          required: false
          type: integer
          minimum: 1
          default: 1
        - name: per_page
          in: query
          description: "Items per page (default: 20, max: 100)"
          required: false
          type: integer
          minimum: 1
          maximum: 100
          default: 20
        - name: search
          in: query
          description: Search by username or email
          required: false
          type: string
      responses:
        200:
          description: Users retrieved successfully
          schema:
            $ref: '#/definitions/UserListPaginatedResponse'
        403:
          description: Forbidden - Admin, project_owner, or project_manager access required
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'



  /users/{user_id}/roles:
    get:
      tags:
        - User Management
      summary: Get user roles
      description: |
        Retrieve all roles assigned to a specific user.

        **Access Control**:
        - Admin, project_owner, and project_manager can view any user's roles
      security:
        - BearerAuth: []
      parameters:
        - name: user_id
          in: path
          description: User ID
          required: true
          type: integer
      responses:
        200:
          description: User roles retrieved successfully
          schema:
            $ref: '#/definitions/UserRolesResponse'
        403:
          description: Forbidden - Admin, project_owner, or project_manager access required
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

    post:
      tags:
        - User Management
      summary: Assign role to user (Admin, Project Owner, and Project Manager with restrictions)
      description: |
        Assign a role to a specific user with permission restrictions.

        **Permission Rules**:
        - **Admin**: Can assign any role except admin role
        - **Project Owner**: Can only assign project_member or project_viewer roles to users who are not admin, project_owner, or project_manager
        - **Project Manager**: Can only assign project_member or project_viewer roles to users who are not admin, project_owner, or project_manager

        **Available Roles**:
        - member: Regular user (admin only)
        - project_owner: Project owner (admin only)
        - project_manager: Project manager (admin only)
        - project_member: Project member (admin, project_owner, and project_manager)
        - project_viewer: Project viewer (admin, project_owner, and project_manager)
      security:
        - BearerAuth: []
      parameters:
        - name: user_id
          in: path
          description: User ID
          required: true
          type: integer
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/AssignRoleRequest'
      responses:
        200:
          description: Role assigned successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        400:
          description: Bad request - Invalid role or user already has role
          schema:
            $ref: '#/definitions/ErrorResponse'
        403:
          description: Forbidden - Insufficient permissions or invalid role assignment
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: User or role not found
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /users/{user_id}/roles/{role_name}:
    delete:
      tags:
        - User Management
      summary: Remove role from user (Admin, Project Owner, and Project Manager with restrictions)
      description: |
        Remove a specific role from a user with permission restrictions.

        **Permission Rules**:
        - **Admin**: Can remove any role except admin role
        - **Project Owner**: Can only remove project_member or project_viewer roles from users who are not admin, project_owner, or project_manager
        - **Project Manager**: Can only remove project_member or project_viewer roles from users who are not admin, project_owner, or project_manager
      security:
        - BearerAuth: []
      parameters:
        - name: user_id
          in: path
          description: User ID
          required: true
          type: integer
        - name: role_name
          in: path
          description: Role name to remove
          required: true
          type: string
          enum: [admin, member, project_owner, project_manager, project_member, project_viewer]
      responses:
        200:
          description: Role removed successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        400:
          description: Bad request - User does not have this role
          schema:
            $ref: '#/definitions/ErrorResponse'
        403:
          description: Forbidden - Insufficient permissions or invalid role removal
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: User or role not found
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /users/{user_id}/permissions:
    get:
      tags:
        - User Management
      summary: Get user permissions
      description: |
        Retrieve all permissions for a specific user based on their roles.

        **Access Control**:
        - Admin, project_owner, and project_manager can view any user's permissions

        **Returns**: List of all permissions granted through user's roles
      security:
        - BearerAuth: []
      parameters:
        - name: user_id
          in: path
          description: User ID
          required: true
          type: integer
      responses:
        200:
          description: User permissions retrieved successfully
          schema:
            $ref: '#/definitions/UserPermissionsResponse'
        403:
          description: Forbidden - Admin, project_owner, or project_manager access required
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

# All definitions in one place
definitions:
  # Common Response Schemas
  SuccessResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Operation completed successfully"
      data:
        type: object
        example: null
      code:
        type: integer
        example: 200

  ErrorResponse:
    type: object
    properties:
      success:
        type: boolean
        example: false
      message:
        type: string
        example: "Operation failed"
      data:
        type: object
        example: null
      code:
        type: integer
        example: 400

  # User-specific Responses
  UserResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "User retrieved successfully"
      data:
        $ref: '#/definitions/UserWithRoles'
      code:
        type: integer
        example: 200

  UserListResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Users retrieved successfully"
      data:
        type: array
        items:
          $ref: '#/definitions/User'
      code:
        type: integer
        example: 200

  LoginResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Login successful"
      data:
        type: object
        properties:
          access_token:
            type: string
            example: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
            description: "JWT access token with user roles and permissions"
          user:
            $ref: '#/definitions/UserWithRoles'
            description: "User information with roles"
      code:
        type: integer
        example: 200

  # Task-specific Responses
  TaskResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Task retrieved successfully"
      data:
        $ref: '#/definitions/Task'
      code:
        type: integer
        example: 200

  TaskListResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Tasks retrieved successfully"
      data:
        type: array
        items:
          $ref: '#/definitions/Task'
      code:
        type: integer
        example: 200

  TaskCreateResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Task created successfully"
      data:
        type: object
        properties:
          id:
            type: integer
            example: 1
            description: "ID of the created task"
      code:
        type: integer
        example: 201

  # User Schemas (from paths/auth.yaml)
  User:
    type: object
    properties:
      id:
        type: integer
        example: 1
      username:
        type: string
        example: "testuser"
      email:
        type: string
        format: email
        example: "<EMAIL>"
      phone:
        type: string
        example: "******-123-4567"
      bio:
        type: string
        example: "This is a user bio"
      created_at:
        type: string
        format: date-time
        example: "2024-01-01T10:00:00Z"
      updated_at:
        type: string
        format: date-time
        example: "2024-01-01T10:00:00Z"
      two_factor_enabled:
        type: boolean
        example: false
      github_id:
        type: string
        example: "********"
        description: "GitHub user ID (if linked)"
      github_username:
        type: string
        example: "octocat"
        description: "GitHub username (if linked)"
      github_avatar_url:
        type: string
        example: "https://avatars.githubusercontent.com/u/583231?v=4"
        description: "GitHub avatar URL (if linked)"
      auth_provider:
        type: string
        example: "local"
        description: "Authentication provider (local, github, etc.)"

  UserRegister:
    type: object
    required:
      - username
      - email
      - password
    properties:
      username:
        type: string
        example: "testuser"
        minLength: 3
        maxLength: 100
        description: "Username (3-100 characters)"
      email:
        type: string
        format: email
        example: "<EMAIL>"
        description: "Valid email address"
      password:
        type: string
        example: "Admin_12345"
        minLength: 8
        description: "Password (minimum 8 characters)"

  UserLogin:
    type: object
    required:
      - email
      - password
    properties:
      email:
        type: string
        format: email
        example: "<EMAIL>"
        description: "User email address"
      password:
        type: string
        example: "Admin_12345"
        description: "User password"

  # GitHub Authentication Schemas
  GitHubAuthResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "GitHub authentication successful"
      data:
        type: object
        properties:
          access_token:
            type: string
            example: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
            description: "JWT access token"
          user:
            $ref: '#/definitions/User'
      code:
        type: integer
        example: 200

  GitHubLinkRequest:
    type: object
    required:
      - access_token
    properties:
      access_token:
        type: string
        example: "gho_xxxxxxxxxxxxxxxxxxxx"
        description: "GitHub access token obtained from OAuth flow"

  # Password Management Schemas
  ForgotPasswordRequest:
    type: object
    required:
      - email
    properties:
      email:
        type: string
        format: email
        example: "<EMAIL>"
        description: "Email address of the user requesting password reset"

  ResetPasswordRequest:
    type: object
    required:
      - token
      - new_password
    properties:
      token:
        type: string
        example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        description: "Password reset token received via email"
      new_password:
        type: string
        example: "NewPassword123!"
        minLength: 8
        description: |
          New password for the user. Must meet strength requirements:
          - Minimum 8 characters
          - At least one uppercase letter
          - At least one lowercase letter
          - At least one number
          - At least one special character

  ChangePasswordRequest:
    type: object
    required:
      - current_password
      - new_password
    properties:
      current_password:
        type: string
        example: "CurrentPassword123!"
        description: "User's current password"
      new_password:
        type: string
        example: "NewPassword123!"
        minLength: 8
        description: |
          New password for the user. Must meet strength requirements:
          - Minimum 8 characters
          - At least one uppercase letter
          - At least one lowercase letter
          - At least one number
          - At least one special character
          - Must be different from current password

  # User Management Schemas
  UserWithRoles:
    type: object
    allOf:
      - $ref: '#/definitions/User'
      - type: object
        properties:
          roles:
            type: array
            items:
              $ref: '#/definitions/Role'
            description: "User's assigned roles"
          role_names:
            type: array
            items:
              type: string
            example: ["admin", "member"]
            description: "List of role names"
          is_admin:
            type: boolean
            example: true
            description: "Whether user has admin privileges"

  UserProfileResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "User profile retrieved successfully"
      data:
        $ref: '#/definitions/UserWithRoles'
      code:
        type: integer
        example: 200

  UserProfileUpdate:
    type: object
    properties:
      username:
        type: string
        example: "updated_username"
        minLength: 3
        maxLength: 100
        description: "New username (3-100 characters)"
      email:
        type: string
        format: email
        example: "<EMAIL>"
        description: "New email address (must be unique)"
      phone:
        type: string
        example: "******-987-6543"
        description: "Phone number"
      bio:
        type: string
        example: "Updated bio information"
        description: "User biography"

  UserListPaginatedResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Users retrieved successfully"
      data:
        type: object
        properties:
          users:
            type: array
            items:
              $ref: '#/definitions/UserWithRoles'
          pagination:
            type: object
            properties:
              page:
                type: integer
                example: 1
                description: "Current page number"
              pages:
                type: integer
                example: 5
                description: "Total number of pages"
              per_page:
                type: integer
                example: 20
                description: "Items per page"
              total:
                type: integer
                example: 95
                description: "Total number of users"
              has_next:
                type: boolean
                example: true
                description: "Whether there is a next page"
              has_prev:
                type: boolean
                example: false
                description: "Whether there is a previous page"
      code:
        type: integer
        example: 200

  Role:
    type: object
    properties:
      id:
        type: integer
        example: 1
      name:
        type: string
        example: "admin"
        description: "Role name"
      description:
        type: string
        example: "System administrator with full access"
        description: "Role description"
      created_at:
        type: string
        format: date-time
        example: "2024-01-01T10:00:00Z"
      updated_at:
        type: string
        format: date-time
        example: "2024-01-01T10:00:00Z"

  Permission:
    type: object
    properties:
      id:
        type: integer
        example: 1
      name:
        type: string
        example: "manage_users"
        description: "Permission name"
      description:
        type: string
        example: "Create, update, delete users"
        description: "Permission description"
      resource:
        type: string
        example: "user"
        description: "Resource this permission applies to"
      action:
        type: string
        example: "manage"
        description: "Action this permission allows"

  UserRolesResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "User roles retrieved successfully"
      data:
        type: array
        items:
          $ref: '#/definitions/Role'
      code:
        type: integer
        example: 200

  UserPermissionsResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "User permissions retrieved successfully"
      data:
        type: array
        items:
          $ref: '#/definitions/Permission'
      code:
        type: integer
        example: 200

  AssignRoleRequest:
    type: object
    required:
      - role_name
    properties:
      role_name:
        type: string
        example: "admin"
        enum: [admin, member, project_owner, project_manager, project_member, project_viewer]
        description: "Name of the role to assign"

  # Task Schemas
  Task:
    type: object
    properties:
      id:
        type: integer
        example: 1
      title:
        type: string
        example: "Complete project documentation"
      description:
        type: string
        x-nullable: true
        example: "Write comprehensive documentation for the project"
      status:
        type: string
        example: "To Do"
        enum: ["To Do", "In Progress", "Done"]
      est_time:
        type: number
        format: float
        x-nullable: true
        example: 4.5
        description: "Estimated time in hours"
      due_date:
        type: string
        format: date-time
        x-nullable: true
        example: "2024-12-31T23:59:59Z"
      priority:
        type: string
        example: "Medium"
        enum: ["Low", "Medium", "High"]
      assignee_id:
        type: integer
        x-nullable: true
        example: 1
      project_id:
        type: integer
        x-nullable: true
        example: 1
      created_by:
        type: integer
        example: 1
      created_at:
        type: string
        format: date-time
        example: "2024-01-01T10:00:00Z"
      updated_at:
        type: string
        format: date-time
        example: "2024-01-01T10:00:00Z"
      deleted_at:
        type: string
        format: date-time
        x-nullable: true

  TaskCreate:
    type: object
    required:
      - title
    properties:
      title:
        type: string
        example: "Complete project documentation"
        description: "Task title (required)"
        minLength: 1
        maxLength: 255
      description:
        type: string
        x-nullable: true
        example: "Write comprehensive documentation for the project"
      status:
        type: string
        example: "To Do"
        default: "To Do"
        enum: ["To Do", "In Progress", "Done"]
      est_time:
        type: number
        format: float
        x-nullable: true
        example: 4.5
        description: "Estimated time in hours"
        minimum: 0
      due_date:
        type: string
        format: date-time
        x-nullable: true
        example: "2024-12-31T23:59:59Z"
        description: "Due date in ISO 8601 format"
      priority:
        type: string
        example: "Medium"
        default: "Medium"
        enum: ["Low", "Medium", "High"]
      assignee_id:
        type: integer
        x-nullable: true
        example: 1
        description: "ID of the user assigned to this task"
      project_id:
        type: integer
        x-nullable: true
        example: 1
        description: "ID of the project this task belongs to"

  TaskUpdate:
    type: object
    properties:
      title:
        type: string
        example: "Updated task title"
        minLength: 1
        maxLength: 255
      description:
        type: string
        x-nullable: true
        example: "Updated task description"
      status:
        type: string
        example: "In Progress"
        enum: ["To Do", "In Progress", "Done"]
      est_time:
        type: number
        format: float
        x-nullable: true
        example: 6.0
        description: "Estimated time in hours"
        minimum: 0
      due_date:
        type: string
        format: date-time
        x-nullable: true
        example: "2024-12-31T23:59:59Z"
        description: "Due date in ISO 8601 format"
      priority:
        type: string
        example: "High"
        enum: ["Low", "Medium", "High"]
      assignee_id:
        type: integer
        x-nullable: true
        example: 2
        description: "ID of the user assigned to this task"
      project_id:
        type: integer
        x-nullable: true
        example: 1
        description: "ID of the project this task belongs to"

  # Project Definitions
  Project:
    type: object
    properties:
      id:
        type: integer
        example: 1
      name:
        type: string
        example: "My Project"
      description:
        type: string
        example: "Project description"
      status:
        type: string
        enum: [active, inactive, completed, on_hold]
        example: "active"
      priority:
        type: string
        enum: [low, medium, high, urgent]
        example: "high"
      start_date:
        type: string
        format: date
        example: "2024-01-01"
      end_date:
        type: string
        format: date
        example: "2024-12-31"
      user_id:
        type: integer
        example: 1
        description: "Project owner ID"
      created_by:
        type: integer
        example: 1
      created_at:
        type: string
        format: date-time
        example: "2024-01-01T10:00:00Z"
      updated_at:
        type: string
        format: date-time
        example: "2024-01-01T10:00:00Z"
      deleted_at:
        type: string
        format: date-time
        x-nullable: true

  ProjectCreate:
    type: object
    required:
      - name
    properties:
      name:
        type: string
        example: "New Project"
        minLength: 1
        maxLength: 255
      description:
        type: string
        example: "Project description"
      status:
        type: string
        enum: [active, inactive, completed, on_hold]
        default: "active"
      priority:
        type: string
        enum: [low, medium, high, urgent]
        default: "medium"
      start_date:
        type: string
        format: date
        example: "2024-01-01"
      end_date:
        type: string
        format: date
        example: "2024-12-31"
      user_id:
        type: integer
        example: 1
        description: "Project owner ID (optional, defaults to creator)"

  ProjectUpdate:
    type: object
    properties:
      name:
        type: string
        example: "Updated Project"
        minLength: 1
        maxLength: 255
      description:
        type: string
        example: "Updated description"
      status:
        type: string
        enum: [active, inactive, completed, on_hold]
      priority:
        type: string
        enum: [low, medium, high, urgent]
      start_date:
        type: string
        format: date
        example: "2024-01-01"
      end_date:
        type: string
        format: date
        example: "2024-12-31"
      user_id:
        type: integer
        example: 1

  ProjectResponse:
    type: object
    properties:
      code:
        type: integer
        example: 200
      data:
        type: object
        properties:
          project:
            $ref: '#/definitions/Project'
      message:
        type: string
        example: "Project retrieved successfully"
      success:
        type: boolean
        example: true

  ProjectListResponse:
    type: object
    properties:
      code:
        type: integer
        example: 200
      data:
        type: object
        properties:
          projects:
            type: array
            items:
              $ref: '#/definitions/Project'
          count:
            type: integer
            example: 5
      message:
        type: string
        example: "Projects retrieved successfully"
      success:
        type: boolean
        example: true

  # Member Definitions
  ProjectMember:
    type: object
    properties:
      user_id:
        type: integer
        example: 1
      username:
        type: string
        example: "john_doe"
      email:
        type: string
        example: "<EMAIL>"
      role:
        type: string
        enum: [project_owner, project_member, project_viewer]
        example: "project_member"
      role_description:
        type: string
        example: "Project member with limited permissions"
      joined_at:
        type: string
        format: date-time
        example: "2024-01-01T10:00:00Z"

  AddMemberRequest:
    type: object
    required:
      - user_id
    properties:
      user_id:
        type: integer
        example: 2
        description: "ID of user to add as member"
      role:
        type: string
        enum: [project_member, project_viewer]
        default: "project_member"
        example: "project_member"
        description: "Role to assign to the member"

  UpdateMemberRoleRequest:
    type: object
    required:
      - role
    properties:
      role:
        type: string
        enum: [project_member, project_viewer]
        example: "project_viewer"
        description: "New role for the member"

  ProjectMembersResponse:
    type: object
    properties:
      code:
        type: integer
        example: 200
      data:
        type: object
        properties:
          project_id:
            type: integer
            example: 1
          project_name:
            type: string
            example: "My Project"
          members:
            type: array
            items:
              $ref: '#/definitions/ProjectMember'
          total_members:
            type: integer
            example: 3
      message:
        type: string
        example: "Project members retrieved successfully"
      success:
        type: boolean
        example: true

  MemberResponse:
    type: object
    properties:
      code:
        type: integer
        example: 201
      data:
        type: object
        properties:
          member:
            $ref: '#/definitions/ProjectMember'
      message:
        type: string
        example: "Member added successfully"
      success:
        type: boolean
        example: true

  MemberUpdateResponse:
    type: object
    properties:
      code:
        type: integer
        example: 200
      data:
        type: object
        properties:
          member:
            type: object
            properties:
              user_id:
                type: integer
                example: 2
              username:
                type: string
                example: "jane_doe"
              email:
                type: string
                example: "<EMAIL>"
              old_role:
                type: string
                example: "project_member"
              new_role:
                type: string
                example: "project_viewer"
              updated_at:
                type: string
                format: date-time
                example: "2024-01-01T10:00:00Z"
      message:
        type: string
        example: "Member role updated successfully"
      success:
        type: boolean
        example: true

  MemberRemoveResponse:
    type: object
    properties:
      code:
        type: integer
        example: 200
      data:
        type: object
        properties:
          removed_member:
            type: object
            properties:
              user_id:
                type: integer
                example: 2
              username:
                type: string
                example: "jane_doe"
      message:
        type: string
        example: "Member removed successfully"
      success:
        type: boolean
        example: true

  UserProjectsResponse:
    type: object
    properties:
      code:
        type: integer
        example: 200
      data:
        type: object
        properties:
          projects:
            type: array
            items:
              type: object
              properties:
                project_id:
                  type: integer
                  example: 1
                name:
                  type: string
                  example: "My Project"
                description:
                  type: string
                  example: "Project description"
                status:
                  type: string
                  example: "active"
                priority:
                  type: string
                  example: "high"
                role:
                  type: string
                  example: "project_owner"
                is_creator:
                  type: boolean
                  example: true
                created_at:
                  type: string
                  format: date-time
                  example: "2024-01-01T10:00:00Z"
                joined_at:
                  type: string
                  format: date-time
                  example: "2024-01-01T10:00:00Z"
          total_projects:
            type: integer
            example: 5
      message:
        type: string
        example: "User projects retrieved successfully"
      success:
        type: boolean
        example: true

  # Statistics Definitions
  DashboardStatsResponse:
    type: object
    properties:
      code:
        type: integer
        example: 200
      data:
        type: object
        properties:
          user_id:
            type: integer
            example: 1
          username:
            type: string
            example: "john_doe"
          dashboard:
            type: object
            properties:
              overview:
                type: object
                properties:
                  total_projects:
                    type: integer
                    example: 5
                  active_projects:
                    type: integer
                    example: 3
                  completed_projects:
                    type: integer
                    example: 2
                  total_tasks_in_projects:
                    type: integer
                    example: 25
              my_tasks:
                type: object
                properties:
                  assigned:
                    type: integer
                    example: 8
                  completed:
                    type: integer
                    example: 5
                  pending:
                    type: integer
                    example: 3
                  overdue:
                    type: integer
                    example: 1
              recent_activity:
                type: object
                properties:
                  projects_created_week:
                    type: integer
                    example: 1
                  tasks_created_week:
                    type: integer
                    example: 3
                  tasks_completed_week:
                    type: integer
                    example: 2
      message:
        type: string
        example: "Dashboard statistics retrieved successfully"
      success:
        type: boolean
        example: true

  UserStatsResponse:
    type: object
    properties:
      code:
        type: integer
        example: 200
      data:
        type: object
        properties:
          user_id:
            type: integer
            example: 1
          username:
            type: string
            example: "john_doe"
          email:
            type: string
            example: "<EMAIL>"
          statistics:
            type: object
            properties:
              projects:
                type: object
                properties:
                  total:
                    type: integer
                    example: 5
                  created:
                    type: integer
                    example: 3
                  member_of:
                    type: integer
                    example: 2
              tasks:
                type: object
                properties:
                  created:
                    type: integer
                    example: 15
                  assigned:
                    type: integer
                    example: 8
                  completed:
                    type: integer
                    example: 5
                  overdue:
                    type: integer
                    example: 1
                  completion_rate:
                    type: number
                    example: 62.5
              reliability:
                type: object
                description: "User's reliability metrics"
                properties:
                  score:
                    type: number
                    format: float
                    example: 75.5
                    description: "Overall reliability score (0-100)"
                  metrics:
                    type: object
                    properties:
                      task_completion_ratio:
                        type: number
                        format: float
                        example: 80.0
                      on_time_ratio:
                        type: number
                        format: float
                        example: 68.75
                      assigned_tasks:
                        type: integer
                        example: 15
                      completed_tasks:
                        type: integer
                        example: 12
                      completed_on_time:
                        type: integer
                        example: 11
                      time_period_days:
                        type: integer
                        example: 30
              recent_activity:
                type: object
                properties:
                  tasks_created_week:
                    type: integer
                    example: 3
                  tasks_completed_week:
                    type: integer
                    example: 2
      message:
        type: string
        example: "User statistics retrieved successfully"
      success:
        type: boolean
        example: true

  ProjectStatsResponse:
    type: object
    properties:
      code:
        type: integer
        example: 200
      data:
        type: object
        properties:
          project_id:
            type: integer
            example: 1
          project_name:
            type: string
            example: "My Project"
          project_status:
            type: string
            example: "active"
          project_priority:
            type: string
            example: "high"
          statistics:
            type: object
            properties:
              tasks:
                type: object
                properties:
                  total:
                    type: integer
                    example: 10
                  completed:
                    type: integer
                    example: 4
                  in_progress:
                    type: integer
                    example: 3
                  todo:
                    type: integer
                    example: 3
                  overdue:
                    type: integer
                    example: 1
                  assigned:
                    type: integer
                    example: 8
                  unassigned:
                    type: integer
                    example: 2
                  recent_week:
                    type: integer
                    example: 2
              priority_breakdown:
                type: object
                properties:
                  high:
                    type: integer
                    example: 3
                  medium:
                    type: integer
                    example: 5
                  low:
                    type: integer
                    example: 2
              time_estimation:
                type: object
                properties:
                  total_estimated_hours:
                    type: number
                    example: 40.5
                  completed_estimated_hours:
                    type: number
                    example: 16.0
                  remaining_estimated_hours:
                    type: number
                    example: 24.5
              progress:
                type: object
                properties:
                  percentage:
                    type: number
                    example: 40.0
                  completed_tasks:
                    type: integer
                    example: 4
                  total_tasks:
                    type: integer
                    example: 10
              team:
                type: object
                properties:
                  total_members:
                    type: integer
                    example: 3
      message:
        type: string
        example: "Project statistics retrieved successfully"
      success:
        type: boolean
        example: true
        
  ReliabilityResponse:
    type: object
    properties:
      code:
        type: integer
        example: 200
      data:
        type: object
        properties:
          user_id:
            type: integer
            example: 1
          reliability:
            type: object
            properties:
              score:
                type: number
                format: float
                example: 75.5
                description: "Overall reliability score (0-100)"
              metrics:
                type: object
                properties:
                  task_completion_ratio:
                    type: number
                    format: float
                    example: 80.0
                    description: "Percentage of assigned tasks completed"
                  on_time_ratio:
                    type: number
                    format: float
                    example: 68.75
                    description: "Percentage of completed tasks finished on time"
                  assigned_tasks:
                    type: integer
                    example: 15
                    description: "Total tasks assigned in the time period"
                  completed_tasks:
                    type: integer
                    example: 12
                    description: "Total tasks completed in the time period"
                  completed_on_time:
                    type: integer
                    example: 11
                    description: "Tasks completed before their due date"
                  time_period_days:
                    type: integer
                    example: 30
                    description: "Time period in days used for calculation"
      message:
        type: string
        example: "Reliability metrics retrieved successfully"
      success:
        type: boolean
        example: true

  # GitHub Sync Definitions
  GitHubRepository:
    type: object
    properties:
      id:
        type: string
        example: "123456"
        description: "GitHub repository ID"
      name:
        type: string
        example: "my-awesome-project"
        description: "Repository name"
      full_name:
        type: string
        example: "username/my-awesome-project"
        description: "Full repository name (owner/repo)"
      description:
        type: string
        x-nullable: true
        example: "An awesome project description"
        description: "Repository description"
      html_url:
        type: string
        example: "https://github.com/username/my-awesome-project"
        description: "Repository URL"
      private:
        type: boolean
        example: false
        description: "Whether the repository is private"
      language:
        type: string
        x-nullable: true
        example: "Python"
        description: "Primary programming language"
      stargazers_count:
        type: integer
        example: 42
        description: "Number of stars"
      open_issues_count:
        type: integer
        example: 5
        description: "Number of open issues"
      created_at:
        type: string
        format: date-time
        example: "2023-01-01T00:00:00Z"
        description: "Repository creation date"
      updated_at:
        type: string
        format: date-time
        example: "2023-12-01T00:00:00Z"
        description: "Repository last update date"

  GitHubRepositoriesResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "All repositories fetched successfully"
      data:
        type: object
        properties:
          repositories:
            type: array
            items:
              $ref: '#/definitions/GitHubRepository'
          total_count:
            type: integer
            example: 10
            description: "Total number of repositories"
      code:
        type: integer
        example: 200

  GitHubSyncRequest:
    type: object
    required:
      - repository_urls
    properties:
      repository_urls:
        type: array
        items:
          type: string
        example:
          - "https://github.com/username/repo1"
          - "https://github.com/username/repo2"
        description: "List of GitHub repository URLs to sync"

  GitHubSyncedProject:
    type: object
    properties:
      action:
        type: string
        enum: ["created", "updated"]
        example: "created"
        description: "Action performed on the project"
      project:
        type: object
        properties:
          id:
            type: integer
            example: 1
          name:
            type: string
            example: "my-awesome-project"
          description:
            type: string
            example: "An awesome project description"
          github_repo_id:
            type: string
            example: "123456"
          github_repo_url:
            type: string
            example: "https://github.com/username/my-awesome-project"
          github_repo_full_name:
            type: string
            example: "username/my-awesome-project"
          is_github_synced:
            type: boolean
            example: true
          last_github_sync:
            type: string
            format: date-time
            example: "2023-12-01T12:00:00Z"

  GitHubSyncResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Repositories synced successfully"

  # Activity Log related schemas
  ActivityLog:
    type: object
    properties:
      id:
        type: integer
        example: 1
      user_id:
        type: integer
        example: 2
      project_id:
        type: integer
        example: 3
        nullable: true
      task_id:
        type: integer
        example: 4
        nullable: true
      action:
        type: string
        example: "create_task"
      entity_type:
        type: string
        example: "task"
      entity_id:
        type: integer
        example: 4
        nullable: true
      description:
        type: string
        example: "Created task My Task"
      details:
        type: object
        example: {"priority": "high", "status": "todo"}
      ip_address:
        type: string
        example: "***********"
        nullable: true
      created_at:
        type: string
        format: date-time
        example: "2023-01-01T12:00:00Z"

  ActivityLogsPagination:
    type: object
    properties:
      page:
        type: integer
        example: 1
      per_page:
        type: integer
        example: 20
      total_items:
        type: integer
        example: 100
      total_pages:
        type: integer
        example: 5

  ActivityLogsResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Activity logs retrieved successfully"
      data:
        type: object
        properties:
          logs:
            type: array
            items:
              $ref: '#/definitions/ActivityLog'
          pagination:
            $ref: '#/definitions/ActivityLogsPagination'
      code:
        type: integer
        example: 200

  RecentActivityLogsResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Recent activity logs retrieved successfully"
      data:
        type: array
        items:
          $ref: '#/definitions/ActivityLog'
      code:
        type: integer
        example: 200
      data:
        type: object
        properties:
          synced_projects:
            type: array
            items:
              $ref: '#/definitions/GitHubSyncedProject'
          total_synced:
            type: integer
            example: 2
            description: "Number of projects synced"
          errors:
            type: array
            items:
              type: string
            example: []
            description: "List of errors encountered during sync"
      code:
        type: integer
        example: 200

  GitHubSyncedTask:
    type: object
    properties:
      action:
        type: string
        enum: ["created", "updated"]
        example: "created"
        description: "Action performed on the task"
      task:
        type: object
        properties:
          id:
            type: integer
            example: 1
          title:
            type: string
            example: "Fix authentication bug"
          description:
            type: string
            example: "Issue description from GitHub"
          status:
            type: string
            example: "To Do"
          github_issue_id:
            type: string
            example: "789012"
          github_issue_number:
            type: integer
            example: 1
          github_issue_url:
            type: string
            example: "https://github.com/username/repo/issues/1"
          is_github_synced:
            type: boolean
            example: true
          last_github_sync:
            type: string
            format: date-time
            example: "2023-12-01T12:00:00Z"

  GitHubIssuesSyncResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Issues synced successfully"
      data:
        type: object
        properties:
          synced_tasks:
            type: array
            items:
              $ref: '#/definitions/GitHubSyncedTask'
          total_synced:
            type: integer
            example: 5
            description: "Number of tasks synced"
          total_issues:
            type: integer
            example: 5
            description: "Total number of issues processed"
          errors:
            type: array
            items:
              type: string
            example: []
            description: "List of errors encountered during sync"
          project:
            type: object
            properties:
              id:
                type: integer
                example: 1
              name:
                type: string
                example: "my-awesome-project"
              github_repo_full_name:
                type: string
                example: "username/my-awesome-project"
              last_github_sync:
                type: string
                format: date-time
                example: "2023-12-01T12:00:00Z"
      code:
        type: integer
        example: 200

  GitHubCompleteSyncResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Project synced completely"
      data:
        type: object
        properties:
          project_updated:
            type: boolean
            example: true
            description: "Whether the project was updated"
          synced_tasks:
            type: array
            items:
              $ref: '#/definitions/GitHubSyncedTask'
          total_synced:
            type: integer
            example: 5
            description: "Number of tasks synced"
          total_issues:
            type: integer
            example: 5
            description: "Total number of issues processed"
          errors:
            type: array
            items:
              type: string
            example: []
            description: "List of errors encountered during sync"
          project:
            type: object
            properties:
              id:
                type: integer
                example: 1
              name:
                type: string
                example: "my-awesome-project"
              github_repo_full_name:
                type: string
                example: "username/my-awesome-project"
              last_github_sync:
                type: string
                format: date-time
                example: "2023-12-01T12:00:00Z"
      code:
        type: integer
        example: 200

  # Webhook Definitions
  WebhookCreateRequest:
    type: object
    required:
      - repository_urls
    properties:
      repository_urls:
        type: array
        items:
          type: string
          format: uri
        example:
          - "https://github.com/owner/repo1"
          - "https://github.com/owner/repo2"
        description: List of GitHub repository URLs
      webhook_url:
        type: string
        format: uri
        example: "https://your-domain.com/webhooks/github"
        description: Custom webhook URL (optional, defaults to current domain)

  WebhookCreateResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Successfully created 2 webhook(s)"
      data:
        type: object
        properties:
          created_webhooks:
            type: array
            items:
              type: object
              properties:
                webhook_id:
                  type: integer
                  example: 1
                github_webhook_id:
                  type: string
                  example: "********"
                repository:
                  type: string
                  example: "owner/repo"
                events:
                  type: array
                  items:
                    type: string
                  example: ["push", "issues", "pull_request"]
          errors:
            type: array
            items:
              type: string
            example: []
      code:
        type: integer
        example: 200

  WebhookListResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Found 2 webhook(s)"
      data:
        type: object
        properties:
          webhooks:
            type: array
            items:
              $ref: '#/definitions/GitHubWebhook'
      code:
        type: integer
        example: 200

  WebhookEventsResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Found 10 event(s) for webhook 1"
      data:
        type: object
        properties:
          events:
            type: array
            items:
              $ref: '#/definitions/WebhookEvent'
          pagination:
            type: object
            properties:
              page:
                type: integer
                example: 1
              per_page:
                type: integer
                example: 20
              total:
                type: integer
                example: 50
              pages:
                type: integer
                example: 3
              has_next:
                type: boolean
                example: true
              has_prev:
                type: boolean
                example: false
      code:
        type: integer
        example: 200

  GitHubWebhook:
    type: object
    properties:
      id:
        type: integer
        description: Webhook ID
        example: 1
      user_id:
        type: integer
        description: User ID who owns the webhook
        example: 123
      project_id:
        type: integer
        nullable: true
        description: Associated project ID
        example: 456
      github_webhook_id:
        type: string
        description: GitHub webhook ID
        example: "********"
      github_repo_full_name:
        type: string
        description: Repository full name (owner/repo)
        example: "owner/repository"
      webhook_url:
        type: string
        format: uri
        description: Webhook endpoint URL
        example: "https://your-domain.com/webhooks/github"
      events:
        type: array
        items:
          type: string
        description: List of GitHub events to listen for
        example: ["push", "issues", "pull_request"]
      is_active:
        type: boolean
        description: Whether the webhook is active
        example: true
      created_at:
        type: string
        format: date-time
        description: Webhook creation timestamp
        example: "2024-01-15T10:30:00Z"
      updated_at:
        type: string
        format: date-time
        description: Webhook last update timestamp
        example: "2024-01-15T10:30:00Z"
      last_ping:
        type: string
        format: date-time
        nullable: true
        description: Last ping from GitHub
        example: "2024-01-15T12:45:00Z"

  WebhookEvent:
    type: object
    properties:
      id:
        type: integer
        description: Event ID
        example: 1
      webhook_id:
        type: integer
        description: Associated webhook ID
        example: 1
      github_delivery_id:
        type: string
        description: GitHub delivery ID
        example: "********-1234-1234-1234-*********012"
      event_type:
        type: string
        description: GitHub event type
        example: "push"
      action:
        type: string
        nullable: true
        description: Event action (if applicable)
        example: "opened"
      processed:
        type: boolean
        description: Whether the event has been processed
        example: true
      processed_at:
        type: string
        format: date-time
        nullable: true
        description: Event processing timestamp
        example: "2024-01-15T10:35:00Z"
      error_message:
        type: string
        nullable: true
        description: Error message if processing failed
        example: null
      created_at:
        type: string
        format: date-time
        description: Event creation timestamp
        example: "2024-01-15T10:30:00Z"

  # Telegram Integration Models
  TelegramIntegration:
    type: object
    properties:
      id:
        type: integer
        example: 1
      user_id:
        type: integer
        example: 1
      telegram_user_id:
        type: string
        example: "*********"
      telegram_username:
        type: string
        example: "john_doe"
        x-nullable: true
      telegram_first_name:
        type: string
        example: "John"
        x-nullable: true
      telegram_last_name:
        type: string
        example: "Doe"
        x-nullable: true
      chat_id:
        type: string
        example: "*********"
      is_active:
        type: boolean
        example: true
      language_code:
        type: string
        example: "en"
      created_at:
        type: string
        format: date-time
        example: "2024-01-01T10:00:00Z"
      updated_at:
        type: string
        format: date-time
        example: "2024-01-01T10:00:00Z"
      last_interaction:
        type: string
        format: date-time
        example: "2024-01-01T10:00:00Z"

  TelegramSubscription:
    type: object
    properties:
      id:
        type: integer
        example: 1
      telegram_integration_id:
        type: integer
        example: 1
      notification_type:
        type: string
        example: "github_notifications"
        enum: ["github_notifications"]
      is_enabled:
        type: boolean
        example: true
      settings:
        type: object
        x-nullable: true
        example: {}
      created_at:
        type: string
        format: date-time
        example: "2024-01-01T10:00:00Z"
      updated_at:
        type: string
        format: date-time
        example: "2024-01-01T10:00:00Z"

  TelegramNotificationLog:
    type: object
    properties:
      id:
        type: integer
        example: 1
      telegram_integration_id:
        type: integer
        example: 1
      notification_type:
        type: string
        example: "github_notifications"
      message_content:
        type: string
        example: "🚀 New Push\n\n📁 Repository: user/repo\n🌿 Branch: main"
      telegram_message_id:
        type: string
        example: "123"
        x-nullable: true
      status:
        type: string
        example: "sent"
        enum: ["pending", "sent", "failed"]
      error_message:
        type: string
        x-nullable: true
        example: null
      webhook_event_id:
        type: integer
        x-nullable: true
        example: 1
      created_at:
        type: string
        format: date-time
        example: "2024-01-01T10:00:00Z"
      sent_at:
        type: string
        format: date-time
        x-nullable: true
        example: "2024-01-01T10:00:00Z"

  # Telegram Request/Response Models
  TelegramLinkRequest:
    type: object
    required:
      - telegram_user_id
      - chat_id
    properties:
      telegram_user_id:
        type: string
        example: "*********"
        description: "Telegram user ID"
      chat_id:
        type: string
        example: "*********"
        description: "Telegram chat ID"
      telegram_data:
        type: object
        description: "Additional Telegram user data"
        properties:
          username:
            type: string
            example: "john_doe"
          first_name:
            type: string
            example: "John"
          last_name:
            type: string
            example: "Doe"
          language_code:
            type: string
            example: "en"

  TelegramSubscriptionRequest:
    type: object
    required:
      - notification_type
    properties:
      notification_type:
        type: string
        example: "github_notifications"
        enum: ["github_notifications"]
        description: "Type of notification to subscribe/unsubscribe"
      enabled:
        type: boolean
        example: true
        default: true
        description: "Enable or disable the subscription"

  TelegramStatusResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Telegram status retrieved"
      data:
        type: object
        properties:
          linked:
            type: boolean
            example: true
          integration:
            $ref: '#/definitions/TelegramIntegration'
          subscriptions:
            type: array
            items:
              $ref: '#/definitions/TelegramSubscription'
      code:
        type: integer
        example: 200

  TelegramLinkResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Telegram account linked successfully"
      data:
        $ref: '#/definitions/TelegramIntegration'
      code:
        type: integer
        example: 200

  TelegramSubscriptionResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Successfully subscribed to github_notifications"
      data:
        $ref: '#/definitions/TelegramSubscription'
      code:
        type: integer
        example: 200

  TelegramWebhookUpdate:
    type: object
    description: "Telegram webhook update payload (varies by update type)"
    properties:
      update_id:
        type: integer
        example: *********
      message:
        type: object
        properties:
          message_id:
            type: integer
            example: 1
          from:
            type: object
            properties:
              id:
                type: integer
                example: *********
              is_bot:
                type: boolean
                example: false
              first_name:
                type: string
                example: "John"
              username:
                type: string
                example: "john_doe"
              language_code:
                type: string
                example: "en"
          chat:
            type: object
            properties:
              id:
                type: integer
                example: *********
              first_name:
                type: string
                example: "John"
              username:
                type: string
                example: "john_doe"
              type:
                type: string
                example: "private"
          date:
            type: integer
            example: 1640995200
          text:
            type: string
            example: "/start"
            description: "Bot command text. Examples: /start, /link <EMAIL> password, /subscribe github_notifications"

  # Telegram Webhook Management Models
  TelegramWebhookSetupRequest:
    type: object
    required:
      - webhook_url
    properties:
      webhook_url:
        type: string
        format: uri
        example: "https://your-domain.com/telegram/webhook"
        description: "HTTPS URL for webhook endpoint"
      secret_token:
        type: string
        example: "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"
        description: "Secret token for webhook security (optional, will be generated if not provided)"

  TelegramWebhookSetupResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Telegram webhook setup successfully"
      data:
        type: object
        properties:
          webhook_url:
            type: string
            example: "https://your-domain.com/telegram/webhook"
          secret_token:
            type: string
            example: "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"
          telegram_response:
            type: object
            properties:
              ok:
                type: boolean
                example: true
              result:
                type: boolean
                example: true
              description:
                type: string
                example: "Webhook was set"
          setup_time:
            type: string
            format: date-time
            example: "2024-01-15T10:30:00Z"
      code:
        type: integer
        example: 200

  TelegramWebhookInfoResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Webhook information retrieved"
      data:
        type: object
        properties:
          url:
            type: string
            example: "https://your-domain.com/telegram/webhook"
          has_custom_certificate:
            type: boolean
            example: false
          pending_update_count:
            type: integer
            example: 0
          last_error_date:
            type: integer
            x-nullable: true
            example: null
          last_error_message:
            type: string
            x-nullable: true
            example: null
          max_connections:
            type: integer
            example: 40
          allowed_updates:
            type: array
            items:
              type: string
            example: ["message", "callback_query"]
          ip_address:
            type: string
            x-nullable: true
            example: "***********"
          last_synchronization_error_date:
            type: integer
            x-nullable: true
            example: null
      code:
        type: integer
        example: 200

  TelegramBotInfoResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Bot information retrieved"
      data:
        type: object
        properties:
          id:
            type: integer
            example: *********
          is_bot:
            type: boolean
            example: true
          first_name:
            type: string
            example: "TMS Bot"
          username:
            type: string
            example: "tms_notification_bot"
          can_join_groups:
            type: boolean
            example: false
          can_read_all_group_messages:
            type: boolean
            example: false
          supports_inline_queries:
            type: boolean
            example: false
          can_connect_to_business:
            type: boolean
            example: false
          has_main_web_app:
            type: boolean
            example: false
      code:
        type: integer
        example: 200

  TelegramWebhookDeleteResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Telegram webhook deleted successfully"
      data:
        type: object
        properties:
          deleted_at:
            type: string
            format: date-time
            example: "2024-01-15T10:30:00Z"
      code:
        type: integer
        example: 200

  # Jira Integration Definitions
  JiraAuthResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Jira authentication successful"
      data:
        type: object
        properties:
          access_token:
            type: string
            example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
          user:
            type: object
            properties:
              id:
                type: integer
                example: 1
              username:
                type: string
                example: "john_doe"
              email:
                type: string
                example: "<EMAIL>"
              jira_account_id:
                type: string
                example: "5d53f3cbc6b9320d9ea1b14f"
              jira_display_name:
                type: string
                example: "John Doe"
      code:
        type: integer
        example: 200

  JiraStatusResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Jira integration status retrieved"
      data:
        type: object
        properties:
          connected:
            type: boolean
            example: true
          platform:
            type: string
            example: "jira"
          cloud_id:
            type: string
            example: "ari:cloud:jira::site/********-1234-1234-1234-*********012"
          platform_user_id:
            type: string
            example: "5d53f3cbc6b9320d9ea1b14f"
      code:
        type: integer
        example: 200

  JiraProjectsResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Jira projects retrieved successfully"
      data:
        type: object
        properties:
          projects:
            type: array
            items:
              $ref: '#/definitions/JiraProject'
      code:
        type: integer
        example: 200

  JiraProject:
    type: object
    properties:
      id:
        type: string
        example: "10000"
      key:
        type: string
        example: "PROJ"
      name:
        type: string
        example: "Sample Project"
      description:
        type: string
        example: "This is a sample project"
      projectTypeKey:
        type: string
        example: "software"
      lead:
        type: object
        properties:
          accountId:
            type: string
            example: "5d53f3cbc6b9320d9ea1b14f"
          displayName:
            type: string
            example: "John Doe"

  JiraProjectSyncResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Jira project synced successfully"
      data:
        type: object
        properties:
          project:
            type: object
            properties:
              id:
                type: integer
                example: 1
              name:
                type: string
                example: "Sample Project"
              jira_project_key:
                type: string
                example: "PROJ"
              jira_project_id:
                type: string
                example: "10000"
      code:
        type: integer
        example: 200

  JiraIssuesResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Jira issues retrieved successfully"
      data:
        type: object
        properties:
          issues:
            type: array
            items:
              $ref: '#/definitions/JiraIssue'
          total:
            type: integer
            example: 25
      code:
        type: integer
        example: 200

  JiraIssue:
    type: object
    properties:
      id:
        type: string
        example: "10001"
      key:
        type: string
        example: "PROJ-1"
      summary:
        type: string
        example: "Sample issue"
      description:
        type: string
        example: "This is a sample issue description"
      status:
        type: object
        properties:
          name:
            type: string
            example: "To Do"
      priority:
        type: object
        properties:
          name:
            type: string
            example: "Medium"
      assignee:
        type: object
        properties:
          accountId:
            type: string
            example: "5d53f3cbc6b9320d9ea1b14f"
          displayName:
            type: string
            example: "John Doe"
      created:
        type: string
        format: date-time
        example: "2024-01-01T10:00:00.000+0000"
      updated:
        type: string
        format: date-time
        example: "2024-01-01T10:30:00.000+0000"

  JiraIssueResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Jira issue details retrieved successfully"
      data:
        type: object
        properties:
          issue:
            $ref: '#/definitions/JiraIssue'
      code:
        type: integer
        example: 200

  JiraIssueSyncResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Jira issue synced successfully"
      data:
        type: object
        properties:
          task:
            type: object
            properties:
              id:
                type: integer
                example: 1
              title:
                type: string
                example: "Sample issue"
              jira_issue_key:
                type: string
                example: "PROJ-1"
              jira_issue_id:
                type: string
                example: "10001"
      code:
        type: integer
        example: 200

  JiraTaskSyncResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Task synced to Jira successfully"
      data:
        type: object
        properties:
          jira_issue:
            type: object
            properties:
              id:
                type: string
                example: "10001"
              key:
                type: string
                example: "PROJ-1"
              self:
                type: string
                example: "https://your-domain.atlassian.net/rest/api/2/issue/10001"
      code:
        type: integer
        example: 200

  JiraCommentResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Comment added successfully"
      data:
        type: object
        properties:
          comment:
            type: object
            properties:
              id:
                type: string
                example: "10000"
              body:
                type: string
                example: "This is a comment from TMS"
              author:
                type: object
                properties:
                  accountId:
                    type: string
                    example: "5d53f3cbc6b9320d9ea1b14f"
                  displayName:
                    type: string
                    example: "John Doe"
              created:
                type: string
                format: date-time
                example: "2024-01-01T10:00:00.000+0000"
      code:
        type: integer
        example: 200

  JiraSearchResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Search results retrieved successfully"
      data:
        type: object
        properties:
          issues:
            type: array
            items:
              $ref: '#/definitions/JiraIssue'
          total:
            type: integer
            example: 15
          maxResults:
            type: integer
            example: 50
          startAt:
            type: integer
            example: 0
      code:
        type: integer
        example: 200
