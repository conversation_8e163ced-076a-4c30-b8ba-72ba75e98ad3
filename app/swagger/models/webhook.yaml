GitHubWebhook:
  type: object
  properties:
    id:
      type: integer
      description: Webhook ID
      example: 1
    user_id:
      type: integer
      description: User ID who owns the webhook
      example: 123
    project_id:
      type: integer
      nullable: true
      description: Associated project ID
      example: 456
    github_webhook_id:
      type: string
      description: GitHub webhook ID
      example: "12345678"
    github_repo_full_name:
      type: string
      description: Repository full name (owner/repo)
      example: "owner/repository"
    webhook_url:
      type: string
      format: uri
      description: Webhook endpoint URL
      example: "https://your-domain.com/webhooks/github"
    events:
      type: array
      items:
        type: string
      description: List of GitHub events to listen for
      example: ["push", "issues", "pull_request"]
    is_active:
      type: boolean
      description: Whether the webhook is active
      example: true
    created_at:
      type: string
      format: date-time
      description: Webhook creation timestamp
      example: "2024-01-15T10:30:00Z"
    updated_at:
      type: string
      format: date-time
      description: Webhook last update timestamp
      example: "2024-01-15T10:30:00Z"
    last_ping:
      type: string
      format: date-time
      nullable: true
      description: Last ping from GitHub
      example: "2024-01-15T12:45:00Z"

WebhookEvent:
  type: object
  properties:
    id:
      type: integer
      description: Event ID
      example: 1
    webhook_id:
      type: integer
      description: Associated webhook ID
      example: 1
    github_delivery_id:
      type: string
      description: GitHub delivery ID
      example: "12345678-1234-1234-1234-123456789012"
    event_type:
      type: string
      description: GitHub event type
      example: "push"
    action:
      type: string
      nullable: true
      description: Event action (if applicable)
      example: "opened"
    processed:
      type: boolean
      description: Whether the event has been processed
      example: true
    processed_at:
      type: string
      format: date-time
      nullable: true
      description: Event processing timestamp
      example: "2024-01-15T10:35:00Z"
    error_message:
      type: string
      nullable: true
      description: Error message if processing failed
      example: null
    created_at:
      type: string
      format: date-time
      description: Event creation timestamp
      example: "2024-01-15T10:30:00Z"
