# Telegram Integration Models

TelegramIntegration:
  type: object
  properties:
    id:
      type: integer
      example: 1
    user_id:
      type: integer
      example: 1
    telegram_user_id:
      type: string
      example: "*********"
    telegram_username:
      type: string
      example: "john_doe"
      x-nullable: true
    telegram_first_name:
      type: string
      example: "<PERSON>"
      x-nullable: true
    telegram_last_name:
      type: string
      example: "Doe"
      x-nullable: true
    chat_id:
      type: string
      example: "*********"
    is_active:
      type: boolean
      example: true
    language_code:
      type: string
      example: "en"
    created_at:
      type: string
      format: date-time
      example: "2024-01-01T10:00:00Z"
    updated_at:
      type: string
      format: date-time
      example: "2024-01-01T10:00:00Z"
    last_interaction:
      type: string
      format: date-time
      example: "2024-01-01T10:00:00Z"

TelegramSubscription:
  type: object
  properties:
    id:
      type: integer
      example: 1
    telegram_integration_id:
      type: integer
      example: 1
    notification_type:
      type: string
      example: "github_notifications"
      enum: ["github_notifications"]
    is_enabled:
      type: boolean
      example: true
    settings:
      type: object
      x-nullable: true
      example: {}
    created_at:
      type: string
      format: date-time
      example: "2024-01-01T10:00:00Z"
    updated_at:
      type: string
      format: date-time
      example: "2024-01-01T10:00:00Z"

TelegramNotificationLog:
  type: object
  properties:
    id:
      type: integer
      example: 1
    telegram_integration_id:
      type: integer
      example: 1
    notification_type:
      type: string
      example: "github_notifications"
    message_content:
      type: string
      example: "🚀 New Push\n\n📁 Repository: user/repo\n🌿 Branch: main"
    telegram_message_id:
      type: string
      example: "123"
      x-nullable: true
    status:
      type: string
      example: "sent"
      enum: ["pending", "sent", "failed"]
    error_message:
      type: string
      x-nullable: true
      example: null
    webhook_event_id:
      type: integer
      x-nullable: true
      example: 1
    created_at:
      type: string
      format: date-time
      example: "2024-01-01T10:00:00Z"
    sent_at:
      type: string
      format: date-time
      x-nullable: true
      example: "2024-01-01T10:00:00Z"

# Request/Response Models

TelegramLinkRequest:
  type: object
  required:
    - telegram_user_id
    - chat_id
  properties:
    telegram_user_id:
      type: string
      example: "*********"
      description: "Telegram user ID"
    chat_id:
      type: string
      example: "*********"
      description: "Telegram chat ID"
    telegram_data:
      type: object
      description: "Additional Telegram user data"
      properties:
        username:
          type: string
          example: "john_doe"
        first_name:
          type: string
          example: "John"
        last_name:
          type: string
          example: "Doe"
        language_code:
          type: string
          example: "en"

TelegramSubscriptionRequest:
  type: object
  required:
    - notification_type
  properties:
    notification_type:
      type: string
      example: "github_notifications"
      enum: ["github_notifications"]
      description: "Type of notification to subscribe/unsubscribe"
    enabled:
      type: boolean
      example: true
      default: true
      description: "Enable or disable the subscription"

TelegramStatusResponse:
  type: object
  properties:
    success:
      type: boolean
      example: true
    message:
      type: string
      example: "Telegram status retrieved"
    data:
      type: object
      properties:
        linked:
          type: boolean
          example: true
        integration:
          $ref: '#/definitions/TelegramIntegration'
        subscriptions:
          type: array
          items:
            $ref: '#/definitions/TelegramSubscription'
    code:
      type: integer
      example: 200

TelegramLinkResponse:
  type: object
  properties:
    success:
      type: boolean
      example: true
    message:
      type: string
      example: "Telegram account linked successfully"
    data:
      $ref: '#/definitions/TelegramIntegration'
    code:
      type: integer
      example: 200

TelegramSubscriptionResponse:
  type: object
  properties:
    success:
      type: boolean
      example: true
    message:
      type: string
      example: "Successfully subscribed to github_notifications"
    data:
      $ref: '#/definitions/TelegramSubscription'
    code:
      type: integer
      example: 200

TelegramWebhookUpdate:
  type: object
  description: "Telegram webhook update payload (varies by update type)"
  properties:
    update_id:
      type: integer
      example: *********
    message:
      type: object
      properties:
        message_id:
          type: integer
          example: 1
        from:
          type: object
          properties:
            id:
              type: integer
              example: *********
            is_bot:
              type: boolean
              example: false
            first_name:
              type: string
              example: "John"
            username:
              type: string
              example: "john_doe"
            language_code:
              type: string
              example: "en"
        chat:
          type: object
          properties:
            id:
              type: integer
              example: *********
            first_name:
              type: string
              example: "John"
            username:
              type: string
              example: "john_doe"
            type:
              type: string
              example: "private"
        date:
          type: integer
          example: 1640995200
        text:
          type: string
          example: "/start"

# Webhook Management Models

TelegramWebhookSetupRequest:
  type: object
  required:
    - webhook_url
  properties:
    webhook_url:
      type: string
      format: uri
      example: "https://your-domain.com/telegram/webhook"
      description: "HTTPS URL for webhook endpoint"
    secret_token:
      type: string
      example: "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"
      description: "Secret token for webhook security (optional, will be generated if not provided)"

TelegramWebhookSetupResponse:
  type: object
  properties:
    success:
      type: boolean
      example: true
    message:
      type: string
      example: "Telegram webhook setup successfully"
    data:
      type: object
      properties:
        webhook_url:
          type: string
          example: "https://your-domain.com/telegram/webhook"
        secret_token:
          type: string
          example: "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"
        telegram_response:
          type: object
          properties:
            ok:
              type: boolean
              example: true
            result:
              type: boolean
              example: true
            description:
              type: string
              example: "Webhook was set"
        setup_time:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"
    code:
      type: integer
      example: 200

TelegramWebhookInfoResponse:
  type: object
  properties:
    success:
      type: boolean
      example: true
    message:
      type: string
      example: "Webhook information retrieved"
    data:
      type: object
      properties:
        url:
          type: string
          example: "https://your-domain.com/telegram/webhook"
        has_custom_certificate:
          type: boolean
          example: false
        pending_update_count:
          type: integer
          example: 0
        last_error_date:
          type: integer
          x-nullable: true
          example: null
        last_error_message:
          type: string
          x-nullable: true
          example: null
        max_connections:
          type: integer
          example: 40
        allowed_updates:
          type: array
          items:
            type: string
          example: ["message", "callback_query"]
        ip_address:
          type: string
          x-nullable: true
          example: "***********"
        last_synchronization_error_date:
          type: integer
          x-nullable: true
          example: null
    code:
      type: integer
      example: 200

TelegramBotInfoResponse:
  type: object
  properties:
    success:
      type: boolean
      example: true
    message:
      type: string
      example: "Bot information retrieved"
    data:
      type: object
      properties:
        id:
          type: integer
          example: *********
        is_bot:
          type: boolean
          example: true
        first_name:
          type: string
          example: "TMS Bot"
        username:
          type: string
          example: "tms_notification_bot"
        can_join_groups:
          type: boolean
          example: false
        can_read_all_group_messages:
          type: boolean
          example: false
        supports_inline_queries:
          type: boolean
          example: false
        can_connect_to_business:
          type: boolean
          example: false
        has_main_web_app:
          type: boolean
          example: false
    code:
      type: integer
      example: 200

TelegramWebhookDeleteResponse:
  type: object
  properties:
    success:
      type: boolean
      example: true
    message:
      type: string
      example: "Telegram webhook deleted successfully"
    data:
      type: object
      properties:
        deleted_at:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"
    code:
      type: integer
      example: 200
