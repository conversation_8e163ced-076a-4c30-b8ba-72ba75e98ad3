"""
Webhook-related models for Task Management System
"""

from app.helpers.extensions import db
from datetime import datetime


class GitHubWebhook(db.Model):
    """
    GitHub Webhook model for storing webhook configurations
    """

    __tablename__ = "github_webhooks"

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey("user.id"), nullable=False)
    project_id = db.Column(db.Integer, db.<PERSON>ey("project.id"), nullable=True)
    github_webhook_id = db.Column(db.String(255), nullable=False)  # GitHub webhook ID
    github_repo_full_name = db.Column(db.String(255), nullable=False)  # owner/repo
    webhook_url = db.Column(db.String(500), nullable=False)  # Our webhook endpoint URL
    secret = db.Column(db.String(255), nullable=False)  # Webhook secret for validation
    events = db.Column(db.JSON, nullable=False)  # List of events to listen for
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(
        db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )
    last_ping = db.Column(db.DateTime, nullable=True)  # Last ping from GitHub

    # Relationships
    user = db.relationship("User", backref="github_webhooks")
    project = db.relationship("Project", backref="github_webhooks")

    def to_dict(self):
        """Convert webhook object to dictionary"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "project_id": self.project_id,
            "github_webhook_id": self.github_webhook_id,
            "github_repo_full_name": self.github_repo_full_name,
            "webhook_url": self.webhook_url,
            "events": self.events,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_ping": self.last_ping.isoformat() if self.last_ping else None,
        }


class JiraWebhook(db.Model):
    """
    Jira Webhook model for storing webhook configurations
    """

    __tablename__ = "jira_webhooks"

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    project_id = db.Column(db.Integer, db.ForeignKey("project.id"), nullable=True)
    jira_webhook_id = db.Column(db.String(255), nullable=False)  # Jira webhook ID
    project_key = db.Column(db.String(50), nullable=False)  # Jira project key
    webhook_url = db.Column(db.String(500), nullable=False)  # Our webhook endpoint URL
    events = db.Column(db.JSON, nullable=False)  # List of events to listen for
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(
        db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )
    last_ping = db.Column(db.DateTime, nullable=True)  # Last ping from Jira

    # Relationships
    user = db.relationship("User", backref="jira_webhooks")
    project = db.relationship("Project", backref="jira_webhooks")

    def to_dict(self):
        """Convert webhook object to dictionary"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "project_id": self.project_id,
            "jira_webhook_id": self.jira_webhook_id,
            "project_key": self.project_key,
            "webhook_url": self.webhook_url,
            "events": self.events,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_ping": self.last_ping.isoformat() if self.last_ping else None,
        }

    def __repr__(self):
        return f"<JiraWebhook {self.project_key}>"


class WebhookEvent(db.Model):
    """
    Model for storing webhook event logs
    """

    __tablename__ = "webhook_events"

    id = db.Column(db.Integer, primary_key=True)
    github_webhook_id = db.Column(
        db.Integer, db.ForeignKey("github_webhooks.id"), nullable=True
    )
    jira_webhook_id = db.Column(
        db.Integer, db.ForeignKey("jira_webhooks.id"), nullable=True
    )
    delivery_id = db.Column(db.String(255), nullable=False)
    event_type = db.Column(db.String(50), nullable=False)
    action = db.Column(db.String(50), nullable=True)
    payload = db.Column(db.JSON, nullable=False)
    processed = db.Column(db.Boolean, default=False)
    processed_at = db.Column(db.DateTime, nullable=True)
    error_message = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    github_webhook = db.relationship("GitHubWebhook", backref="webhook_events")
    jira_webhook = db.relationship("JiraWebhook", backref="webhook_events")

    def to_dict(self):
        """Convert webhook event object to dictionary"""
        return {
            "id": self.id,
            "github_webhook_id": self.github_webhook_id,
            "jira_webhook_id": self.jira_webhook_id,
            "delivery_id": self.delivery_id,
            "event_type": self.event_type,
            "action": self.action,
            "processed": self.processed,
            "processed_at": (
                self.processed_at.isoformat() if self.processed_at else None
            ),
            "error_message": self.error_message,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }

    def __repr__(self):
        return f"<WebhookEvent {self.event_type}:{self.action}>"
