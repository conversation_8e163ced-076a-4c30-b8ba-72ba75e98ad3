from typing import List, TypeVar, Optional, Any
from app.helpers.pagination import Pagination, PaginatedResponse

T = TypeVar('T')

class ApiResponse:
    def __init__(self, is_success: bool, message: str = "", data=None, code: int = 200):
        self.is_success = is_success
        self.message = message
        self.data = data
        self.code = code

    def to_dict(self):
        return {
            "success": self.is_success,
            "message": self.message,
            "data": self.data,
            "code": self.code,
        }

    @classmethod
    def success(cls, message: str = "Success", data=None, code: int = 200):
        return cls(is_success=True, message=message, data=data, code=code)

    @classmethod
    def failure(cls, message: str = "Failure", data=None, code: int = 400):
        return cls(is_success=False, message=message, data=data, code=code)
        
    @classmethod
    def paginated(cls, items: List[Any], page: int, per_page: int, total_items: int,
                  message: str = "Fetched successfully", code: int = 200):
        """
        Create a successful paginated response
        
        Args:
            items: List of items for the current page
            page: Current page number (1-indexed)
            per_page: Number of items per page
            total_items: Total number of items across all pages
            message: Response message
            code: HTTP status code
            
        Returns:
            ApiResponse with standardized pagination format
        """
        pagination = Pagination(page, per_page, total_items)
        paginated_data = PaginatedResponse(items, pagination).to_dict()
        return cls(is_success=True, message=message, data=paginated_data, code=code)
