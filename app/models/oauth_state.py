"""
OAuth State model for storing temporary OAuth state data
"""

from app.helpers.extensions import db
from datetime import datetime, timedelta


class OAuthState(db.Model):
    """
    Model for storing OAuth state data temporarily
    """
    __tablename__ = 'oauth_states'
    
    id = db.Column(db.Integer, primary_key=True)
    state = db.Column(db.String(255), unique=True, nullable=False)
    user_id = db.Column(db.Integer, db.Foreign<PERSON>ey("user.id"), nullable=True)  # For linking flow
    flow_type = db.Column(db.String(50), nullable=False)  # 'login' or 'link'
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    expires_at = db.Column(db.DateTime, default=lambda: datetime.utcnow() + timedelta(minutes=10))
    
    # Relationships
    user = db.relationship('User', backref='oauth_states')
    
    def is_expired(self):
        """Check if the state has expired"""
        return datetime.utcnow() > self.expires_at
    
    def to_dict(self):
        """Convert state object to dictionary"""
        return {
            "id": self.id,
            "state": self.state,
            "user_id": self.user_id,
            "flow_type": self.flow_type,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "is_expired": self.is_expired()
        }
    
    def __repr__(self):
        return f"<OAuthState {self.state} - {self.flow_type}>"
    
    @classmethod
    def cleanup_expired(cls):
        """Remove expired states"""
        expired_states = cls.query.filter(cls.expires_at < datetime.utcnow()).all()
        for state in expired_states:
            db.session.delete(state)
        db.session.commit()
        return len(expired_states)
