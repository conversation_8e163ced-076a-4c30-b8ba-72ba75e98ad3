"""
Telegram integration related models for Task Management System
"""

from app.helpers.extensions import db
from datetime import datetime


class TelegramIntegration(db.Model):
    """
    Telegram bot integration model for users
    """
    __tablename__ = 'telegram_integrations'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    telegram_user_id = db.Column(db.String(50), unique=True, nullable=False)  # Telegram user ID
    telegram_username = db.Column(db.String(100), nullable=True)  # Telegram username
    telegram_first_name = db.Column(db.String(100), nullable=True)
    telegram_last_name = db.Column(db.String(100), nullable=True)
    chat_id = db.Column(db.String(50), nullable=False)  # Telegram chat ID
    is_active = db.Column(db.<PERSON>, default=True)
    language_code = db.Column(db.String(10), default='en')  # User's language preference
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_interaction = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    user = db.relationship("User", backref=db.backref("telegram_integration", uselist=False, lazy=True))
    subscriptions = db.relationship("TelegramSubscription", backref="telegram_integration", lazy=True, cascade="all, delete-orphan")

    def to_dict(self):
        """Convert telegram integration object to dictionary"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "telegram_user_id": self.telegram_user_id,
            "telegram_username": self.telegram_username,
            "telegram_first_name": self.telegram_first_name,
            "telegram_last_name": self.telegram_last_name,
            "chat_id": self.chat_id,
            "is_active": self.is_active,
            "language_code": self.language_code,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_interaction": self.last_interaction.isoformat() if self.last_interaction else None,
        }

    def __repr__(self):
        return f"<TelegramIntegration user_id={self.user_id} telegram_user_id={self.telegram_user_id}>"


class TelegramSubscription(db.Model):
    """
    User subscription preferences for different types of notifications
    """
    __tablename__ = 'telegram_subscriptions'
    
    id = db.Column(db.Integer, primary_key=True)
    telegram_integration_id = db.Column(db.Integer, db.ForeignKey("telegram_integrations.id"), nullable=False)
    notification_type = db.Column(db.String(50), nullable=False)  # 'github_notifications', 'task_updates', etc.
    is_enabled = db.Column(db.Boolean, default=True)
    settings = db.Column(db.JSON, nullable=True)  # Additional settings for specific notification types
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Unique constraint to prevent duplicate subscriptions
    __table_args__ = (
        db.UniqueConstraint('telegram_integration_id', 'notification_type', name='unique_subscription'),
    )

    def to_dict(self):
        """Convert telegram subscription object to dictionary"""
        return {
            "id": self.id,
            "telegram_integration_id": self.telegram_integration_id,
            "notification_type": self.notification_type,
            "is_enabled": self.is_enabled,
            "settings": self.settings,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    def __repr__(self):
        return f"<TelegramSubscription integration_id={self.telegram_integration_id} type={self.notification_type}>"


class TelegramNotificationLog(db.Model):
    """
    Log of sent Telegram notifications for tracking and debugging
    """
    __tablename__ = 'telegram_notification_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    telegram_integration_id = db.Column(db.Integer, db.ForeignKey("telegram_integrations.id"), nullable=False)
    notification_type = db.Column(db.String(50), nullable=False)
    message_content = db.Column(db.Text, nullable=False)
    telegram_message_id = db.Column(db.String(50), nullable=True)  # Telegram's message ID if sent successfully
    status = db.Column(db.String(20), default='pending')  # 'pending', 'sent', 'failed'
    error_message = db.Column(db.Text, nullable=True)
    webhook_event_id = db.Column(db.Integer, db.ForeignKey("webhook_events.id"), nullable=True)  # Link to webhook event if applicable
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    sent_at = db.Column(db.DateTime, nullable=True)

    # Relationships
    telegram_integration = db.relationship("TelegramIntegration", backref="notification_logs")
    webhook_event = db.relationship("WebhookEvent", backref="telegram_notifications")

    def to_dict(self):
        """Convert telegram notification log object to dictionary"""
        return {
            "id": self.id,
            "telegram_integration_id": self.telegram_integration_id,
            "notification_type": self.notification_type,
            "message_content": self.message_content,
            "telegram_message_id": self.telegram_message_id,
            "status": self.status,
            "error_message": self.error_message,
            "webhook_event_id": self.webhook_event_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "sent_at": self.sent_at.isoformat() if self.sent_at else None,
        }

    def __repr__(self):
        return f"<TelegramNotificationLog id={self.id} status={self.status}>"
