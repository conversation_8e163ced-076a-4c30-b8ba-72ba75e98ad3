"""
Telegram Bot Service for handling bot interactions and commands
"""

import requests
import json
from typing import Dict, List, Optional, <PERSON><PERSON>
from flask import current_app
from app.helpers.extensions import db
from app.models.user import User
from app.models.telegram_integration import TelegramIntegration, TelegramSubscription
from app.models.api_response import ApiResponse


class TelegramBotService:
    """Service for handling Telegram bot operations using direct HTTP API calls"""

    def __init__(self):
        self.bot_token = current_app.config.get("TELEGRAM_BOT_TOKEN")
        if not self.bot_token:
            raise ValueError("TELEGRAM_BOT_TOKEN not configured")

        self.base_url = f"https://api.telegram.org/bot{self.bot_token}"

    def send_message(
        self, chat_id: str, text: str, parse_mode: str = "HTML"
    ) -> Tuple[bool, Optional[Dict]]:
        """
        Send a message to a Telegram chat using HTTP API

        Args:
            chat_id (str): Telegram chat ID
            text (str): Message text
            parse_mode (str): Message parse mode (HTML, Markdown, etc.)

        Returns:
            Tuple[bool, Optional[Dict]]: (success, response_data)
        """
        try:
            url = f"{self.base_url}/sendMessage"
            payload = {"chat_id": chat_id, "text": text, "parse_mode": parse_mode}

            response = requests.post(url, json=payload, timeout=10)
            response_data = response.json()

            if response.status_code == 200 and response_data.get("ok"):
                return True, response_data
            else:
                current_app.logger.error(
                    f"Failed to send Telegram message: {response_data}"
                )
                return False, response_data

        except Exception as e:
            current_app.logger.error(f"Error sending Telegram message: {str(e)}")
            return False, None

    def process_webhook_update(self, update_data: Dict) -> ApiResponse:
        """
        Process incoming webhook update from Telegram

        Args:
            update_data (Dict): Telegram update data

        Returns:
            ApiResponse: Processing result
        """
        try:
            message = update_data.get("message")
            if not message:
                return ApiResponse.success("No message in update")

            chat = message.get("chat", {})
            user = message.get("from", {})
            text = message.get("text", "")

            chat_id = str(chat.get("id"))
            telegram_user_id = str(user.get("id"))

            # Handle different commands
            if text.startswith("/start"):
                return self._handle_start_command(chat_id, telegram_user_id, user)
            elif text.startswith("/link"):
                return self._handle_link_command(chat_id, telegram_user_id, user, text)
            elif text.startswith("/unlink"):
                return self._handle_unlink_command(chat_id, telegram_user_id)
            elif text.startswith("/subscribe"):
                return self._handle_subscribe_command(chat_id, telegram_user_id, text)
            elif text.startswith("/unsubscribe"):
                return self._handle_unsubscribe_command(chat_id, telegram_user_id, text)
            elif text.startswith("/status"):
                return self._handle_status_command(chat_id, telegram_user_id)
            elif text.startswith("/help"):
                return self._handle_help_command(chat_id)
            else:
                return self._handle_unknown_command(chat_id)

        except Exception as e:
            current_app.logger.error(f"Error processing Telegram update: {str(e)}")
            return ApiResponse.failure(f"Error processing update: {str(e)}")

    def _handle_start_command(
        self, chat_id: str, telegram_user_id: str, user_data: Dict
    ) -> ApiResponse:
        """Handle /start command"""
        try:
            # Check if user is already registered
            telegram_integration = TelegramIntegration.query.filter_by(
                telegram_user_id=telegram_user_id
            ).first()

            if telegram_integration:
                welcome_text = (
                    f"Welcome back, {user_data.get('first_name', 'User')}! 👋\n\n"
                )
                welcome_text += f"✅ <b>Account Status:</b> Linked\n"
                welcome_text += (
                    f"👤 <b>TMS User:</b> {telegram_integration.user.username}\n"
                )
                welcome_text += (
                    f"📧 <b>Email:</b> {telegram_integration.user.email}\n\n"
                )
                welcome_text += "Use /help to see available commands."
            else:
                welcome_text = f"Hello {user_data.get('first_name', 'User')}! 👋\n\n"
                welcome_text += "Welcome to TMS Notification Bot!\n\n"
                welcome_text += "🔗 <b>To get started, link your TMS account:</b>\n"
                welcome_text += (
                    "<code>/link <EMAIL> your_password</code>\n\n"
                )
                welcome_text += "📋 <b>After linking, you can:</b>\n"
                welcome_text += "• Subscribe to GitHub notifications\n"
                welcome_text += "• Get real-time updates about your projects\n"
                welcome_text += "• Manage your notification preferences\n\n"
                welcome_text += "Use /help to see all available commands."

            success, _ = self.send_message(chat_id, welcome_text)

            if success:
                return ApiResponse.success("Start command processed")
            else:
                return ApiResponse.failure("Failed to send welcome message")

        except Exception as e:
            return ApiResponse.failure(f"Error handling start command: {str(e)}")

    def _handle_link_command(
        self, chat_id: str, telegram_user_id: str, user_data: Dict, text: str
    ) -> ApiResponse:
        """Handle /link command"""
        try:
            # Parse command: /link <email> <password>
            parts = text.split()
            if len(parts) < 3:
                error_text = "❌ Invalid format!\n\n"
                error_text += "<b>Usage:</b> /link <email> <password>\n\n"
                error_text += "<b>Example:</b>\n"
                error_text += "<code>/link <EMAIL> mypassword</code>\n\n"
                error_text += (
                    "This will link your Telegram account with your TMS account."
                )
                self.send_message(chat_id, error_text)
                return ApiResponse.failure("Invalid link command format")

            email = parts[1]
            password = parts[2]

            # Check if already linked
            telegram_integration = TelegramIntegration.query.filter_by(
                telegram_user_id=telegram_user_id
            ).first()

            if telegram_integration:
                error_text = "✅ Your Telegram account is already linked to TMS!\n\n"
                error_text += (
                    f"👤 <b>Linked to:</b> {telegram_integration.user.username}\n"
                )
                error_text += f"📧 <b>Email:</b> {telegram_integration.user.email}\n\n"
                error_text += "Use /unlink to unlink first, then /link again with different credentials."
                self.send_message(chat_id, error_text)
                return ApiResponse.success("Already linked")

            # Authenticate user with TMS
            auth_result = self._authenticate_user(email, password)
            if not auth_result["success"]:
                error_text = f"❌ <b>Authentication failed!</b>\n\n"
                error_text += f"Error: {auth_result['message']}\n\n"
                error_text += "Please check your email and password and try again."
                self.send_message(chat_id, error_text)
                return ApiResponse.failure("Authentication failed")

            user = auth_result["user"]

            # Check if this user already has Telegram linked
            existing_integration = TelegramIntegration.query.filter_by(
                user_id=user.id
            ).first()

            if existing_integration:
                error_text = f"❌ <b>Account already linked!</b>\n\n"
                error_text += f"Your TMS account is already linked to another Telegram account.\n\n"
                error_text += f"Please unlink the existing connection first."
                self.send_message(chat_id, error_text)
                return ApiResponse.failure("User already has Telegram linked")

            # Create new integration
            telegram_integration = TelegramIntegration(
                user_id=user.id,
                telegram_user_id=str(telegram_user_id),
                chat_id=str(chat_id),
                telegram_username=user_data.get("username"),
                telegram_first_name=user_data.get("first_name"),
                telegram_last_name=user_data.get("last_name"),
                language_code=user_data.get("language_code", "en"),
                is_active=True,
            )

            db.session.add(telegram_integration)
            db.session.commit()

            # Send success message
            success_text = f"🎉 <b>Account Linked Successfully!</b>\n\n"
            success_text += f"👤 <b>TMS User:</b> {user.username}\n"
            success_text += f"📧 <b>Email:</b> {user.email}\n\n"
            success_text += f"✅ Your Telegram account is now linked to TMS!\n\n"
            success_text += f"<b>Next steps:</b>\n"
            success_text += (
                f"• Use /subscribe github_notifications to get GitHub notifications\n"
            )
            success_text += f"• Use /status to check your subscription status\n"
            success_text += f"• Use /help to see all available commands"

            self.send_message(chat_id, success_text)
            return ApiResponse.success("Account linked successfully")

        except Exception as e:
            error_text = "❌ Error linking account. Please try again."
            self.send_message(chat_id, error_text)
            return ApiResponse.failure(f"Error handling link command: {str(e)}")

    def _authenticate_user(self, email: str, password: str) -> Dict:
        """
        Authenticate user with email and password

        Args:
            email (str): User email
            password (str): User password

        Returns:
            Dict: Authentication result
        """
        try:
            from werkzeug.security import check_password_hash

            # Find user by email
            user = User.query.filter_by(email=email, deleted_at=None).first()
            if not user:
                return {"success": False, "message": "User not found", "user": None}

            # Check password
            if not user.password_hash:
                return {
                    "success": False,
                    "message": "Account uses OAuth authentication. Please use web interface to link.",
                    "user": None,
                }

            if not check_password_hash(user.password_hash, password):
                return {"success": False, "message": "Invalid password", "user": None}

            return {
                "success": True,
                "message": "Authentication successful",
                "user": user,
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"Authentication error: {str(e)}",
                "user": None,
            }

    def _handle_unlink_command(
        self, chat_id: str, telegram_user_id: str
    ) -> ApiResponse:
        """Handle /unlink command"""
        try:
            # Find telegram integration
            telegram_integration = TelegramIntegration.query.filter_by(
                telegram_user_id=telegram_user_id
            ).first()

            if not telegram_integration:
                error_text = "❌ <b>No linked account found!</b>\n\n"
                error_text += (
                    "Your Telegram account is not linked to any TMS account.\n\n"
                )
                error_text += "Use /link <email> <password> to link your account."
                self.send_message(chat_id, error_text)
                return ApiResponse.failure("No linked account")

            # Get user info before deleting
            user = telegram_integration.user

            # Delete the integration (this will cascade delete subscriptions and logs)
            db.session.delete(telegram_integration)
            db.session.commit()

            # Send success message
            success_text = f"👋 <b>Account Unlinked Successfully!</b>\n\n"
            success_text += f"Your Telegram account has been unlinked from:\n"
            success_text += f"👤 <b>User:</b> {user.username}\n"
            success_text += f"📧 <b>Email:</b> {user.email}\n\n"
            success_text += f"❌ You will no longer receive notifications.\n\n"
            success_text += f"Use /link <email> <password> to link again."

            self.send_message(chat_id, success_text)
            return ApiResponse.success("Account unlinked successfully")

        except Exception as e:
            error_text = "❌ Error unlinking account. Please try again."
            self.send_message(chat_id, error_text)
            return ApiResponse.failure(f"Error handling unlink command: {str(e)}")

    def _handle_help_command(self, chat_id: str) -> ApiResponse:
        """Handle /help command"""
        help_text = """
🤖 <b>TMS Notification Bot - Help</b>

<b>Account Management:</b>
/start - Start the bot and get welcome message
/link &lt;email&gt; &lt;password&gt; - Link your TMS account
/unlink - Unlink your TMS account
/status - Check your account and subscription status

<b>Notifications:</b>
/subscribe github_notifications - Subscribe to GitHub notifications
/unsubscribe github_notifications - Unsubscribe from GitHub notifications

<b>Other Commands:</b>
/help - Show this help message

<b>Examples:</b>
• <code>/link <EMAIL> mypassword</code>
• <code>/subscribe github_notifications</code>
• <code>/unsubscribe github_notifications</code>

<b>Notification Types:</b>
• <code>github_notifications</code> - Get notified about GitHub webhook events (push, issues, PRs, etc.)

<b>Need Help?</b>
Visit your TMS dashboard for more integration options and settings.
        """

        success, _ = self.send_message(chat_id, help_text.strip())

        if success:
            return ApiResponse.success("Help command processed")
        else:
            return ApiResponse.failure("Failed to send help message")

    def _handle_subscribe_command(
        self, chat_id: str, telegram_user_id: str, text: str
    ) -> ApiResponse:
        """Handle /subscribe command"""
        try:
            parts = text.split()
            if len(parts) < 2:
                error_text = "❌ Please specify notification type.\nExample: /subscribe github_notifications"
                self.send_message(chat_id, error_text)
                return ApiResponse.failure("Invalid subscribe command format")

            notification_type = parts[1]
            valid_types = ["github_notifications"]

            if notification_type not in valid_types:
                error_text = f"❌ Invalid notification type: {notification_type}\n"
                error_text += f"Valid types: {', '.join(valid_types)}"
                self.send_message(chat_id, error_text)
                return ApiResponse.failure("Invalid notification type")

            # Find telegram integration
            telegram_integration = TelegramIntegration.query.filter_by(
                telegram_user_id=telegram_user_id
            ).first()

            if not telegram_integration:
                error_text = "❌ Your Telegram account is not linked to TMS.\n"
                error_text += (
                    "Please visit your TMS dashboard to link your account first."
                )
                self.send_message(chat_id, error_text)
                return ApiResponse.failure("Telegram account not linked")

            # Check if subscription already exists
            subscription = TelegramSubscription.query.filter_by(
                telegram_integration_id=telegram_integration.id,
                notification_type=notification_type,
            ).first()

            if subscription:
                if subscription.is_enabled:
                    success_text = (
                        f"✅ You're already subscribed to {notification_type}"
                    )
                else:
                    subscription.is_enabled = True
                    db.session.commit()
                    success_text = (
                        f"✅ Successfully re-enabled {notification_type} notifications"
                    )
            else:
                # Create new subscription
                subscription = TelegramSubscription(
                    telegram_integration_id=telegram_integration.id,
                    notification_type=notification_type,
                    is_enabled=True,
                )
                db.session.add(subscription)
                db.session.commit()
                success_text = (
                    f"✅ Successfully subscribed to {notification_type} notifications"
                )

            self.send_message(chat_id, success_text)
            return ApiResponse.success("Subscription updated")

        except Exception as e:
            error_text = "❌ Error processing subscription. Please try again."
            self.send_message(chat_id, error_text)
            return ApiResponse.failure(f"Error handling subscribe command: {str(e)}")

    def _handle_unsubscribe_command(
        self, chat_id: str, telegram_user_id: str, text: str
    ) -> ApiResponse:
        """Handle /unsubscribe command"""
        try:
            parts = text.split()
            if len(parts) < 2:
                error_text = "❌ Please specify notification type.\nExample: /unsubscribe github_notifications"
                self.send_message(chat_id, error_text)
                return ApiResponse.failure("Invalid unsubscribe command format")

            notification_type = parts[1]

            # Find telegram integration
            telegram_integration = TelegramIntegration.query.filter_by(
                telegram_user_id=telegram_user_id
            ).first()

            if not telegram_integration:
                error_text = "❌ Your Telegram account is not linked to TMS."
                self.send_message(chat_id, error_text)
                return ApiResponse.failure("Telegram account not linked")

            # Find subscription
            subscription = TelegramSubscription.query.filter_by(
                telegram_integration_id=telegram_integration.id,
                notification_type=notification_type,
            ).first()

            if not subscription or not subscription.is_enabled:
                error_text = (
                    f"❌ You're not subscribed to {notification_type} notifications"
                )
            else:
                subscription.is_enabled = False
                db.session.commit()
                error_text = f"✅ Successfully unsubscribed from {notification_type} notifications"

            self.send_message(chat_id, error_text)
            return ApiResponse.success("Unsubscription processed")

        except Exception as e:
            error_text = "❌ Error processing unsubscription. Please try again."
            self.send_message(chat_id, error_text)
            return ApiResponse.failure(f"Error handling unsubscribe command: {str(e)}")

    def _handle_status_command(
        self, chat_id: str, telegram_user_id: str
    ) -> ApiResponse:
        """Handle /status command"""
        try:
            telegram_integration = TelegramIntegration.query.filter_by(
                telegram_user_id=telegram_user_id
            ).first()

            if not telegram_integration:
                status_text = "❌ <b>Account Status:</b> Not Linked\n\n"
                status_text += "Your Telegram account is not linked to TMS.\n\n"
                status_text += "🔗 <b>To link your account:</b>\n"
                status_text += "<code>/link <EMAIL> your_password</code>"
                self.send_message(chat_id, status_text)
                return ApiResponse.success("Status command processed")

            # Get active subscriptions
            subscriptions = TelegramSubscription.query.filter_by(
                telegram_integration_id=telegram_integration.id, is_enabled=True
            ).all()

            # Get all subscriptions (including disabled)
            all_subscriptions = TelegramSubscription.query.filter_by(
                telegram_integration_id=telegram_integration.id
            ).all()

            status_text = f"📊 <b>Your Account Status</b>\n\n"
            status_text += f"✅ <b>Account:</b> Linked to TMS\n"
            status_text += f"👤 <b>User:</b> {telegram_integration.user.username}\n"
            status_text += f"📧 <b>Email:</b> {telegram_integration.user.email}\n"
            status_text += f"🕒 <b>Linked:</b> {telegram_integration.created_at.strftime('%Y-%m-%d %H:%M')}\n\n"

            if subscriptions:
                status_text += (
                    f"✅ <b>Active Subscriptions ({len(subscriptions)}):</b>\n"
                )
                for sub in subscriptions:
                    status_text += f"• {sub.notification_type}\n"
            else:
                status_text += f"❌ <b>No active subscriptions</b>\n"

            if all_subscriptions and len(all_subscriptions) > len(subscriptions):
                disabled_count = len(all_subscriptions) - len(subscriptions)
                status_text += f"\n🔕 <b>Disabled subscriptions:</b> {disabled_count}\n"

            status_text += f"\n<b>Commands:</b>\n"
            status_text += f"• /subscribe github_notifications - Enable notifications\n"
            status_text += (
                f"• /unsubscribe github_notifications - Disable notifications\n"
            )
            status_text += f"• /unlink - Unlink account"

            self.send_message(chat_id, status_text)
            return ApiResponse.success("Status command processed")

        except Exception as e:
            error_text = "❌ Error getting status. Please try again."
            self.send_message(chat_id, error_text)
            return ApiResponse.failure(f"Error handling status command: {str(e)}")

    def _handle_unknown_command(self, chat_id: str) -> ApiResponse:
        """Handle unknown commands"""
        error_text = "❓ Unknown command. Use /help to see available commands."
        success, _ = self.send_message(chat_id, error_text)

        if success:
            return ApiResponse.success("Unknown command handled")
        else:
            return ApiResponse.failure("Failed to send unknown command message")
