"""
Telegram Notification Service for sending GitHub webhook notifications
"""

from typing import Dict, List, Optional
from datetime import datetime
from flask import current_app
from app.helpers.extensions import db
from app.models.telegram_integration import TelegramIntegration, TelegramSubscription, TelegramNotificationLog
from app.models.webhook import WebhookEvent
from app.telegram.bot_service import TelegramBotService


class TelegramNotificationService:
    """Service for sending Telegram notifications based on GitHub webhook events"""
    
    def __init__(self):
        self.bot_service = TelegramBotService()
    
    def send_github_webhook_notification(self, webhook_event: WebhookEvent) -> bool:
        """
        Send Telegram notifications for GitHub webhook events
        
        Args:
            webhook_event (WebhookEvent): The webhook event to notify about
            
        Returns:
            bool: True if at least one notification was sent successfully
        """
        try:
            # Get all users subscribed to GitHub notifications for this webhook's user
            webhook_user_id = webhook_event.webhook.user_id
            
            subscriptions = db.session.query(TelegramSubscription).join(
                TelegramIntegration
            ).filter(
                TelegramIntegration.user_id == webhook_user_id,
                TelegramIntegration.is_active == True,
                TelegramSubscription.notification_type == 'github_notifications',
                TelegramSubscription.is_enabled == True
            ).all()
            
            if not subscriptions:
                current_app.logger.info(f"No Telegram subscriptions found for webhook event {webhook_event.id}")
                return False
            
            # Format notification message
            message = self._format_github_notification(webhook_event)
            if not message:
                current_app.logger.warning(f"Could not format notification for webhook event {webhook_event.id}")
                return False
            
            success_count = 0
            
            # Send notification to each subscribed user
            for subscription in subscriptions:
                telegram_integration = subscription.telegram_integration
                
                # Create notification log entry
                notification_log = TelegramNotificationLog(
                    telegram_integration_id=telegram_integration.id,
                    notification_type='github_notifications',
                    message_content=message,
                    webhook_event_id=webhook_event.id,
                    status='pending'
                )
                db.session.add(notification_log)
                db.session.flush()  # Get the ID
                
                try:
                    # Send the message
                    success, response_data = self.bot_service.send_message(
                        telegram_integration.chat_id,
                        message
                    )
                    
                    if success:
                        # Update notification log
                        notification_log.status = 'sent'
                        notification_log.sent_at = datetime.utcnow()
                        if response_data and response_data.get('result'):
                            notification_log.telegram_message_id = str(response_data['result'].get('message_id'))
                        
                        # Update last interaction
                        telegram_integration.last_interaction = datetime.utcnow()
                        
                        success_count += 1
                        current_app.logger.info(f"Sent Telegram notification to user {telegram_integration.user_id}")
                    else:
                        # Update notification log with error
                        notification_log.status = 'failed'
                        error_msg = response_data.get('description') if response_data else 'Unknown error'
                        notification_log.error_message = error_msg
                        
                        current_app.logger.error(f"Failed to send Telegram notification to user {telegram_integration.user_id}: {error_msg}")
                
                except Exception as e:
                    notification_log.status = 'failed'
                    notification_log.error_message = str(e)
                    current_app.logger.error(f"Exception sending Telegram notification: {str(e)}")
            
            db.session.commit()
            return success_count > 0
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error in send_github_webhook_notification: {str(e)}")
            return False
    
    def _format_github_notification(self, webhook_event: WebhookEvent) -> Optional[str]:
        """
        Format GitHub webhook event into a Telegram message
        
        Args:
            webhook_event (WebhookEvent): The webhook event
            
        Returns:
            Optional[str]: Formatted message or None if event type not supported
        """
        try:
            event_type = webhook_event.event_type
            action = webhook_event.action
            payload = webhook_event.payload
            
            repo_name = payload.get('repository', {}).get('full_name', 'Unknown Repository')
            repo_url = payload.get('repository', {}).get('html_url', '')
            
            if event_type == 'push':
                return self._format_push_notification(payload, repo_name, repo_url)
            elif event_type == 'issues':
                return self._format_issues_notification(payload, action, repo_name, repo_url)
            elif event_type == 'issue_comment':
                return self._format_issue_comment_notification(payload, action, repo_name, repo_url)
            elif event_type == 'pull_request':
                return self._format_pull_request_notification(payload, action, repo_name, repo_url)
            elif event_type == 'pull_request_review':
                return self._format_pull_request_review_notification(payload, action, repo_name, repo_url)
            elif event_type == 'repository':
                return self._format_repository_notification(payload, action, repo_name, repo_url)
            else:
                # Generic notification for unsupported event types
                return f"🔔 <b>GitHub Event</b>\n\n📁 <b>Repository:</b> <a href='{repo_url}'>{repo_name}</a>\n🎯 <b>Event:</b> {event_type}"
                
        except Exception as e:
            current_app.logger.error(f"Error formatting GitHub notification: {str(e)}")
            return None
    
    def _format_push_notification(self, payload: Dict, repo_name: str, repo_url: str) -> str:
        """Format push event notification"""
        pusher = payload.get('pusher', {}).get('name', 'Unknown')
        ref = payload.get('ref', '')
        branch = ref.split('/')[-1] if ref.startswith('refs/heads/') else ref
        commits = payload.get('commits', [])
        commit_count = len(commits)
        
        message = f"🚀 <b>New Push</b>\n\n"
        message += f"📁 <b>Repository:</b> <a href='{repo_url}'>{repo_name}</a>\n"
        message += f"🌿 <b>Branch:</b> {branch}\n"
        message += f"👤 <b>Pusher:</b> {pusher}\n"
        message += f"📝 <b>Commits:</b> {commit_count}\n"
        
        if commits:
            message += f"\n<b>Latest commit:</b>\n"
            latest_commit = commits[-1]
            commit_message = latest_commit.get('message', '').split('\n')[0][:100]
            commit_url = latest_commit.get('url', '')
            message += f"• <a href='{commit_url}'>{commit_message}</a>"
        
        return message
    
    def _format_issues_notification(self, payload: Dict, action: str, repo_name: str, repo_url: str) -> str:
        """Format issues event notification"""
        issue = payload.get('issue', {})
        issue_number = issue.get('number', 'Unknown')
        issue_title = issue.get('title', 'Unknown Issue')[:100]
        issue_url = issue.get('html_url', '')
        user = issue.get('user', {}).get('login', 'Unknown')
        
        action_emoji = {
            'opened': '🆕',
            'closed': '✅',
            'reopened': '🔄',
            'assigned': '👤',
            'unassigned': '👤',
            'labeled': '🏷️',
            'unlabeled': '🏷️'
        }.get(action, '📝')
        
        message = f"{action_emoji} <b>Issue {action.title()}</b>\n\n"
        message += f"📁 <b>Repository:</b> <a href='{repo_url}'>{repo_name}</a>\n"
        message += f"🎫 <b>Issue:</b> <a href='{issue_url}'>#{issue_number} {issue_title}</a>\n"
        message += f"👤 <b>User:</b> {user}"
        
        return message

    def _format_issue_comment_notification(self, payload: Dict, action: str, repo_name: str, repo_url: str) -> str:
        """Format issue comment event notification"""
        issue = payload.get('issue', {})
        comment = payload.get('comment', {})
        issue_number = issue.get('number', 'Unknown')
        issue_title = issue.get('title', 'Unknown Issue')[:50]
        issue_url = issue.get('html_url', '')
        comment_user = comment.get('user', {}).get('login', 'Unknown')
        comment_body = comment.get('body', '')[:100]

        message = f"💬 <b>New Comment</b>\n\n"
        message += f"📁 <b>Repository:</b> <a href='{repo_url}'>{repo_name}</a>\n"
        message += f"🎫 <b>Issue:</b> <a href='{issue_url}'>#{issue_number} {issue_title}</a>\n"
        message += f"👤 <b>Commenter:</b> {comment_user}\n"
        message += f"💭 <b>Comment:</b> {comment_body}..."

        return message

    def _format_pull_request_notification(self, payload: Dict, action: str, repo_name: str, repo_url: str) -> str:
        """Format pull request event notification"""
        pr = payload.get('pull_request', {})
        pr_number = pr.get('number', 'Unknown')
        pr_title = pr.get('title', 'Unknown PR')[:100]
        pr_url = pr.get('html_url', '')
        user = pr.get('user', {}).get('login', 'Unknown')

        action_emoji = {
            'opened': '🆕',
            'closed': '✅',
            'reopened': '🔄',
            'merged': '🔀',
            'ready_for_review': '👀',
            'review_requested': '👀'
        }.get(action, '📝')

        message = f"{action_emoji} <b>Pull Request {action.title()}</b>\n\n"
        message += f"📁 <b>Repository:</b> <a href='{repo_url}'>{repo_name}</a>\n"
        message += f"🔀 <b>PR:</b> <a href='{pr_url}'>#{pr_number} {pr_title}</a>\n"
        message += f"👤 <b>User:</b> {user}"

        if action == 'merged':
            merged_by = payload.get('pull_request', {}).get('merged_by', {}).get('login', 'Unknown')
            message += f"\n🎉 <b>Merged by:</b> {merged_by}"

        return message

    def _format_pull_request_review_notification(self, payload: Dict, action: str, repo_name: str, repo_url: str) -> str:
        """Format pull request review event notification"""
        pr = payload.get('pull_request', {})
        review = payload.get('review', {})
        pr_number = pr.get('number', 'Unknown')
        pr_title = pr.get('title', 'Unknown PR')[:50]
        pr_url = pr.get('html_url', '')
        reviewer = review.get('user', {}).get('login', 'Unknown')
        review_state = review.get('state', 'commented').lower()

        state_emoji = {
            'approved': '✅',
            'changes_requested': '❌',
            'commented': '💬'
        }.get(review_state, '📝')

        message = f"{state_emoji} <b>PR Review {review_state.title()}</b>\n\n"
        message += f"📁 <b>Repository:</b> <a href='{repo_url}'>{repo_name}</a>\n"
        message += f"🔀 <b>PR:</b> <a href='{pr_url}'>#{pr_number} {pr_title}</a>\n"
        message += f"👤 <b>Reviewer:</b> {reviewer}"

        return message

    def _format_repository_notification(self, payload: Dict, action: str, repo_name: str, repo_url: str) -> str:
        """Format repository event notification"""
        action_emoji = {
            'created': '🆕',
            'deleted': '🗑️',
            'archived': '📦',
            'unarchived': '📦',
            'publicized': '🌍',
            'privatized': '🔒',
            'renamed': '✏️',
            'transferred': '🔄'
        }.get(action, '📝')

        message = f"{action_emoji} <b>Repository {action.title()}</b>\n\n"
        message += f"📁 <b>Repository:</b> <a href='{repo_url}'>{repo_name}</a>"

        if action == 'renamed':
            old_name = payload.get('changes', {}).get('repository', {}).get('name', {}).get('from', 'Unknown')
            message += f"\n📝 <b>Old name:</b> {old_name}"

        return message
