"""
Telegram bot routes for webhook and API endpoints
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.models.api_response import ApiResponse
from app.models.user import User
from app.models.telegram_integration import TelegramIntegration, TelegramSubscription
from app.telegram.bot_service import TelegramBotService
from app.helpers.extensions import db
from datetime import datetime
import secrets
import requests

telegram_bp = Blueprint("telegram_bp", __name__, url_prefix="/telegram")


@telegram_bp.route("/webhook", methods=["POST"])
def telegram_webhook():
    """
    Telegram webhook endpoint - receives updates from Telegram
    This endpoint does not require JWT authentication as it's called by Telegram
    """
    try:
        current_app.logger.info("Telegram webhook called")

        # Verify webhook token if configured
        webhook_token = current_app.config.get('TELEGRAM_WEBHOOK_TOKEN')
        if webhook_token:
            provided_token = request.headers.get('X-Telegram-Bot-Api-Secret-Token')
            if provided_token != webhook_token:
                current_app.logger.warning("Invalid webhook token provided")
                return jsonify({"error": "Invalid webhook token"}), 401

        # Get update data
        update_data = request.get_json()
        if not update_data:
            current_app.logger.error("No JSON payload in webhook")
            return jsonify({"error": "No JSON payload"}), 400

        current_app.logger.info(f"Processing Telegram update: {update_data.get('update_id')}")

        # Process the update
        bot_service = TelegramBotService()
        result = bot_service.process_webhook_update(update_data)

        if result.success:
            current_app.logger.info(f"Telegram update processed successfully: {result.message}")
            return jsonify({"ok": True}), 200
        else:
            current_app.logger.error(f"Error processing Telegram update: {result.message}")
            return jsonify({"ok": True}), 200  # Always return 200 to Telegram

    except Exception as e:
        current_app.logger.error(f"Error in Telegram webhook: {str(e)}")
        return jsonify({"ok": True}), 200  # Always return 200 to Telegram


@telegram_bp.route("/link", methods=["POST"])
@jwt_required()
def link_telegram_account():
    """
    Link user's Telegram account to TMS account
    Expects: {"telegram_user_id": "*********", "chat_id": "*********", "telegram_data": {...}}
    """
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        telegram_user_id = data.get('telegram_user_id')
        chat_id = data.get('chat_id')
        telegram_data = data.get('telegram_data', {})
        
        if not telegram_user_id or not chat_id:
            response = ApiResponse.failure("telegram_user_id and chat_id are required", code=400)
            return jsonify(response.to_dict()), response.code
        
        # Check if this Telegram account is already linked to another user
        existing_integration = TelegramIntegration.query.filter_by(
            telegram_user_id=str(telegram_user_id)
        ).first()
        
        if existing_integration and existing_integration.user_id != current_user_id:
            response = ApiResponse.failure("This Telegram account is already linked to another user", code=400)
            return jsonify(response.to_dict()), response.code
        
        # Check if current user already has a Telegram integration
        user_integration = TelegramIntegration.query.filter_by(
            user_id=current_user_id
        ).first()
        
        if user_integration:
            # Update existing integration
            user_integration.telegram_user_id = str(telegram_user_id)
            user_integration.chat_id = str(chat_id)
            user_integration.telegram_username = telegram_data.get('username')
            user_integration.telegram_first_name = telegram_data.get('first_name')
            user_integration.telegram_last_name = telegram_data.get('last_name')
            user_integration.language_code = telegram_data.get('language_code', 'en')
            user_integration.is_active = True
            user_integration.last_interaction = datetime.utcnow()
            user_integration.updated_at = datetime.utcnow()
        else:
            # Create new integration
            user_integration = TelegramIntegration(
                user_id=current_user_id,
                telegram_user_id=str(telegram_user_id),
                chat_id=str(chat_id),
                telegram_username=telegram_data.get('username'),
                telegram_first_name=telegram_data.get('first_name'),
                telegram_last_name=telegram_data.get('last_name'),
                language_code=telegram_data.get('language_code', 'en'),
                is_active=True
            )
            db.session.add(user_integration)
        
        db.session.commit()
        
        # Send welcome message to user
        try:
            bot_service = TelegramBotService()
            welcome_message = f"🎉 <b>Account Linked Successfully!</b>\n\n"
            welcome_message += f"Your Telegram account has been linked to your TMS account.\n"
            welcome_message += f"Use /help to see available commands and manage your notifications."
            
            bot_service.send_message(str(chat_id), welcome_message)
        except Exception as e:
            current_app.logger.warning(f"Failed to send welcome message: {str(e)}")
        
        response = ApiResponse.success(
            "Telegram account linked successfully",
            data=user_integration.to_dict()
        )
        return jsonify(response.to_dict()), response.code
        
    except Exception as e:
        db.session.rollback()
        response = ApiResponse.failure(f"Error linking Telegram account: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@telegram_bp.route("/unlink", methods=["POST"])
@jwt_required()
def unlink_telegram_account():
    """
    Unlink user's Telegram account from TMS account
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Find user's Telegram integration
        user_integration = TelegramIntegration.query.filter_by(
            user_id=current_user_id
        ).first()
        
        if not user_integration:
            response = ApiResponse.failure("No Telegram account linked", code=404)
            return jsonify(response.to_dict()), response.code
        
        # Send goodbye message
        try:
            bot_service = TelegramBotService()
            goodbye_message = f"👋 <b>Account Unlinked</b>\n\n"
            goodbye_message += f"Your Telegram account has been unlinked from TMS.\n"
            goodbye_message += f"You will no longer receive notifications."
            
            bot_service.send_message(user_integration.chat_id, goodbye_message)
        except Exception as e:
            current_app.logger.warning(f"Failed to send goodbye message: {str(e)}")
        
        # Delete the integration (this will cascade delete subscriptions and logs)
        db.session.delete(user_integration)
        db.session.commit()
        
        response = ApiResponse.success("Telegram account unlinked successfully")
        return jsonify(response.to_dict()), response.code
        
    except Exception as e:
        db.session.rollback()
        response = ApiResponse.failure(f"Error unlinking Telegram account: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@telegram_bp.route("/status", methods=["GET"])
@jwt_required()
def get_telegram_status():
    """
    Get user's Telegram integration status and subscriptions
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Find user's Telegram integration
        user_integration = TelegramIntegration.query.filter_by(
            user_id=current_user_id
        ).first()
        
        if not user_integration:
            response = ApiResponse.success(
                "No Telegram account linked",
                data={"linked": False, "integration": None, "subscriptions": []}
            )
            return jsonify(response.to_dict()), response.code
        
        # Get subscriptions
        subscriptions = TelegramSubscription.query.filter_by(
            telegram_integration_id=user_integration.id
        ).all()
        
        response_data = {
            "linked": True,
            "integration": user_integration.to_dict(),
            "subscriptions": [sub.to_dict() for sub in subscriptions]
        }
        
        response = ApiResponse.success("Telegram status retrieved", data=response_data)
        return jsonify(response.to_dict()), response.code
        
    except Exception as e:
        response = ApiResponse.failure(f"Error getting Telegram status: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@telegram_bp.route("/subscribe", methods=["POST"])
@jwt_required()
def manage_subscription():
    """
    Subscribe/unsubscribe to notification types
    Expects: {"notification_type": "github_notifications", "enabled": true}
    """
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        notification_type = data.get('notification_type')
        enabled = data.get('enabled', True)
        
        if not notification_type:
            response = ApiResponse.failure("notification_type is required", code=400)
            return jsonify(response.to_dict()), response.code
        
        # Validate notification type
        valid_types = ["github_notifications"]
        if notification_type not in valid_types:
            response = ApiResponse.failure(f"Invalid notification type. Valid types: {valid_types}", code=400)
            return jsonify(response.to_dict()), response.code
        
        # Find user's Telegram integration
        user_integration = TelegramIntegration.query.filter_by(
            user_id=current_user_id
        ).first()
        
        if not user_integration:
            response = ApiResponse.failure("No Telegram account linked. Please link your account first.", code=400)
            return jsonify(response.to_dict()), response.code
        
        # Find or create subscription
        subscription = TelegramSubscription.query.filter_by(
            telegram_integration_id=user_integration.id,
            notification_type=notification_type
        ).first()
        
        if subscription:
            subscription.is_enabled = enabled
            subscription.updated_at = datetime.utcnow()
        else:
            subscription = TelegramSubscription(
                telegram_integration_id=user_integration.id,
                notification_type=notification_type,
                is_enabled=enabled
            )
            db.session.add(subscription)
        
        db.session.commit()
        
        action = "subscribed to" if enabled else "unsubscribed from"
        response = ApiResponse.success(
            f"Successfully {action} {notification_type}",
            data=subscription.to_dict()
        )
        return jsonify(response.to_dict()), response.code
        
    except Exception as e:
        db.session.rollback()
        response = ApiResponse.failure(f"Error managing subscription: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@telegram_bp.route("/webhook/setup", methods=["POST"])
@jwt_required()
def setup_telegram_webhook():
    """
    Setup Telegram webhook (Admin only)
    Expects: {"webhook_url": "https://domain.com/telegram/webhook", "secret_token": "optional"}
    """
    try:
        current_user_id = get_jwt_identity()

        # Check if user is admin (you might want to implement proper role checking)
        user = User.query.get(current_user_id)
        if not user:
            response = ApiResponse.failure("User not found", code=404)
            return jsonify(response.to_dict()), response.code

        # For now, allow any authenticated user. In production, add admin role check
        # if not user.is_admin:
        #     response = ApiResponse.failure("Admin access required", code=403)
        #     return jsonify(response.to_dict()), response.code

        data = request.get_json()
        webhook_url = data.get('webhook_url')
        secret_token = data.get('secret_token')

        if not webhook_url:
            response = ApiResponse.failure("webhook_url is required", code=400)
            return jsonify(response.to_dict()), response.code

        # Validate webhook URL
        if not webhook_url.startswith('https://'):
            response = ApiResponse.failure("Webhook URL must use HTTPS", code=400)
            return jsonify(response.to_dict()), response.code

        # Generate secret token if not provided
        if not secret_token:
            secret_token = secrets.token_urlsafe(32)

        # Get bot token
        bot_token = current_app.config.get('TELEGRAM_BOT_TOKEN')
        if not bot_token:
            response = ApiResponse.failure("TELEGRAM_BOT_TOKEN not configured", code=500)
            return jsonify(response.to_dict()), response.code

        # Setup webhook with Telegram
        telegram_url = f"https://api.telegram.org/bot{bot_token}/setWebhook"
        payload = {
            "url": webhook_url,
            "secret_token": secret_token,
            "allowed_updates": ["message", "callback_query"],
            "drop_pending_updates": True,
            "max_connections": 40
        }

        response_data = requests.post(telegram_url, json=payload, timeout=10)
        telegram_result = response_data.json()

        if telegram_result.get("ok"):
            response_data = {
                "webhook_url": webhook_url,
                "secret_token": secret_token,
                "telegram_response": telegram_result,
                "setup_time": datetime.utcnow().isoformat()
            }

            response = ApiResponse.success(
                "Telegram webhook setup successfully",
                data=response_data
            )
            return jsonify(response.to_dict()), response.code
        else:
            error_msg = telegram_result.get("description", "Unknown error")
            response = ApiResponse.failure(f"Failed to setup webhook: {error_msg}", code=400)
            return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(f"Error setting up webhook: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@telegram_bp.route("/webhook/info", methods=["GET"])
@jwt_required()
def get_telegram_webhook_info():
    """
    Get current Telegram webhook information (Admin only)
    """
    try:
        current_user_id = get_jwt_identity()

        # Check if user exists
        user = User.query.get(current_user_id)
        if not user:
            response = ApiResponse.failure("User not found", code=404)
            return jsonify(response.to_dict()), response.code

        # Get bot token
        bot_token = current_app.config.get('TELEGRAM_BOT_TOKEN')
        if not bot_token:
            response = ApiResponse.failure("TELEGRAM_BOT_TOKEN not configured", code=500)
            return jsonify(response.to_dict()), response.code

        # Get webhook info from Telegram
        telegram_url = f"https://api.telegram.org/bot{bot_token}/getWebhookInfo"
        response_data = requests.get(telegram_url, timeout=10)
        telegram_result = response_data.json()

        if telegram_result.get("ok"):
            webhook_info = telegram_result.get("result", {})

            # Format response
            formatted_info = {
                "url": webhook_info.get("url", ""),
                "has_custom_certificate": webhook_info.get("has_custom_certificate", False),
                "pending_update_count": webhook_info.get("pending_update_count", 0),
                "last_error_date": webhook_info.get("last_error_date"),
                "last_error_message": webhook_info.get("last_error_message"),
                "max_connections": webhook_info.get("max_connections", 40),
                "allowed_updates": webhook_info.get("allowed_updates", []),
                "ip_address": webhook_info.get("ip_address"),
                "last_synchronization_error_date": webhook_info.get("last_synchronization_error_date")
            }

            response = ApiResponse.success(
                "Webhook information retrieved",
                data=formatted_info
            )
            return jsonify(response.to_dict()), response.code
        else:
            error_msg = telegram_result.get("description", "Unknown error")
            response = ApiResponse.failure(f"Failed to get webhook info: {error_msg}", code=400)
            return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(f"Error getting webhook info: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@telegram_bp.route("/webhook/delete", methods=["POST"])
@jwt_required()
def delete_telegram_webhook():
    """
    Delete Telegram webhook (Admin only)
    """
    try:
        current_user_id = get_jwt_identity()

        # Check if user exists
        user = User.query.get(current_user_id)
        if not user:
            response = ApiResponse.failure("User not found", code=404)
            return jsonify(response.to_dict()), response.code

        # Get bot token
        bot_token = current_app.config.get('TELEGRAM_BOT_TOKEN')
        if not bot_token:
            response = ApiResponse.failure("TELEGRAM_BOT_TOKEN not configured", code=500)
            return jsonify(response.to_dict()), response.code

        # Delete webhook from Telegram
        telegram_url = f"https://api.telegram.org/bot{bot_token}/deleteWebhook"
        payload = {"drop_pending_updates": True}

        response_data = requests.post(telegram_url, json=payload, timeout=10)
        telegram_result = response_data.json()

        if telegram_result.get("ok"):
            response = ApiResponse.success(
                "Telegram webhook deleted successfully",
                data={"deleted_at": datetime.utcnow().isoformat()}
            )
            return jsonify(response.to_dict()), response.code
        else:
            error_msg = telegram_result.get("description", "Unknown error")
            response = ApiResponse.failure(f"Failed to delete webhook: {error_msg}", code=400)
            return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(f"Error deleting webhook: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@telegram_bp.route("/bot/info", methods=["GET"])
@jwt_required()
def get_telegram_bot_info():
    """
    Get Telegram bot information (Admin only)
    """
    try:
        current_user_id = get_jwt_identity()

        # Check if user exists
        user = User.query.get(current_user_id)
        if not user:
            response = ApiResponse.failure("User not found", code=404)
            return jsonify(response.to_dict()), response.code

        # Get bot token
        bot_token = current_app.config.get('TELEGRAM_BOT_TOKEN')
        if not bot_token:
            response = ApiResponse.failure("TELEGRAM_BOT_TOKEN not configured", code=500)
            return jsonify(response.to_dict()), response.code

        # Get bot info from Telegram
        telegram_url = f"https://api.telegram.org/bot{bot_token}/getMe"
        response_data = requests.get(telegram_url, timeout=10)
        telegram_result = response_data.json()

        if telegram_result.get("ok"):
            bot_info = telegram_result.get("result", {})

            # Format response
            formatted_info = {
                "id": bot_info.get("id"),
                "is_bot": bot_info.get("is_bot"),
                "first_name": bot_info.get("first_name"),
                "username": bot_info.get("username"),
                "can_join_groups": bot_info.get("can_join_groups"),
                "can_read_all_group_messages": bot_info.get("can_read_all_group_messages"),
                "supports_inline_queries": bot_info.get("supports_inline_queries"),
                "can_connect_to_business": bot_info.get("can_connect_to_business"),
                "has_main_web_app": bot_info.get("has_main_web_app")
            }

            response = ApiResponse.success(
                "Bot information retrieved",
                data=formatted_info
            )
            return jsonify(response.to_dict()), response.code
        else:
            error_msg = telegram_result.get("description", "Unknown error")
            response = ApiResponse.failure(f"Failed to get bot info: {error_msg}", code=400)
            return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(f"Error getting bot info: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code
