import secrets
from flask import (
    request,
    jsonify,
    Blueprint,
    redirect,
    session,
)
from flask_jwt_extended import (
    create_access_token,
    get_jwt,
    jwt_required,
    get_jwt_identity,
)
from werkzeug.security import check_password_hash
from app.auth.services import (
    create_user,
    get_user_by_email,
    get_user_by_id,
    get_user_with_roles,
    create_user_token,
    jwt_blocklist,
    forgot_password,
    reset_password,
    change_password,
    update_profile,
    reset_session,
)
from app.auth.github_oauth import GitHubOAuthService
from app.webhooks.jira.oauth import JiraOAuthService
from app.auth.github_services import (
    create_or_update_github_user,
    link_github_account,
    unlink_github_account,
)
from app.models.oauth_state import OAuthState
from app.helpers.extensions import db
from app.auth.jira_services import (
    create_or_update_jira_user,
    link_jira_account,
    unlink_jira_account,
)
from app.models.api_response import ApiResponse
from app.history.logger import activity_logger

auth_bp = Blueprint("auth", __name__, url_prefix="/auth")


# GET METHODS
@auth_bp.route("/me", methods=["GET"])
@jwt_required()
def get_current_user():
    user_id = get_jwt_identity()
    user_data = get_user_with_roles(int(user_id))

    if not user_data:
        response = ApiResponse.failure("User not found", code=404)
        return jsonify(response.to_dict()), response.code

    response = ApiResponse.success(data=user_data)
    return jsonify(response.to_dict()), response.code


# POST METHODS
@auth_bp.route("/register", methods=["POST"])
def register():
    data = request.get_json()

    # Validate required fields according to openapi.yaml
    if not data:
        response = ApiResponse.failure("Request body is required", code=400)
        return jsonify(response.to_dict()), response.code

    required_fields = ["username", "email", "password"]
    for field in required_fields:
        if not data.get(field):
            response = ApiResponse.failure(
                f"{field.capitalize()} is required", code=400
            )
            return jsonify(response.to_dict()), response.code

    response = create_user(data)
    return jsonify(response.to_dict()), response.code


@auth_bp.route("/login", methods=["POST"])
def login():
    try:
        print("🔍 Login endpoint called")
        data = request.get_json()
        print(f"📥 Request data: {data}")

        # Validate required fields
        if not data:
            print("❌ No request body")
            response = ApiResponse.failure("Request body is required", code=400)
            return jsonify(response.to_dict()), response.code

        if not data.get("email") or not data.get("password"):
            print("❌ Missing email or password")
            response = ApiResponse.failure("Email and password are required", code=400)
            return jsonify(response.to_dict()), response.code

        print(f"🔍 Looking for user with email: {data.get('email')}")
        user = get_user_by_email(data.get("email"))

        if user:
            print(f"✅ User found: {user.username}")

            # Check if user is a GitHub OAuth user without password
            if not user.password_hash and user.auth_provider == "github":
                print("❌ GitHub OAuth user attempting password login")
                response = ApiResponse.failure(
                    "This account uses GitHub authentication. Please use GitHub login.",
                    code=400,
                )
                return jsonify(response.to_dict()), response.code

            # Check password for local users
            if user.password_hash and check_password_hash(
                user.password_hash, data.get("password")
            ):
                print("✅ Password correct")
                access_token = create_user_token(user.id)
                user_data = get_user_with_roles(user.id)

                # Log successful login activity
                activity_logger.log(
                    user_id=user.id,
                    action="login",
                    entity_type="user",
                    entity_id=user.id,
                    description=f"User {user.username} logged in",
                    details={"login_method": "password"}
                )

                # Return format matching LoginResponse schema
                response = ApiResponse.success(
                    "Login successful",
                    data={
                        "access_token": access_token,
                        "user": user_data
                    }
                )
                print(f"📤 Success response: {response.to_dict()}")
                return jsonify(response.to_dict()), response.code
            else:
                print("❌ Password incorrect")

                # Log failed login attempt if user exists
                activity_logger.log(
                    user_id=user.id,
                    action="login_failed",
                    entity_type="user",
                    entity_id=user.id,
                    description=f"Failed login attempt for user {user.username}",
                    details={"reason": "incorrect_password"}
                )
        else:
            print("❌ User not found")

        # Return format matching ErrorResponse schema
        response = ApiResponse.failure("Invalid credentials", code=401)
        print(f"📤 Error response: {response.to_dict()}")
        return jsonify(response.to_dict()), response.code

    except Exception as e:
        print(f"💥 Login error: {str(e)}")
        response = ApiResponse.failure(f"Internal server error: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@auth_bp.route("/logout", methods=["POST"])
@jwt_required()
def logout():
    # Get current user ID from JWT
    user_id = get_jwt_identity()

    # Invalidate the token
    jti = get_jwt()["jti"]
    exp = get_jwt()["exp"]
    jwt_blocklist[jti] = exp

    # Log the logout activity
    user = get_user_by_id(int(user_id))
    if user:
        activity_logger.log(
            user_id=user.id,
            action="logout",
            entity_type="user",
            entity_id=user.id,
            description=f"User {user.username} logged out",
            details={}
        )

    # Return format matching SuccessResponse schema
    response = ApiResponse.success("Logged out successfully")
    return jsonify(response.to_dict()), response.code


@auth_bp.route("/forgot-password", methods=["POST"])
def forgot_password_route():
    """Request password reset"""
    try:
        data = request.get_json()

        if not data:
            response = ApiResponse.failure("Request body is required", code=400)
            return jsonify(response.to_dict()), response.code

        email = data.get("email")
        if not email:
            response = ApiResponse.failure("Email is required", code=400)
            return jsonify(response.to_dict()), response.code

        response = forgot_password(email)
        return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(f"Internal server error: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code

@auth_bp.route("/reset-password", methods=["POST"])
def reset_password_route():
    """Reset password using token"""
    try:
        data = request.get_json()

        if not data:
            response = ApiResponse.failure("Request body is required", code=400)
            return jsonify(response.to_dict()), response.code

        token = data.get("token")
        new_password = data.get("new_password")

        if not token or not new_password:
            response = ApiResponse.failure("Token and new password are required", code=400)
            return jsonify(response.to_dict()), response.code

        response = reset_password(token, new_password)
        return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(f"Internal server error: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code

@auth_bp.route("/change-password", methods=["POST"])
@jwt_required()
def change_password_route():
    """Change password for authenticated user"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        if not data:
            response = ApiResponse.failure("Request body is required", code=400)
            return jsonify(response.to_dict()), response.code

        current_password = data.get("current_password")
        new_password = data.get("new_password")

        if not current_password or not new_password:
            response = ApiResponse.failure(
                "Current password and new password are required",
                code=400
            )
            return jsonify(response.to_dict()), response.code

        response = change_password(int(user_id), current_password, new_password)
        return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(f"Internal server error: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code

@auth_bp.route("/update-profile", methods=["PUT"])
@jwt_required()
def update_profile_route():
    """Update user profile information"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        if not data:
            response = ApiResponse.failure("Request body is required", code=400)
            return jsonify(response.to_dict()), response.code

        response = update_profile(int(user_id), data)
        return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(f"Internal server error: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code

@auth_bp.route("/reset-session", methods=["POST"])
@jwt_required()
def reset_session_route():
    """Reset all sessions for the authenticated user"""
    try:
        user_id = get_jwt_identity()

        response = reset_session(int(user_id))
        return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(f"Internal server error: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code

# GITHUB OAUTH METHODS
@auth_bp.route("/github/url", methods=["GET"])
def github_auth_url():
    """Get GitHub OAuth authorization URL (for Swagger UI testing)"""
    try:
        # Generate state for CSRF protection
        state = secrets.token_urlsafe(32)
        session["github_oauth_state"] = state

        # Get authorization URL
        auth_url = GitHubOAuthService.get_authorization_url(state=state)

        response = ApiResponse.success(
            "GitHub authorization URL generated",
            data={
                "authorization_url": auth_url,
                "instructions": [
                    "Copy the authorization_url above",
                    "Open it in a new browser tab",
                    "Authorize the application on GitHub",
                    "You will be redirected back with a JWT token",
                ],
            },
        )
        return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(
            f"Error generating GitHub OAuth URL: {str(e)}", code=500
        )
        return jsonify(response.to_dict()), response.code


@auth_bp.route("/github", methods=["GET"])
def github_login():
    """Initiate GitHub OAuth login"""
    try:
        # Generate state for CSRF protection
        state = secrets.token_urlsafe(32)
        session["github_oauth_state"] = state

        # Get authorization URL
        auth_url = GitHubOAuthService.get_authorization_url(state=state)

        return redirect(auth_url)

    except Exception as e:
        response = ApiResponse.failure(
            f"Error initiating GitHub OAuth: {str(e)}", code=500
        )
        return jsonify(response.to_dict()), response.code


@auth_bp.route("/github/callback", methods=["GET"])
def github_callback():
    """Handle GitHub OAuth callback"""
    try:
        # Get parameters from callback
        code = request.args.get("code")
        state = request.args.get("state")
        error = request.args.get("error")

        if error:
            response = ApiResponse.failure(f"GitHub OAuth error: {error}", code=400)
            return jsonify(response.to_dict()), response.code

        if not code:
            response = ApiResponse.failure("Authorization code not provided", code=400)
            return jsonify(response.to_dict()), response.code

        # Check if this is a linking flow or login flow based on state format
        print(f"DEBUG: Received state: {state}")

        is_linking_flow = False
        link_user_id = None

        if state and state.startswith("link_"):
            # This is a linking flow: link_<user_id>_<random>
            try:
                parts = state.split("_")
                if len(parts) >= 3:
                    link_user_id = int(parts[1])
                    is_linking_flow = True
                    print(f"DEBUG: Detected linking flow for user_id: {link_user_id}")
            except (ValueError, IndexError):
                response = ApiResponse.failure(
                    f"Invalid state format for linking: {state}",
                    code=400
                )
                return jsonify(response.to_dict()), response.code
        else:
            # This is a login flow - verify against session
            stored_oauth_state = session.get("github_oauth_state")
            print(f"DEBUG: Detected login flow, stored state: {stored_oauth_state}")

            if not stored_oauth_state or stored_oauth_state != state:
                response = ApiResponse.failure(
                    f"Invalid state parameter for login. Received: {state}, Expected: {stored_oauth_state}",
                    code=400
                )
                return jsonify(response.to_dict()), response.code

            # Clear login state from session
            session.pop("github_oauth_state", None)

        # Exchange code for token
        token_response = GitHubOAuthService.exchange_code_for_token(code, state)
        if not token_response.is_success:
            return jsonify(token_response.to_dict()), token_response.code

        access_token = token_response.data.get("access_token")
        if not access_token:
            response = ApiResponse.failure(
                "No access token received from GitHub", code=400
            )
            return jsonify(response.to_dict()), response.code

        # Get user information from GitHub
        user_info_response = GitHubOAuthService.get_user_info(access_token)
        if not user_info_response.is_success:
            return jsonify(user_info_response.to_dict()), user_info_response.code

        # Handle linking flow vs login flow
        if is_linking_flow:
            # Link GitHub account to existing user
            link_response = link_github_account(
                link_user_id, user_info_response.data, access_token
            )

            if link_response.is_success:
                response = ApiResponse.success(
                    "GitHub account linked successfully",
                    data={
                        "user": link_response.data,
                        "github_info": user_info_response.data,
                        "message": "Your GitHub account has been successfully linked to your TMS account"
                    }
                )
            else:
                response = link_response

            return jsonify(response.to_dict()), response.code
        else:
            # Normal login/registration flow
            auth_response = create_or_update_github_user(
                user_info_response.data, access_token
            )
            return jsonify(auth_response.to_dict()), auth_response.code

    except Exception as e:
        response = ApiResponse.failure(
            f"Error processing GitHub callback: {str(e)}", code=500
        )
        return jsonify(response.to_dict()), response.code


@auth_bp.route("/github/link", methods=["POST"])
@jwt_required()
def link_github():
    """Link GitHub account to current user (manual token)"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        if not data or not data.get("access_token"):
            response = ApiResponse.failure("GitHub access token is required", code=400)
            return jsonify(response.to_dict()), response.code

        access_token = data.get("access_token")

        # Get user information from GitHub
        user_info_response = GitHubOAuthService.get_user_info(access_token)
        if not user_info_response.is_success:
            return jsonify(user_info_response.to_dict()), user_info_response.code

        # Link GitHub account
        link_response = link_github_account(
            int(user_id), user_info_response.data, access_token
        )

        return jsonify(link_response.to_dict()), link_response.code

    except Exception as e:
        response = ApiResponse.failure(
            f"Error linking GitHub account: {str(e)}", code=500
        )
        return jsonify(response.to_dict()), response.code


@auth_bp.route("/github/link-oauth", methods=["GET"])
@jwt_required()
def link_github_oauth():
    """Initiate GitHub OAuth flow for linking account to current user"""
    try:
        user_id = get_jwt_identity()

        # Create state with encoded information: link_<user_id>_<random>
        random_part = secrets.token_urlsafe(16)
        state = f"link_{user_id}_{random_part}"

        print(f"DEBUG: Generated state for linking: {state}")
        print(f"DEBUG: User ID: {user_id}")

        # Get authorization URL with link-specific scope
        auth_url = GitHubOAuthService.get_authorization_url(state=state)

        response = ApiResponse.success(
            "GitHub OAuth authorization URL generated for account linking",
            data={
                "authorization_url": auth_url,
                "instructions": [
                    "Copy the authorization_url above",
                    "Open it in a new browser tab",
                    "Authorize the application on GitHub",
                    "You will be redirected back and your GitHub account will be linked",
                ],
            },
        )
        return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(
            f"Error generating GitHub OAuth URL: {str(e)}", code=500
        )
        return jsonify(response.to_dict()), response.code


@auth_bp.route("/github/unlink", methods=["POST"])
@jwt_required()
def unlink_github():
    """Unlink GitHub account from current user"""
    try:
        user_id = get_jwt_identity()

        # Unlink GitHub account
        unlink_response = unlink_github_account(int(user_id))

        return jsonify(unlink_response.to_dict()), unlink_response.code

    except Exception as e:
        response = ApiResponse.failure(
            f"Error unlinking GitHub account: {str(e)}", code=500
        )
        return jsonify(response.to_dict()), response.code


# JIRA OAUTH METHODS
@auth_bp.route("/jira/url", methods=["GET"])
def jira_auth_url():
    """Get Jira OAuth authorization URL (for Swagger UI testing)"""
    try:
        # Generate state for CSRF protection
        state = secrets.token_urlsafe(32)

        # Save state to database instead of session
        try:
            oauth_state = OAuthState(
                state=state,
                flow_type='login'
            )
            db.session.add(oauth_state)
            db.session.commit()
            print(f"DEBUG: Saved OAuth state to database: {state}")
        except Exception as e:
            print(f"ERROR: Failed to save OAuth state: {e}")
            db.session.rollback()
            # Fallback to session for now
            session["jira_oauth_state"] = state
            print(f"DEBUG: Fallback to session storage: {state}")

        # Get authorization URL
        oauth_service = JiraOAuthService()
        auth_url = oauth_service.get_authorization_url(state=state)

        response = ApiResponse.success(
            "Jira authorization URL generated",
            data={
                "authorization_url": auth_url,
                "instructions": [
                    "Copy the authorization_url above",
                    "Open it in a new browser tab",
                    "Authorize the application on Jira",
                    "You will be redirected back with a JWT token",
                ],
            },
        )
        return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(
            f"Error generating Jira OAuth URL: {str(e)}", code=500
        )
        return jsonify(response.to_dict()), response.code


@auth_bp.route("/jira", methods=["GET"])
def jira_login():
    """Initiate Jira OAuth login"""
    try:
        # Generate state for CSRF protection
        state = secrets.token_urlsafe(32)

        # Save state to database instead of session
        oauth_state = OAuthState(
            state=state,
            flow_type='login'
        )
        db.session.add(oauth_state)
        db.session.commit()

        # Get authorization URL
        oauth_service = JiraOAuthService()
        auth_url = oauth_service.get_authorization_url(state=state)

        return redirect(auth_url)

    except Exception as e:
        response = ApiResponse.failure(
            f"Error initiating Jira OAuth: {str(e)}", code=500
        )
        return jsonify(response.to_dict()), response.code


@auth_bp.route("/jira/callback", methods=["GET"])
def jira_callback():
    """Handle Jira OAuth callback"""
    try:
        # Get parameters from callback
        code = request.args.get("code")
        state = request.args.get("state")
        error = request.args.get("error")

        if error:
            response = ApiResponse.failure(f"Jira OAuth error: {error}", code=400)
            return jsonify(response.to_dict()), response.code

        if not code:
            response = ApiResponse.failure("Authorization code not provided", code=400)
            return jsonify(response.to_dict()), response.code

        # Verify state parameter for CSRF protection
        # Try database first, then fallback to session
        oauth_state = OAuthState.query.filter_by(state=state).first()
        if oauth_state:
            # Database validation
            if oauth_state.is_expired():
                db.session.delete(oauth_state)
                db.session.commit()
                response = ApiResponse.failure("OAuth state expired", code=400)
                return jsonify(response.to_dict()), response.code

            # Clear state from database
            db.session.delete(oauth_state)
            db.session.commit()
            print(f"DEBUG: Validated state from database: {state}")
        else:
            # Fallback to session validation
            stored_state = session.get("jira_oauth_state")
            if not stored_state or stored_state != state:
                response = ApiResponse.failure("Invalid state parameter", code=400)
                return jsonify(response.to_dict()), response.code

            # Clear state from session
            session.pop("jira_oauth_state", None)
            print(f"DEBUG: Validated state from session: {state}")

        # Exchange code for token
        oauth_service = JiraOAuthService()
        token_data = oauth_service.exchange_code_for_token(code)

        access_token = token_data.get("access_token")
        if not access_token:
            response = ApiResponse.failure("No access token received from Jira", code=400)
            return jsonify(response.to_dict()), response.code

        # Get accessible resources (Jira sites)
        resources = oauth_service.get_accessible_resources(access_token)
        print(f"DEBUG: Jira resources response: {resources}")
        print(f"DEBUG: Resources type: {type(resources)}")

        if not resources:
            response = ApiResponse.failure("No accessible Jira sites found", code=400)
            return jsonify(response.to_dict()), response.code

        # For now, use the first available site
        jira_site = resources[0]

        # Safely extract cloud_id
        if isinstance(jira_site, dict):
            cloud_id = jira_site.get("id")
            if not cloud_id:
                response = ApiResponse.failure("Jira site missing ID field", code=400)
                return jsonify(response.to_dict()), response.code
        else:
            response = ApiResponse.failure(f"Invalid Jira site format: {type(jira_site)}", code=400)
            return jsonify(response.to_dict()), response.code

        # Get user information from Jira
        from app.webhooks.jira.api_client import JiraAPIClient
        jira_client = JiraAPIClient(access_token, cloud_id)
        user_info = jira_client.get_current_user()
        print(f"DEBUG: Jira user info: {user_info}")

        # Create or update user
        print(f"DEBUG: Creating/updating Jira user with cloud_id: {cloud_id}")
        auth_response = create_or_update_jira_user(user_info, access_token, cloud_id)
        print(f"DEBUG: Auth response: {auth_response.success}, {auth_response.message}")

        if auth_response.success:
            # Get JWT token from response
            jwt_token = auth_response.data.get('access_token') if auth_response.data else None

            if jwt_token:
                # Redirect to dashboard with success message and token
                return redirect(f"/dashboard/?jira_auth=success&token={jwt_token}&message=Jira authentication successful")
            else:
                # Redirect with success but no token
                return redirect(f"/dashboard/?jira_auth=success&message=Jira authentication successful")
        else:
            # Redirect to dashboard with error message
            return redirect(f"/dashboard/?jira_auth=error&message={auth_response.message}")

        # Fallback JSON response for API clients
        return jsonify(auth_response.to_dict()), auth_response.code

    except Exception as e:
        response = ApiResponse.failure(
            f"Error processing Jira callback: {str(e)}", code=500
        )
        return jsonify(response.to_dict()), response.code


@auth_bp.route("/jira/link", methods=["POST"])
@jwt_required()
def link_jira():
    """Link Jira account to current user"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        if not data or not data.get("access_token") or not data.get("cloud_id"):
            response = ApiResponse.failure("Jira access token and cloud ID are required", code=400)
            return jsonify(response.to_dict()), response.code

        access_token = data.get("access_token")
        cloud_id = data.get("cloud_id")

        # Link Jira account
        link_response = link_jira_account(int(user_id), access_token, cloud_id)

        return jsonify(link_response.to_dict()), link_response.code

    except Exception as e:
        response = ApiResponse.failure(
            f"Error linking Jira account: {str(e)}", code=500
        )
        return jsonify(response.to_dict()), response.code


@auth_bp.route("/jira/unlink", methods=["POST"])
@jwt_required()
def unlink_jira():
    """Unlink Jira account from current user"""
    try:
        user_id = get_jwt_identity()

        # Unlink Jira account
        unlink_response = unlink_jira_account(int(user_id))

        return jsonify(unlink_response.to_dict()), unlink_response.code

    except Exception as e:
        response = ApiResponse.failure(
            f"Error unlinking Jira account: {str(e)}", code=500
        )
        return jsonify(response.to_dict()), response.code
