"""
Jira authentication services for TMS Backend
Similar to GitHub services but for Jira integration
"""

from datetime import datetime
from flask_jwt_extended import create_access_token

from app.models import User, Integration
from app.models.api_response import ApiResponse
from app.helpers.extensions import db
from app.integrations.jira.oauth import JiraOAuthService
from app.integrations.jira.api_client import JiraAPIClient


def create_or_update_jira_user(jira_user_data, access_token, cloud_id):
    """
    Create or update user from Jira OAuth data
    
    Args:
        jira_user_data (dict): User data from Jira API
        access_token (str): Jira access token
        cloud_id (str): Atlassian cloud ID
        
    Returns:
        ApiResponse: Success or failure response
    """
    try:
        # Extract user info from Jira data
        jira_account_id = jira_user_data.get("account_id")
        email = jira_user_data.get("emailAddress")
        display_name = jira_user_data.get("displayName", "")
        avatar_url = jira_user_data.get("avatarUrls", {}).get("48x48", "")
        
        if not jira_account_id or not email:
            return ApiResponse.failure("Invalid Jira user data", code=400)
        
        # Check if user exists by email
        user = User.query.filter_by(email=email).first()
        
        if user:
            # Update existing user with Jira info
            user.jira_account_id = jira_account_id
            user.jira_display_name = display_name
            user.jira_avatar_url = avatar_url
            user.updated_at = datetime.utcnow()
        else:
            # Create new user
            # Generate username from display name or email
            username = display_name.lower().replace(" ", "_") if display_name else email.split("@")[0]
            
            # Ensure username is unique
            base_username = username
            counter = 1
            while User.query.filter_by(username=username).first():
                username = f"{base_username}_{counter}"
                counter += 1
            
            user = User(
                username=username,
                email=email,
                jira_account_id=jira_account_id,
                jira_display_name=display_name,
                jira_avatar_url=avatar_url,
                auth_provider='jira',
                password_hash=None  # OAuth users don't have passwords
            )
            db.session.add(user)
            db.session.flush()  # Get user ID
        
        # Update or create Jira integration record
        integration = Integration.query.filter_by(
            user_id=user.id, platform="jira"
        ).first()
        
        if integration:
            integration.access_token = access_token
            integration.platform_user_id = jira_account_id
            integration.updated_at = datetime.utcnow()
            integration.is_active = True
            integration.settings = {"cloud_id": cloud_id}
        else:
            integration = Integration(
                user_id=user.id,
                platform="jira",
                platform_user_id=jira_account_id,
                access_token=access_token,
                is_active=True,
                settings={"cloud_id": cloud_id}
            )
            db.session.add(integration)
        
        db.session.commit()
        
        # Generate JWT token
        jwt_token = create_access_token(identity=str(user.id))
        
        return ApiResponse.success(
            "Jira authentication successful",
            data={"access_token": jwt_token, "user": user.to_dict()},
        )
        
    except Exception as e:
        db.session.rollback()
        return ApiResponse.failure(f"Error creating/updating Jira user: {str(e)}", code=500)


def link_jira_account(user_id, access_token, cloud_id):
    """
    Link Jira account to existing user
    
    Args:
        user_id (int): User ID
        access_token (str): Jira access token
        cloud_id (str): Atlassian cloud ID
        
    Returns:
        ApiResponse: Success or failure response
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return ApiResponse.failure("User not found", code=404)
        
        # Get user info from Jira to update profile
        try:
            jira_client = JiraAPIClient(access_token, cloud_id)
            user_info = jira_client.get_current_user()
            
            # Update user with Jira info
            user.jira_account_id = user_info.get("accountId")
            user.jira_display_name = user_info.get("displayName", "")
            user.jira_avatar_url = user_info.get("avatarUrls", {}).get("48x48", "")
            user.updated_at = datetime.utcnow()
            
        except Exception as e:
            print(f"Warning: Could not fetch Jira user info: {e}")
        
        # Update or create Jira integration
        integration = Integration.query.filter_by(
            user_id=user.id, platform="jira"
        ).first()
        
        if integration:
            integration.access_token = access_token
            integration.platform_user_id = user.jira_account_id
            integration.updated_at = datetime.utcnow()
            integration.is_active = True
            integration.settings = {"cloud_id": cloud_id}
        else:
            integration = Integration(
                user_id=user.id,
                platform="jira",
                platform_user_id=user.jira_account_id,
                access_token=access_token,
                is_active=True,
                settings={"cloud_id": cloud_id}
            )
            db.session.add(integration)
        
        db.session.commit()
        
        return ApiResponse.success(
            "Jira account linked successfully", data=user.to_dict()
        )
        
    except Exception as e:
        db.session.rollback()
        return ApiResponse.failure(f"Error linking Jira account: {str(e)}", code=500)


def unlink_jira_account(user_id):
    """
    Unlink Jira account from user
    
    Args:
        user_id (int): User ID
        
    Returns:
        ApiResponse: Success or failure response
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return ApiResponse.failure("User not found", code=404)
        
        # Find and deactivate Jira integration
        integration = Integration.query.filter_by(
            user_id=user_id, platform="jira"
        ).first()
        
        if integration:
            integration.is_active = False
            integration.deleted_at = datetime.utcnow()
        
        # Clear Jira-specific fields from user
        user.jira_account_id = None
        user.jira_display_name = None
        user.jira_avatar_url = None
        user.updated_at = datetime.utcnow()
        
        # If user was created via Jira OAuth and has no password, 
        # they won't be able to login anymore
        if user.auth_provider == 'jira' and not user.password_hash:
            return ApiResponse.failure(
                "Cannot unlink Jira account: User was created via Jira OAuth and has no password set",
                code=400
            )
        
        db.session.commit()
        
        return ApiResponse.success("Jira account unlinked successfully")
        
    except Exception as e:
        db.session.rollback()
        return ApiResponse.failure(f"Error unlinking Jira account: {str(e)}", code=500)


def get_jira_integration(user_id):
    """
    Get active Jira integration for user
    
    Args:
        user_id (int): User ID
        
    Returns:
        Integration or None: Jira integration if exists and active
    """
    return Integration.query.filter_by(
        user_id=user_id,
        platform="jira",
        is_active=True
    ).first()


def refresh_jira_token(user_id):
    """
    Refresh Jira access token using refresh token
    
    Args:
        user_id (int): User ID
        
    Returns:
        ApiResponse: Success or failure response
    """
    try:
        integration = get_jira_integration(user_id)
        if not integration:
            return ApiResponse.failure("No active Jira integration found", code=404)
        
        if not integration.refresh_token:
            return ApiResponse.failure("No refresh token available", code=400)
        
        # Use OAuth service to refresh token
        oauth_service = JiraOAuthService()
        token_data = oauth_service.refresh_access_token(integration.refresh_token)
        
        # Update integration with new token
        integration.access_token = token_data["access_token"]
        if "refresh_token" in token_data:
            integration.refresh_token = token_data["refresh_token"]
        if "expires_in" in token_data:
            integration.expires_at = datetime.utcnow() + datetime.timedelta(seconds=token_data["expires_in"])
        integration.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return ApiResponse.success("Jira token refreshed successfully")
        
    except Exception as e:
        db.session.rollback()
        return ApiResponse.failure(f"Error refreshing Jira token: {str(e)}", code=500)
