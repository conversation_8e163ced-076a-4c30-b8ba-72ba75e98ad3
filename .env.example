## ==============================
## ====== DOCKER VARIABLES ======
## ==============================
COMPOSE_PROJECT_NAME=tms-flask

### The platform to build the image: windows/amd64, linux/amd64, linux/arm64, linux/arm64/v8, darwin/amd64, darwin/arm64
PLATFORM=linux/amd64
NGINX_HOST="tms.uit.local"

MYSQL_HOST_PORT=3309
MYSQL_ROOT_PASSWORD=root

FLASK_HOST_PORT=5001

NGINX_HOST_HTTP_PORT=8084

PHPMYADMIN_PORT=9024
## ==============================


## ==============================
## ====== FLASK VARIABLES =======
## ==============================
FLASK_HOST="0.0.0.0"
FLASK_PORT=5000

# True for development, False for production
DEBUG=True

# Regular user
# DB_USER=root
# DB_PORT=3306
DB_NAME=tms
DB_HOST=db

# Flask Secret Key
SECRET_KEY=flask-secret-key-change-in-production-2024

# JWT
JWT_SECRET_KEY=pqjPNZpKuDBYEkoI1JWOPES4PRQpHEKe

# GitHub OAuth
GITHUB_CLIENT_ID=xxxxxxxxxxxxxxxxxxxx
GITHUB_CLIENT_SECRET=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
GITHUB_REDIRECT_URI=http://tms.uit.local:8084/auth/github/callback

# Jira OAuth
JIRA_CLIENT_ID=xxxxxxxxxxxxxxxxxxxx
JIRA_CLIENT_SECRET=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
JIRA_REDIRECT_URI=http://tms.uit.local:8084/auth/jira/callback
JIRA_AUDIENCE=api.atlassian.com
JIRA_SCOPE=read:jira-user read:jira-work write:jira-work manage:jira-project

# Email Configuration (for password reset emails)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>
FRONTEND_URL=http://tms.uit.local:8084

# Telegram Bot
TELEGRAM_BOT_TOKEN=1234567890:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
TELEGRAM_WEBHOOK_TOKEN=your_webhook_secret_token_here
