# 🔧 Jira OAuth Localhost Setup Guide

## 🚨 **Problem Identified**
- **Current Redirect URI**: `https://tms.50tbfk3bdujvlo.flashpanel.link/auth/jira/callback`
- **Testing on**: `http://localhost:5001`
- **Result**: OAuth callback goes to production, not localhost

## 🛠️ **Solution: Create Localhost OAuth App**

### **Step 1: Create New Jira OAuth App**
1. **Go to**: https://developer.atlassian.com/console/myapps/
2. **Click**: "Create" → "OAuth 2.0 integration"
3. **App name**: `TMS Local Development`
4. **Callback URL**: `http://localhost:5001/auth/jira/callback`

### **Step 2: Configure App Permissions**
1. **Click**: "Permissions" in left menu
2. **Add APIs**:
   - Jira platform REST API
   - User Identity API (optional)
3. **Configure scopes**:
   - `read:jira-user`
   - `read:jira-work` 
   - `write:jira-work`
   - `manage:jira-project`
   - `offline_access` (for refresh tokens)

### **Step 3: Get Credentials**
1. **Click**: "Settings" in left menu
2. **Copy**:
   - Client ID
   - Client Secret

### **Step 4: Update .env for Localhost**
Create `.env.local` file:
```bash
# Copy all from .env but update Jira settings:
JIRA_CLIENT_ID=your_localhost_client_id
JIRA_CLIENT_SECRET=your_localhost_client_secret  
JIRA_REDIRECT_URI=http://localhost:5001/auth/jira/callback
JIRA_AUDIENCE=api.atlassian.com
JIRA_SCOPE="read:jira-user read:jira-work write:jira-work manage:jira-project offline_access"
```

### **Step 5: Restart with Local Config**
```bash
# Backup current .env
cp .env .env.production

# Use local config
cp .env.local .env

# Restart container
docker-compose restart flask_app
```

## 🧪 **Test Localhost OAuth**

### **1. Generate OAuth URL**
```bash
curl -X GET http://localhost:5001/auth/jira/url
```

### **2. Complete OAuth Flow**
1. **Copy authorization_url** from response
2. **Open in browser** and authorize
3. **Should redirect** to `http://localhost:5001/auth/jira/callback`
4. **Should see** success response with JWT token

### **3. Test Integration Status**
```bash
curl -X GET http://localhost:5001/webhooks/jira/status \
  -H "Authorization: Bearer YOUR_NEW_JWT_TOKEN"
```

## 🔄 **Alternative: Use Production Domain**

If you prefer to test on production domain:

### **1. Access Production Jira Integration**
```
https://tms.50tbfk3bdujvlo.flashpanel.link/dashboard/jira-integration
```

### **2. Use Production OAuth**
- Current config already points to production
- OAuth will work correctly on production domain
- Can test all Jira features there

## 🎯 **Quick Fix for Current Session**

If you want to test immediately without creating new OAuth app:

### **1. Temporarily Update Redirect URI**
In Atlassian Developer Console:
1. **Go to your existing app**
2. **Authorization** → **Configure**
3. **Change Callback URL** to: `http://localhost:5001/auth/jira/callback`
4. **Save changes**

### **2. Test OAuth**
```bash
curl -X GET http://localhost:5001/auth/jira/url
# Complete OAuth flow in browser
```

### **3. Revert Redirect URI**
After testing, change back to:
`https://tms.50tbfk3bdujvlo.flashpanel.link/auth/jira/callback`

## 📋 **Troubleshooting**

### **Issue: "Invalid redirect_uri"**
- **Cause**: Callback URL doesn't match OAuth app config
- **Fix**: Update OAuth app callback URL to match testing domain

### **Issue: "Invalid state parameter"**
- **Cause**: Session state mismatch between request and callback
- **Fix**: Generate new OAuth URL and complete flow immediately

### **Issue: "No accessible Jira sites found"**
- **Cause**: User doesn't have access to any Jira sites
- **Fix**: Ensure user has access to at least one Jira site

### **Issue: OAuth works but integration status shows "not connected"**
- **Cause**: Integration record not created properly
- **Fix**: Check database for integration records:
```sql
SELECT * FROM integration WHERE platform = 'jira' AND user_id = YOUR_USER_ID;
```

## ✅ **Expected Results**

### **After Successful OAuth:**
1. **Jira Integration Status**: `connected: true`
2. **User Profile**: Updated with Jira account info
3. **Integration Record**: Created in database
4. **JWT Token**: Contains user with Jira integration

### **Available Features:**
1. **Project Sync**: Fetch Jira projects
2. **Issue Sync**: Fetch and sync Jira issues  
3. **Webhook Support**: Real-time updates
4. **API Access**: Full Jira REST API access

## 🚀 **Recommended Approach**

**For Development**: Create separate localhost OAuth app
**For Testing**: Use production domain with existing OAuth app
**For Production**: Keep current production OAuth app

This ensures clean separation between environments and avoids callback URL conflicts.
