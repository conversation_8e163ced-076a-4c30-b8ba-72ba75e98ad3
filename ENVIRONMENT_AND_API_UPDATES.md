# Environment Variables and API Documentation Updates

## 🔧 Environment Variables Added

### Jira OAuth Configuration
Added the following environment variables to `.env.example`:

```bash
# Jira OAuth
JIRA_CLIENT_ID=xxxxxxxxxxxxxxxxxxxx
JIRA_CLIENT_SECRET=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
JIRA_REDIRECT_URI=http://tms.uit.local:8084/auth/jira/callback
JIRA_AUDIENCE=api.atlassian.com
JIRA_SCOPE=read:jira-user read:jira-work write:jira-work manage:jira-project
```

### Required Setup
To use Jira integration, you need to:

1. **Create Jira OAuth App** at https://developer.atlassian.com/console/myapps/
2. **Set OAuth 2.0 (3LO)** configuration:
   - Authorization callback URL: `http://tms.uit.local:8084/auth/jira/callback`
   - Scopes: `read:jira-user read:jira-work write:jira-work manage:jira-project`
3. **Copy Client ID and Secret** to your `.env` file
4. **Update JIRA_REDIRECT_URI** to match your domain

## 📚 API Documentation Updates

### New Endpoints Added to OpenAPI

#### 1. Tasks API
- **GET** `/tasks/{task_id}` - Get task details by ID
  - Security: JWT required
  - Permissions: Only creator or assignee can view
  - Returns: Full task details with project info

#### 2. Dashboard API
- **GET** `/dashboard/api/stats` - Get dashboard statistics
  - Security: Public endpoint
  - Returns: System-wide statistics (users, projects, tasks, integrations)

#### 3. Jira Authentication API
- **POST** `/auth/jira/link` - Link Jira account to current user
  - Security: JWT required
  - Body: `{ access_token, cloud_id }`
  - Returns: Updated user profile

- **POST** `/auth/jira/unlink` - Unlink Jira account from current user
  - Security: JWT required
  - Returns: Updated user profile

### Existing Endpoints Already Documented
The following endpoints were already present in OpenAPI:

#### Tasks Management
- `GET /tasks/list_tasks` - List all tasks
- `POST /tasks/create_task_view` - Create new task
- `PUT /tasks/update_task_view/{task_id}` - Update task
- `DELETE /tasks/delete_task_view/{task_id}` - Delete task
- `POST /tasks/github/sync/{project_id}` - Sync GitHub issues

#### Projects Management
- `GET /projects/` - List projects
- `GET /projects/{project_id}` - Get project details
- `POST /projects/` - Create project
- `PUT /projects/{project_id}` - Update project
- `DELETE /projects/{project_id}` - Delete project

#### Jira Integration
- `GET /webhooks/jira/status` - Check Jira integration status
- `GET /webhooks/jira/projects` - List Jira projects
- `GET /webhooks/jira/projects/{key}/issues` - Get project issues
- `POST /webhooks/jira/issues/{key}/sync` - Sync issue to task
- `POST /webhooks/jira/tasks/{id}/sync` - Sync task to issue
- `GET /webhooks/jira/search` - Search with JQL
- `POST /webhooks/jira/webhook` - Webhook endpoint

#### GitHub Integration
- `GET /projects/github/status` - Check GitHub integration
- `GET /projects/github/repositories` - List repositories
- `POST /projects/github/repositories/{repo}/sync` - Sync repository

#### Authentication
- `POST /auth/login` - User login
- `POST /auth/register` - User registration
- `GET /auth/me` - Get current user info
- `POST /auth/logout` - User logout
- `GET /auth/github` - GitHub OAuth login
- `GET /auth/jira` - Jira OAuth login

## 🚀 Frontend Integration Updates

### Tasks Manager
- ✅ Added `loadGitHubRepositories()` function
- ✅ Added `loadJiraProjects()` function  
- ✅ Changed Jira Project Key from input to dropdown
- ✅ Auto-load repositories and projects on page load
- ✅ Fixed task details endpoint (`GET /tasks/{id}`)

### API Explorer
- ✅ Fixed Details button functionality
- ✅ Added proper JSON encoding for endpoint data
- ✅ Enhanced error handling and debugging

### Auth Manager
- ✅ Fixed authentication status check (use `/auth/me`)
- ✅ Enhanced user profile display with roles
- ✅ Added integration status display

## 📖 Documentation Access

### Swagger UI
- **Local**: http://localhost:5001/apidocs/
- **Production**: https://tms.50tbfk3bdujvlo.flashpanel.link/apidocs/

### Dashboard
- **API Explorer**: http://localhost:5001/dashboard/api-explorer
- **API Tester**: http://localhost:5001/dashboard/api-tester
- **Projects Manager**: http://localhost:5001/dashboard/projects-manager
- **Tasks Manager**: http://localhost:5001/dashboard/tasks-manager

## 🔍 Testing

### Environment Setup
1. Copy `.env.example` to `.env`
2. Fill in your Jira OAuth credentials
3. Restart Docker containers: `docker-compose restart`

### API Testing
1. Use Swagger UI for interactive testing
2. Use Dashboard API Tester for custom requests
3. Check API Explorer for endpoint documentation

### Integration Testing
1. Test GitHub OAuth and repository sync
2. Test Jira OAuth and project/issue sync
3. Test task creation and management
4. Test dashboard statistics and views

## 🎯 Next Steps

1. **Setup Jira OAuth** with your Atlassian account
2. **Test integration flows** end-to-end
3. **Configure webhooks** for real-time sync
4. **Monitor API usage** through dashboard stats
5. **Extend documentation** as needed for new features
