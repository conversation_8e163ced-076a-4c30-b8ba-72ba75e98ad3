"""
User-related models for Task Management System
"""

from app.helpers.extensions import db
from datetime import datetime, timedelta
import secrets
from app.models.constants import ROLE_ADMIN

class User(db.Model):
    """
    User model for authentication and user management
    """
    __tablename__ = 'user'
    
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=True)  # Made nullable for OAuth users
    username = db.Column(db.String(100), nullable=False)
    two_factor_enabled = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = db.Column(db.DateTime, nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    bio = db.Column(db.Text, nullable=True)

    # GitHub OAuth fields
    github_id = db.Column(db.String(50), unique=True, nullable=True)
    github_username = db.Column(db.String(100), nullable=True)
    github_avatar_url = db.Column(db.String(255), nullable=True)
    auth_provider = db.Column(db.String(50), default='local')  # 'local', 'github', etc.

    def to_dict(self):
        """Convert user object to dictionary"""
        return {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "phone": self.phone,
            "bio": self.bio,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "two_factor_enabled": self.two_factor_enabled,
            "github_id": self.github_id,
            "github_username": self.github_username,
            "github_avatar_url": self.github_avatar_url,
            "auth_provider": self.auth_provider,
        }

    def __repr__(self):
        return f"<User {self.username}>"

    def is_admin(self):
        """Check if the user has admin role"""
        # Check if the user has a role_id
        from app.models.auth import UserRole  # Import here to avoid circular imports
        user_role = UserRole.query.filter_by(user_id=self.id).first()
        if user_role and user_role.role.name == ROLE_ADMIN:
            return True
        return False

class UserRole(db.Model):
    """
    Many-to-many relationship between Users and Roles
    """
    __tablename__ = 'user_roles'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    role_id = db.Column(db.Integer, db.ForeignKey('roles.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    user = db.relationship("User", backref=db.backref("user_roles", lazy=True))
    role = db.relationship("Role", backref=db.backref("user_roles", lazy=True))

    def __repr__(self):
        return f"<UserRole user_id={self.user_id} role_id={self.role_id}>"


class PasswordResetToken(db.Model):
    """
    Model for storing password reset tokens
    """
    __tablename__ = 'password_reset_tokens'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    token = db.Column(db.String(255), unique=True, nullable=False)
    expires_at = db.Column(db.DateTime, nullable=False)
    used = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationship
    user = db.relationship("User", backref=db.backref("password_reset_tokens", lazy=True))

    @staticmethod
    def generate_token(user_id, expires_in_minutes=30):
        """
        Generate a new password reset token for a user

        Args:
            user_id (int): User ID
            expires_in_minutes (int): Token expiration time in minutes

        Returns:
            PasswordResetToken: New token instance
        """
        # Invalidate any existing unused tokens for this user
        PasswordResetToken.query.filter_by(
            user_id=user_id,
            used=False
        ).update({'used': True})

        # Generate secure token
        token = secrets.token_urlsafe(32)
        expires_at = datetime.utcnow() + timedelta(minutes=expires_in_minutes)

        # Create new token
        reset_token = PasswordResetToken(
            user_id=user_id,
            token=token,
            expires_at=expires_at
        )

        db.session.add(reset_token)
        db.session.commit()

        return reset_token

    @staticmethod
    def verify_token(token):
        """
        Verify a password reset token

        Args:
            token (str): Token to verify

        Returns:
            PasswordResetToken or None: Valid token instance or None
        """
        reset_token = PasswordResetToken.query.filter_by(
            token=token,
            used=False
        ).first()

        if not reset_token:
            return None

        # Check if token has expired
        if datetime.utcnow() > reset_token.expires_at:
            return None

        return reset_token

    def mark_as_used(self):
        """Mark token as used"""
        self.used = True
        db.session.commit()

    def __repr__(self):
        return f"<PasswordResetToken user_id={self.user_id} expires_at={self.expires_at}>"
