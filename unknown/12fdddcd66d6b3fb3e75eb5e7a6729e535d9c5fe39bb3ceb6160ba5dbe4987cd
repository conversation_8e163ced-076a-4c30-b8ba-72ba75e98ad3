"""
Authentication and authorization related models for Task Management System
"""

from app.helpers.extensions import db
from datetime import datetime


class Role(db.Model):
    """
    Role model for role-based access control
    """
    __tablename__ = 'roles'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    description = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.String(100), nullable=True)
    updated_by = db.Column(db.String(100), nullable=True)

    def to_dict(self):
        """Convert role object to dictionary"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    def __repr__(self):
        return f"<Role {self.name}>"


class Permission(db.Model):
    """
    Permission model for granular access control
    """
    __tablename__ = 'permissions'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    description = db.Column(db.String(255))
    resource = db.Column(db.String(50), nullable=True)  # e.g., 'task', 'project', 'user'
    action = db.Column(db.String(50), nullable=True)    # e.g., 'create', 'read', 'update', 'delete'

    def to_dict(self):
        """Convert permission object to dictionary"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "resource": self.resource,
            "action": self.action,
        }

    def __repr__(self):
        return f"<Permission {self.name}>"



class RolePermission(db.Model):
    """
    Many-to-many relationship between Roles and Permissions
    """
    __tablename__ = 'role_permissions'

    id = db.Column(db.Integer, primary_key=True)
    role_id = db.Column(db.Integer, db.ForeignKey('roles.id'), nullable=False)
    permission_id = db.Column(db.Integer, db.ForeignKey('permissions.id'), nullable=False)
    granted_at = db.Column(db.DateTime, default=datetime.utcnow)
    granted_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)

    # Relationships
    role = db.relationship("Role", backref=db.backref("role_permissions", lazy=True))
    permission = db.relationship("Permission", backref=db.backref("role_permissions", lazy=True))
    granter = db.relationship("User", backref=db.backref("granted_permissions", lazy=True))

    def to_dict(self):
        """Convert role permission object to dictionary"""
        return {
            "id": self.id,
            "role_id": self.role_id,
            "permission_id": self.permission_id,
            "granted_at": self.granted_at.isoformat() if self.granted_at else None,
            "granted_by": self.granted_by,
        }

    def __repr__(self):
        return f"<RolePermission role_id={self.role_id} permission_id={self.permission_id}>"
