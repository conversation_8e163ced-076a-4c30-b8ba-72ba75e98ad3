"""Add Jira fields to Project table

Revision ID: b917a2ff7748
Revises: 7659df8ca49a
Create Date: 2025-06-14 08:30:10.028130

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'b917a2ff7748'
down_revision = '7659df8ca49a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('project', schema=None) as batch_op:
        batch_op.add_column(sa.Column('jira_project_id', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('jira_project_key', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('jira_project_name', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('is_jira_synced', sa.<PERSON>an(), nullable=True))
        batch_op.add_column(sa.Column('last_jira_sync', sa.DateTime(), nullable=True))

    with op.batch_alter_table('webhook_events', schema=None) as batch_op:
        batch_op.add_column(sa.Column('github_webhook_id', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('jira_webhook_id', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('delivery_id', sa.String(length=255), nullable=False))
        batch_op.drop_constraint('webhook_events_ibfk_1', type_='foreignkey')
        batch_op.create_foreign_key(None, 'github_webhooks', ['github_webhook_id'], ['id'])
        batch_op.create_foreign_key(None, 'jira_webhooks', ['jira_webhook_id'], ['id'])
        batch_op.drop_column('github_delivery_id')
        batch_op.drop_column('webhook_id')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('webhook_events', schema=None) as batch_op:
        batch_op.add_column(sa.Column('webhook_id', mysql.INTEGER(), autoincrement=False, nullable=False))
        batch_op.add_column(sa.Column('github_delivery_id', mysql.VARCHAR(length=255), nullable=False))
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('webhook_events_ibfk_1', 'github_webhooks', ['webhook_id'], ['id'])
        batch_op.drop_column('delivery_id')
        batch_op.drop_column('jira_webhook_id')
        batch_op.drop_column('github_webhook_id')

    with op.batch_alter_table('project', schema=None) as batch_op:
        batch_op.drop_column('last_jira_sync')
        batch_op.drop_column('is_jira_synced')
        batch_op.drop_column('jira_project_name')
        batch_op.drop_column('jira_project_key')
        batch_op.drop_column('jira_project_id')

    # ### end Alembic commands ###
