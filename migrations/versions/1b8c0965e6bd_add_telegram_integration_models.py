"""Add Telegram integration models

Revision ID: 1b8c0965e6bd
Revises: b5bcda3642b8
Create Date: 2025-01-15 10:00:00.000000

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "1b8c0965e6bd"
down_revision = "b5bcda3642b8"
branch_labels = None
depends_on = None


def upgrade():
    """Create all Telegram integration tables"""

    # Create telegram_integrations table
    op.create_table(
        "telegram_integrations",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("telegram_user_id", sa.String(length=50), nullable=False),
        sa.Column("telegram_username", sa.String(length=100), nullable=True),
        sa.Column("telegram_first_name", sa.String(length=100), nullable=True),
        sa.Column("telegram_last_name", sa.String(length=100), nullable=True),
        sa.Column("chat_id", sa.String(length=50), nullable=False),
        sa.Column("is_active", sa.Boolean(), nullable=True, default=True),
        sa.Column("language_code", sa.String(length=10), nullable=True, default="en"),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("last_interaction", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("telegram_user_id"),
    )

    # Create telegram_subscriptions table
    op.create_table(
        "telegram_subscriptions",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("telegram_integration_id", sa.Integer(), nullable=False),
        sa.Column("notification_type", sa.String(length=50), nullable=False),
        sa.Column("is_enabled", sa.Boolean(), nullable=True, default=True),
        sa.Column("settings", sa.JSON(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["telegram_integration_id"],
            ["telegram_integrations.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "telegram_integration_id", "notification_type", name="unique_subscription"
        ),
    )

    # Create telegram_notification_logs table
    op.create_table(
        "telegram_notification_logs",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("telegram_integration_id", sa.Integer(), nullable=False),
        sa.Column("notification_type", sa.String(length=50), nullable=False),
        sa.Column("message_content", sa.Text(), nullable=False),
        sa.Column("telegram_message_id", sa.String(length=50), nullable=True),
        sa.Column("status", sa.String(length=20), nullable=True, default="pending"),
        sa.Column("error_message", sa.Text(), nullable=True),
        sa.Column("webhook_event_id", sa.Integer(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("sent_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["telegram_integration_id"],
            ["telegram_integrations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["webhook_event_id"],
            ["webhook_events.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )


def downgrade():
    """Drop all Telegram integration tables"""

    # Drop tables in reverse order to handle foreign key constraints
    op.drop_table("telegram_notification_logs")
    op.drop_table("telegram_subscriptions")
    op.drop_table("telegram_integrations")
