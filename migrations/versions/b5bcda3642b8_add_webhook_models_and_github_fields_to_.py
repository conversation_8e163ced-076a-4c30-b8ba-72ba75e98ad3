"""Add webhook models and GitHub fields to tasks

Revision ID: b5bcda3642b8
Revises: 40e0e7f76be9
Create Date: 2025-06-12 10:31:33.409705

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b5bcda3642b8'
down_revision = '40e0e7f76be9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('github_webhooks',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('project_id', sa.Integer(), nullable=True),
    sa.Column('github_webhook_id', sa.String(length=255), nullable=False),
    sa.Column('github_repo_full_name', sa.String(length=255), nullable=False),
    sa.Column('webhook_url', sa.String(length=500), nullable=False),
    sa.Column('secret', sa.String(length=255), nullable=False),
    sa.Column('events', sa.JSON(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('last_ping', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['project_id'], ['project.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('comment',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('task_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['task_id'], ['task.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('webhook_events',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('webhook_id', sa.Integer(), nullable=False),
    sa.Column('github_delivery_id', sa.String(length=255), nullable=False),
    sa.Column('event_type', sa.String(length=50), nullable=False),
    sa.Column('action', sa.String(length=50), nullable=True),
    sa.Column('payload', sa.JSON(), nullable=False),
    sa.Column('processed', sa.Boolean(), nullable=True),
    sa.Column('processed_at', sa.DateTime(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['webhook_id'], ['github_webhooks.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('task', schema=None) as batch_op:
        batch_op.add_column(sa.Column('github_issue_id', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('github_issue_number', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('github_issue_url', sa.String(length=500), nullable=True))
        batch_op.add_column(sa.Column('is_github_synced', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('last_github_sync', sa.DateTime(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('task', schema=None) as batch_op:
        batch_op.drop_column('last_github_sync')
        batch_op.drop_column('is_github_synced')
        batch_op.drop_column('github_issue_url')
        batch_op.drop_column('github_issue_number')
        batch_op.drop_column('github_issue_id')

    op.drop_table('webhook_events')
    op.drop_table('comment')
    op.drop_table('github_webhooks')
    # ### end Alembic commands ###
