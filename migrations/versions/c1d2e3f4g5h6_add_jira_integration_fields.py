"""Add Jira integration fields and JiraWebhook table

Revision ID: c1d2e3f4g5h6
Revises: 2392774a4931
Create Date: 2025-06-14 07:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'c1d2e3f4g5h6'
down_revision = '2392774a4931'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Add Jira fields to user table
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.add_column(sa.Column('jira_account_id', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('jira_display_name', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('jira_avatar_url', sa.String(length=255), nullable=True))
        batch_op.create_unique_constraint('uq_user_jira_account_id', ['jira_account_id'])

    # Create jira_webhooks table
    op.create_table('jira_webhooks',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('project_id', sa.Integer(), nullable=True),
        sa.Column('jira_webhook_id', sa.String(length=255), nullable=False),
        sa.Column('project_key', sa.String(length=50), nullable=False),
        sa.Column('webhook_url', sa.String(length=500), nullable=False),
        sa.Column('events', sa.JSON(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('last_ping', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['project_id'], ['project.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Drop jira_webhooks table
    op.drop_table('jira_webhooks')
    
    # Remove Jira fields from user table
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.drop_constraint('uq_user_jira_account_id', type_='unique')
        batch_op.drop_column('jira_avatar_url')
        batch_op.drop_column('jira_display_name')
        batch_op.drop_column('jira_account_id')

    # ### end Alembic commands ###
