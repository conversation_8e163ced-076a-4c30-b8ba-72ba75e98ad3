"""
Seed script to generate sample activity logs for testing
"""
import random
from datetime import datetime, timedelta
from app import create_app
from app.models.system import ActivityLog  # Keep for direct DB access in seed script
from app.models.user import User
from app.models.project import Project
from app.models.task import Task
from app.helpers.extensions import db
from app.history.logger import activity_logger


def create_sample_activity_logs():
    """Create sample activity logs for testing"""
    app = create_app()
    with app.app_context():
        try:
            print("Generating sample activity logs...")
            
            # Get existing users, projects, and tasks to reference
            users = User.query.all()
            projects = Project.query.all()
            tasks = Task.query.all()
            
            if not users:
                print("No users found in database. Please run seed.py first.")
                return
                
            # List of possible actions
            user_actions = [
                "login", "logout", "update_profile", "reset_password", "enable_2fa", "disable_2fa"
            ]
            
            project_actions = [
                "create_project", "update_project", "delete_project", "restore_project", 
                "archive_project", "sync_github_repo"
            ]
            
            task_actions = [
                "create_task", "update_task", "delete_task", "assign_task", "complete_task",
                "reopen_task", "attach_file", "add_comment", "change_priority"
            ]
            
            # Generate some user activity logs
            for user in users:
                # Create between 5-15 logs per user
                for _ in range(random.randint(5, 15)):
                    action = random.choice(user_actions)
                    
                    # Create a random timestamp in the past 30 days
                    timestamp = datetime.utcnow() - timedelta(
                        days=random.randint(0, 30),
                        hours=random.randint(0, 23),
                        minutes=random.randint(0, 59)
                    )
                    
                    # Generate appropriate description and details
                    if action == "login":
                        description = f"User {user.username} logged in"
                        details = {
                            "login_method": random.choice(["password", "github", "google"])
                        }
                    elif action == "logout":
                        description = f"User {user.username} logged out"
                        details = {}
                    elif action == "update_profile":
                        description = f"User {user.username} updated profile"
                        details = {
                            "updated_fields": random.sample(["username", "email", "bio", "avatar"], 
                                                          random.randint(1, 3))
                        }
                    elif action == "reset_password":
                        description = f"User {user.username} reset password"
                        details = {"initiated_by": "user"}
                    elif action == "enable_2fa":
                        description = f"User {user.username} enabled two-factor authentication"
                        details = {"method": "app"}
                    elif action == "disable_2fa":
                        description = f"User {user.username} disabled two-factor authentication"
                        details = {}
                        
                    # Use activity_logger singleton
                    activity_logger.log(
                        user_id=user.id,
                        action=action,
                        entity_type="user",
                        entity_id=user.id,
                        description=description,
                        details=details
                    )
                    
                  
                    user_log = ActivityLog.query.filter_by(
                        user_id=user.id,
                        action=action,
                        entity_type="user",
                        entity_id=user.id
                    ).order_by(ActivityLog.id.desc()).first()
                    
                    if user_log:
                        user_log.created_at = timestamp
                        user_log.ip_address = f"192.168.1.{random.randint(1, 254)}"
                        user_log.user_agent = "Mozilla/5.0 (Sample UserAgent)"
            
            # Generate some project activity logs
            if projects:
                for project in projects:
                    # Find users who might be working on this project
                    project_users = users[:3] if len(users) >= 3 else users
                    
                    # Create between 5-15 logs per project
                    for _ in range(random.randint(5, 15)):
                        user = random.choice(project_users)
                        action = random.choice(project_actions)
                        
                        # Create a random timestamp in the past 30 days
                        timestamp = datetime.utcnow() - timedelta(
                            days=random.randint(0, 30),
                            hours=random.randint(0, 23),
                            minutes=random.randint(0, 59)
                        )
                        
                        # Generate appropriate description and details
                        if action == "create_project":
                            description = f"Created project {project.name}"
                            details = {"description": project.description}
                        elif action == "update_project":
                            description = f"Updated project {project.name}"
                            fields = random.sample(["name", "description", "status", "due_date"], 
                                                random.randint(1, 3))
                            details = {"updated_fields": fields}
                        elif action == "delete_project":
                            description = f"Deleted project {project.name}"
                            details = {"permanent": False}
                        elif action == "restore_project":
                            description = f"Restored project {project.name}"
                            details = {}
                        elif action == "archive_project":
                            description = f"Archived project {project.name}"
                            details = {}
                        elif action == "sync_github_repo":
                            description = f"Synchronized GitHub repo for project {project.name}"
                            details = {"issues_added": random.randint(0, 5)}
                        
                        activity_logger.log(
                            user_id=user.id,
                            action=action,
                            entity_type="project",
                            entity_id=project.id,
                            project_id=project.id,
                            description=description,
                            details=details
                        )
                        
                        project_log = ActivityLog.query.filter_by(
                            user_id=user.id,
                            action=action,
                            entity_type="project",
                            entity_id=project.id
                        ).order_by(ActivityLog.id.desc()).first()
                        
                        if project_log:
                            project_log.created_at = timestamp
                            project_log.ip_address = f"192.168.1.{random.randint(1, 254)}"
                            project_log.user_agent = "Mozilla/5.0 (Sample UserAgent)"
            
            # Generate some task activity logs
            if tasks:
                for task in tasks:
                    # Find users who might be working on this task
                    task_users = users[:3] if len(users) >= 3 else users
                    
                    # Create between 3-10 logs per task
                    for _ in range(random.randint(3, 10)):
                        user = random.choice(task_users)
                        action = random.choice(task_actions)
                        
                        # Create a random timestamp in the past 30 days
                        timestamp = datetime.utcnow() - timedelta(
                            days=random.randint(0, 30),
                            hours=random.randint(0, 23),
                            minutes=random.randint(0, 59)
                        )
                        
                        # Generate appropriate description and details
                        if action == "create_task":
                            description = f"Created task {task.title}"
                            details = {"description": task.description}
                        elif action == "update_task":
                            description = f"Updated task {task.title}"
                            fields = random.sample(["title", "description", "status", "due_date", "priority"], 
                                                random.randint(1, 3))
                            details = {"updated_fields": fields}
                        elif action == "delete_task":
                            description = f"Deleted task {task.title}"
                            details = {}
                        elif action == "assign_task":
                            assigned_user = random.choice(users)
                            description = f"Assigned task {task.title} to {assigned_user.username}"
                            details = {"assigned_to": assigned_user.id}
                        elif action == "complete_task":
                            description = f"Completed task {task.title}"
                            details = {"previous_status": "in_progress"}
                        elif action == "reopen_task":
                            description = f"Reopened task {task.title}"
                            details = {"previous_status": "completed"}
                        elif action == "attach_file":
                            description = f"Attached file to task {task.title}"
                            details = {"filename": f"document-{random.randint(1, 100)}.pdf"}
                        elif action == "add_comment":
                            description = f"Added comment to task {task.title}"
                            details = {"comment_preview": "This is a sample comment..."}
                        elif action == "change_priority":
                            priorities = ["low", "medium", "high", "urgent"]
                            old_priority = random.choice(priorities)
                            new_priority = random.choice(priorities)
                            description = f"Changed priority of task {task.title}"
                            details = {"from": old_priority, "to": new_priority}
                        
                        # Use activity_logger singleton
                        activity_logger.log(
                            user_id=user.id,
                            action=action,
                            entity_type="task",
                            entity_id=task.id,
                            project_id=task.project_id,
                            task_id=task.id,
                            description=description,
                            details=details
                        )
                        
                        task_log = ActivityLog.query.filter_by(
                            user_id=user.id,
                            action=action,
                            entity_type="task",
                            entity_id=task.id
                        ).order_by(ActivityLog.id.desc()).first()
                        
                        if task_log:
                            task_log.created_at = timestamp
                            task_log.ip_address = f"192.168.1.{random.randint(1, 254)}"
                            task_log.user_agent = "Mozilla/5.0 (Sample UserAgent)"
            
            # Commit all logs
            db.session.commit()
            print(f"Successfully created sample activity logs")
            
        except Exception as e:
            print(f"Error creating sample activity logs: {str(e)}")
            db.session.rollback()
            raise e


if __name__ == "__main__":
    create_sample_activity_logs()
