# Jira Configuration Templates

## 📋 Mục lục

1. [Environment Variables](#environment-variables)
2. [Docker Configuration](#docker-configuration)
3. [Jira App Settings](#jira-app-settings)
4. [Database Configuration](#database-configuration)
5. [Nginx Configuration](#nginx-configuration)

## 🔧 Environment Variables

### Development (.env)

```env
# ======================
# JIRA INTEGRATION
# ======================

# OAuth 2.0 Credentials
JIRA_CLIENT_ID=your_jira_client_id_here
JIRA_CLIENT_SECRET=your_jira_client_secret_here
JIRA_REDIRECT_URI=http://localhost:8084/auth/jira/callback

# Optional: Custom Jira instance URL (for Jira Server)
# JIRA_BASE_URL=https://your-company.atlassian.net

# ======================
# DATABASE
# ======================
DATABASE_URL=mysql+pymysql://root:password@localhost:3306/tms_db

# ======================
# JWT CONFIGURATION
# ======================
JWT_SECRET_KEY=your-super-secret-jwt-key-here
JWT_ACCESS_TOKEN_EXPIRES=3600

# ======================
# TELEGRAM (Optional)
# ======================
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# ======================
# FLASK CONFIGURATION
# ======================
FLASK_ENV=development
FLASK_DEBUG=1
SECRET_KEY=your-flask-secret-key
```

### Production (.env.production)

```env
# ======================
# JIRA INTEGRATION
# ======================
JIRA_CLIENT_ID=prod_jira_client_id
JIRA_CLIENT_SECRET=prod_jira_client_secret
JIRA_REDIRECT_URI=https://yourdomain.com/auth/jira/callback

# ======================
# DATABASE
# ======================
DATABASE_URL=mysql+pymysql://user:secure_password@db:3306/tms_production

# ======================
# SECURITY
# ======================
JWT_SECRET_KEY=super-secure-jwt-key-for-production
SECRET_KEY=super-secure-flask-key-for-production

# ======================
# FLASK CONFIGURATION
# ======================
FLASK_ENV=production
FLASK_DEBUG=0

# ======================
# LOGGING
# ======================
LOG_LEVEL=INFO
LOG_FILE=/app/logs/app.log
```

## 🐳 Docker Configuration

### docker-compose.yml

```yaml
version: '3.8'

services:
  backend:
    build: .
    container_name: tms-backend
    ports:
      - "8084:5000"
    environment:
      # Jira Integration
      - JIRA_CLIENT_ID=${JIRA_CLIENT_ID}
      - JIRA_CLIENT_SECRET=${JIRA_CLIENT_SECRET}
      - JIRA_REDIRECT_URI=${JIRA_REDIRECT_URI}
      
      # Database
      - DATABASE_URL=mysql+pymysql://root:${MYSQL_ROOT_PASSWORD}@mysql:3306/${MYSQL_DATABASE}
      
      # Security
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - SECRET_KEY=${SECRET_KEY}
      
      # Flask
      - FLASK_ENV=${FLASK_ENV:-production}
      - FLASK_DEBUG=${FLASK_DEBUG:-0}
      
      # Optional: Telegram
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
    
    depends_on:
      - mysql
    
    volumes:
      - ./logs:/app/logs
    
    networks:
      - tms-network

  mysql:
    image: mysql:8.0
    container_name: tms-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=${MYSQL_DATABASE}
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
    
    ports:
      - "3306:3306"
    
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    
    networks:
      - tms-network

  nginx:
    image: nginx:alpine
    container_name: tms-nginx
    ports:
      - "80:80"
      - "443:443"
    
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
    
    depends_on:
      - backend
    
    networks:
      - tms-network

volumes:
  mysql_data:

networks:
  tms-network:
    driver: bridge
```

### Dockerfile

```dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY docker/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create logs directory
RUN mkdir -p /app/logs

# Set environment variables
ENV PYTHONPATH=/app
ENV FLASK_APP=run.py

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5000/health || exit 1

# Run application
CMD ["python", "run.py"]
```

## ⚙️ Jira App Settings

### Atlassian App Configuration

```json
{
  "name": "TMS Integration",
  "description": "Task Management System Integration with Jira",
  "vendor": {
    "name": "Your Company",
    "url": "https://yourcompany.com"
  },
  "authentication": {
    "type": "oauth2",
    "oauth2": {
      "clientId": "your-client-id",
      "scopes": [
        "read:jira-work",
        "write:jira-work",
        "read:jira-user",
        "offline_access"
      ],
      "redirectUrls": [
        "http://localhost:8084/auth/jira/callback",
        "https://yourdomain.com/auth/jira/callback"
      ]
    }
  },
  "lifecycle": {
    "installed": "/installed",
    "uninstalled": "/uninstalled"
  },
  "modules": {
    "webhooks": [
      {
        "event": "jira:issue_created",
        "url": "/webhooks/jira/webhook"
      },
      {
        "event": "jira:issue_updated", 
        "url": "/webhooks/jira/webhook"
      },
      {
        "event": "jira:issue_deleted",
        "url": "/webhooks/jira/webhook"
      }
    ]
  }
}
```

### Webhook Configuration

```json
{
  "name": "TMS Webhook",
  "url": "https://yourdomain.com/webhooks/jira/webhook?user_id=${user.id}",
  "events": [
    "jira:issue_created",
    "jira:issue_updated", 
    "jira:issue_deleted"
  ],
  "filters": {
    "issue-related-events-section": "project = PROJ"
  },
  "excludeBody": false
}
```

## 🗄️ Database Configuration

### MySQL Configuration (my.cnf)

```ini
[mysqld]
# Basic Settings
port = 3306
bind-address = 0.0.0.0
default-storage-engine = InnoDB

# Character Set
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# InnoDB Settings
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_flush_log_at_trx_commit = 1
innodb_lock_wait_timeout = 50

# Connection Settings
max_connections = 200
max_connect_errors = 10000
wait_timeout = 28800
interactive_timeout = 28800

# Query Cache
query_cache_type = 1
query_cache_size = 32M

# Logging
log-error = /var/log/mysql/error.log
slow-query-log = 1
slow-query-log-file = /var/log/mysql/slow.log
long_query_time = 2

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
```

### Database Initialization Script

```sql
-- docker/mysql/init.sql

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS tms_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user for application
CREATE USER IF NOT EXISTS 'tms_user'@'%' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON tms_db.* TO 'tms_user'@'%';

-- Create indexes for better performance
USE tms_db;

-- Indexes for Jira integration
CREATE INDEX idx_integration_user_platform ON integration(user_id, platform);
CREATE INDEX idx_external_task_mapping_platform ON external_task_mapping(external_platform);
CREATE INDEX idx_jira_webhooks_user ON jira_webhooks(user_id);
CREATE INDEX idx_jira_webhooks_project ON jira_webhooks(project_id);

-- Indexes for performance
CREATE INDEX idx_user_jira_account ON user(jira_account_id);
CREATE INDEX idx_project_jira_key ON project(jira_project_key);
CREATE INDEX idx_webhook_events_platform ON webhook_events(platform);

FLUSH PRIVILEGES;
```

## 🌐 Nginx Configuration

### nginx.conf

```nginx
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:5000;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=webhook:10m rate=5r/s;

    server {
        listen 80;
        server_name yourdomain.com www.yourdomain.com;
        
        # Redirect HTTP to HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name yourdomain.com www.yourdomain.com;

        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
        ssl_prefer_server_ciphers off;

        # Security Headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

        # API Routes
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Webhook Routes (separate rate limit)
        location /webhooks/ {
            limit_req zone=webhook burst=10 nodelay;
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Webhook specific settings
            proxy_read_timeout 30s;
            proxy_connect_timeout 10s;
        }

        # Auth Routes
        location /auth/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Health Check
        location /health {
            proxy_pass http://backend;
            access_log off;
        }

        # Static files (if any)
        location /static/ {
            alias /app/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

## 🔒 Security Configuration

### SSL Certificate (Let's Encrypt)

```bash
# Install certbot
sudo apt-get install certbot python3-certbot-nginx

# Get certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Environment Security

```bash
# Set proper file permissions
chmod 600 .env
chmod 600 .env.production

# Use Docker secrets for production
echo "your_secret" | docker secret create jwt_secret -
echo "your_db_password" | docker secret create db_password -
```

## 📊 Monitoring Configuration

### Health Check Endpoint

```python
# Add to your Flask app
@app.route('/health')
def health_check():
    return {
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.0.0',
        'services': {
            'database': check_database_connection(),
            'jira': check_jira_integration()
        }
    }
```

### Logging Configuration

```python
# logging_config.py
import logging
import os

def setup_logging():
    log_level = os.getenv('LOG_LEVEL', 'INFO')
    log_file = os.getenv('LOG_FILE', '/app/logs/app.log')
    
    logging.basicConfig(
        level=getattr(logging, log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
```

## 🚀 Deployment Scripts

### deploy.sh

```bash
#!/bin/bash

# Production deployment script
set -e

echo "🚀 Deploying TMS Backend with Jira Integration..."

# Pull latest code
git pull origin main

# Build and start containers
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build

# Run migrations
docker exec -it tms-backend flask db upgrade

# Health check
sleep 10
curl -f http://localhost/health || exit 1

echo "✅ Deployment completed successfully!"
```

### backup.sh

```bash
#!/bin/bash

# Database backup script
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"

# Create backup
docker exec tms-mysql mysqldump -u root -p$MYSQL_ROOT_PASSWORD tms_db > $BACKUP_DIR/tms_backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/tms_backup_$DATE.sql

# Keep only last 7 days
find $BACKUP_DIR -name "tms_backup_*.sql.gz" -mtime +7 -delete

echo "✅ Backup completed: tms_backup_$DATE.sql.gz"
```
