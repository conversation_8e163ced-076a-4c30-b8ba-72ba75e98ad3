# Activity Logging System

This document describes the activity logging system in the TMS Backend.

## Overview

The activity logging system records user actions across the application, providing a complete audit trail. 
It captures what actions were performed, by whom, when, and with what context.

## ActivityLog Model

The `ActivityLog` model in `app/models/system.py` contains the following fields:

- `id`: Primary key
- `user_id`: ID of the user who performed the action
- `project_id`: ID of the related project (optional)
- `task_id`: ID of the related task (optional)
- `action`: Type of action performed (e.g., "create_task", "login")
- `entity_type`: Type of entity affected (e.g., "task", "project", "user")
- `entity_id`: ID of the affected entity
- `description`: Human-readable description of the activity
- `details`: Additional structured data as JSON
- `ip_address`: IP address of the user
- `user_agent`: User's browser/client information
- `created_at`: Timestamp of the activity

## API Endpoints

The activity logs can be accessed through the following endpoints:

- `GET /history/user/<user_id>`: Get activity logs for a specific user
  - Query parameters: `page` (default: 1), `per_page` (default: 20)
  - Only allows users to see their own logs unless they're admins

- `GET /history/project/<project_id>`: Get activity logs for a specific project
  - Query parameters: `page` (default: 1), `per_page` (default: 20)
  - Requires project access permissions

- `GET /history/task/<task_id>`: Get activity logs for a specific task
  - Query parameters: `page` (default: 1), `per_page` (default: 20)
  - Requires task access permissions

- `GET /history/recent`: Get recent activity logs across the system
  - Query parameters: `limit` (default: 20)
  - Requires admin permissions

## Logging Activity in Your Code

There are two ways to log activities:

### 1. Direct Function Call

```python
from app.history.utils import log_activity

log_activity(
    user_id=1,
    action="create_task",
    entity_type="task",
    description="Created task My Task",
    entity_id=123,
    project_id=45,
    task_id=123,
    details={"priority": "high", "due_date": "2023-12-31"}
)
```

### 2. Using the Decorator

The decorator approach is useful for routes and automatically logs after the function returns:

```python
from flask import g
from app.history.utils import with_activity_log

@app.route("/some/endpoint", methods=["POST"])
@jwt_required()
@with_activity_log(
    action="create_something",
    entity_type="something",
    description_template="Created {name}"
)
def create_something():
    # Your function logic
    
    # Set up activity log data
    g.log_user_id = current_user_id
    g.log_data = {
        "name": "Something name",
        "entity_id": something_id,
        "project_id": project_id
    }
    
    return jsonify(result), 200
```

## Pagination

All list endpoints support pagination with the following response format:

```json
{
  "success": true,
  "message": "Activity logs retrieved successfully",
  "data": {
    "logs": [
      {
        "id": 1,
        "user_id": 1,
        "project_id": 2,
        "action": "create_task",
        "entity_type": "task",
        "entity_id": 3,
        "description": "Created task My Task",
        "details": { "priority": "high" },
        "created_at": "2023-01-01T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total_items": 100,
      "total_pages": 5
    }
  }
}
```
