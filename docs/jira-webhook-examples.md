# Jira Webhook Examples

## 📋 <PERSON>ục lục

1. [Webhook Payloads](#webhook-payloads)
2. [Event Types](#event-types)
3. [Testing Examples](#testing-examples)
4. [Response Formats](#response-formats)

## 🔗 Webhook Payloads

### Issue Created Event

```json
{
  "timestamp": *************,
  "webhookEvent": "jira:issue_created",
  "issue_event_type_name": "issue_created",
  "user": {
    "self": "https://your-domain.atlassian.net/rest/api/2/user?accountId=123",
    "accountId": "*********",
    "displayName": "<PERSON>",
    "active": true
  },
  "issue": {
    "id": "10001",
    "key": "PROJ-1",
    "self": "https://your-domain.atlassian.net/rest/api/2/issue/10001",
    "fields": {
      "summary": "New feature request",
      "description": "Implement new user authentication system",
      "status": {
        "id": "1",
        "name": "<PERSON> <PERSON>",
        "statusCategory": {
          "id": 2,
          "key": "new",
          "colorName": "blue-gray"
        }
      },
      "priority": {
        "id": "3",
        "name": "Medium"
      },
      "assignee": {
        "accountId": "*********",
        "displayName": "<PERSON> Doe"
      },
      "reporter": {
        "accountId": "*********",
        "displayName": "John Doe"
      },
      "created": "2024-01-01T10:00:00.000+0000",
      "updated": "2024-01-01T10:00:00.000+0000",
      "project": {
        "id": "10000",
        "key": "PROJ",
        "name": "Sample Project"
      },
      "issuetype": {
        "id": "1",
        "name": "Task"
      }
    }
  }
}
```

### Issue Updated Event

```json
{
  "timestamp": *************,
  "webhookEvent": "jira:issue_updated",
  "issue_event_type_name": "issue_updated",
  "user": {
    "accountId": "*********",
    "displayName": "John Doe"
  },
  "issue": {
    "id": "10001",
    "key": "PROJ-1",
    "fields": {
      "summary": "Updated feature request",
      "description": "Implement new user authentication system with 2FA",
      "status": {
        "id": "3",
        "name": "In Progress",
        "statusCategory": {
          "id": 4,
          "key": "indeterminate",
          "colorName": "yellow"
        }
      },
      "priority": {
        "id": "2",
        "name": "High"
      },
      "updated": "2024-01-01T10:10:00.000+0000"
    }
  },
  "changelog": {
    "id": "10020",
    "items": [
      {
        "field": "status",
        "fieldtype": "jira",
        "from": "1",
        "fromString": "To Do",
        "to": "3",
        "toString": "In Progress"
      },
      {
        "field": "priority",
        "fieldtype": "jira",
        "from": "3",
        "fromString": "Medium",
        "to": "2",
        "toString": "High"
      }
    ]
  }
}
```

### Issue Deleted Event

```json
{
  "timestamp": *************,
  "webhookEvent": "jira:issue_deleted",
  "issue_event_type_name": "issue_deleted",
  "user": {
    "accountId": "*********",
    "displayName": "John Doe"
  },
  "issue": {
    "id": "10001",
    "key": "PROJ-1",
    "fields": {
      "summary": "Deleted feature request",
      "project": {
        "id": "10000",
        "key": "PROJ",
        "name": "Sample Project"
      }
    }
  }
}
```

## 📝 Event Types

### Supported Events

| Event Type | Description | TMS Action |
|------------|-------------|------------|
| `jira:issue_created` | New issue created | Create new task in TMS |
| `jira:issue_updated` | Issue fields updated | Update corresponding task |
| `jira:issue_deleted` | Issue deleted | Soft delete task in TMS |

### Event Processing Flow

```
Jira Event → Webhook → TMS Backend → Database Update → Telegram Notification
```

## 🧪 Testing Examples

### Test Issue Created

```bash
curl -X POST http://localhost:8084/webhooks/jira/webhook?user_id=1 \
  -H "Content-Type: application/json" \
  -d '{
    "webhookEvent": "jira:issue_created",
    "issue": {
      "id": "10001",
      "key": "TEST-1",
      "fields": {
        "summary": "Test Issue from Webhook",
        "description": "This is a test issue created via webhook",
        "status": {
          "name": "To Do"
        },
        "priority": {
          "name": "Medium"
        },
        "project": {
          "key": "TEST"
        }
      }
    }
  }'
```

### Test Issue Updated

```bash
curl -X POST http://localhost:8084/webhooks/jira/webhook?user_id=1 \
  -H "Content-Type: application/json" \
  -d '{
    "webhookEvent": "jira:issue_updated",
    "issue": {
      "id": "10001",
      "key": "TEST-1",
      "fields": {
        "summary": "Updated Test Issue",
        "description": "This issue has been updated",
        "status": {
          "name": "In Progress"
        },
        "priority": {
          "name": "High"
        }
      }
    }
  }'
```

### Test Issue Deleted

```bash
curl -X POST http://localhost:8084/webhooks/jira/webhook?user_id=1 \
  -H "Content-Type: application/json" \
  -d '{
    "webhookEvent": "jira:issue_deleted",
    "issue": {
      "id": "10001",
      "key": "TEST-1"
    }
  }'
```

## 📤 Response Formats

### Success Response

```json
{
  "success": true,
  "message": "Issue created event processed"
}
```

### Error Response

```json
{
  "success": false,
  "message": "Missing required webhook data"
}
```

### Validation Error

```json
{
  "success": false,
  "message": "User ID is required to process webhook"
}
```

## 🔧 Webhook Configuration in Jira

### Manual Setup

1. Go to **Jira Settings** → **System** → **Webhooks**
2. Click **"Create a webhook"**
3. Fill in details:
   - **Name**: TMS Integration Webhook
   - **URL**: `https://your-domain.com/webhooks/jira/webhook?user_id=USER_ID`
   - **Description**: Webhook for TMS integration
   - **Issue**: Check all (created, updated, deleted)

### Webhook URL Format

```
https://your-domain.com/webhooks/jira/webhook?user_id={USER_ID}
```

**Parameters**:
- `user_id`: ID của user trong TMS database

### Security Considerations

1. **HTTPS Only**: Luôn sử dụng HTTPS cho production
2. **User Validation**: Validate user_id exists trong database
3. **Rate Limiting**: Implement rate limiting cho webhook endpoint
4. **Logging**: Log tất cả webhook events để debug

## 📊 Monitoring

### Webhook Health Check

```bash
# Check webhook endpoint
curl -X POST http://localhost:8084/webhooks/jira/webhook?user_id=1 \
  -H "Content-Type: application/json" \
  -d '{"test": true}'
```

### Database Queries

```sql
-- Check webhook events
SELECT * FROM webhook_events WHERE platform = 'jira' ORDER BY created_at DESC LIMIT 10;

-- Check external task mappings
SELECT * FROM external_task_mapping WHERE external_platform = 'jira';

-- Check integration status
SELECT * FROM integration WHERE platform = 'jira' AND is_active = 1;
```

## 🚨 Error Handling

### Common Webhook Errors

1. **Missing user_id**: Return 400 Bad Request
2. **Invalid user_id**: Return 404 Not Found
3. **Missing webhook data**: Return 400 Bad Request
4. **Database error**: Return 500 Internal Server Error
5. **Integration not found**: Return 404 Not Found

### Retry Logic

Jira sẽ retry webhook nếu:
- Response status >= 400
- Timeout (30 seconds)
- Network error

**Retry schedule**: 1min, 5min, 15min, 30min, 1hr, 3hr, 6hr, 12hr, 24hr

## 📝 Best Practices

1. **Idempotency**: Xử lý duplicate events gracefully
2. **Fast Response**: Trả về response nhanh (< 5 seconds)
3. **Async Processing**: Xử lý heavy operations async
4. **Error Logging**: Log chi tiết errors để debug
5. **Monitoring**: Monitor webhook success rate
