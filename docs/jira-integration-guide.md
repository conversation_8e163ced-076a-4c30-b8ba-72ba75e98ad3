# Hướng dẫn tích hợp Jira từ A đến Z

## 📋 <PERSON><PERSON> l<PERSON>

1. [Tổng quan](#tổng-quan)
2. [<PERSON><PERSON>u hình Jira App](#cấu-hình-jira-app)
3. [C<PERSON><PERSON> hình Backend](#cấu-hình-backend)
4. [Chạy Migration](#chạy-migration)
5. [OAuth Authentication Flow](#oauth-authentication-flow)
6. [Sử dụng API](#sử-dụng-api)
7. [Webhook Configuration](#webhook-configuration)
8. [Testing](#testing)
9. [Troubleshooting](#troubleshooting)

## 🎯 Tổng quan

TMS Backend hỗ trợ tích hợp với Jira thông qua:
- **OAuth 2.0 Authentication**: Xác thực người dùng
- **REST API Integration**: Đồng bộ projects và issues
- **Webhook Events**: Cập nhật real-time từ Jira
- **Bidirectional Sync**: <PERSON><PERSON><PERSON> bộ 2 chiều giữa TMS và Jira

### <PERSON>ến trúc tích hợp

```
TMS Backend ←→ Jira Cloud
     ↑              ↓
   OAuth 2.0    Webhooks
     ↑              ↓
  REST API    Real-time Events
```

## 🔧 Cấu hình Jira App

### Bước 1: Tạo Atlassian App

1. Truy cập [Atlassian Developer Console](https://developer.atlassian.com/console/myapps/)
2. Click **"Create"** → **"OAuth 2.0 integration"**
3. Điền thông tin app:
   - **App name**: TMS Integration
   - **Description**: Task Management System Integration

### Bước 2: Cấu hình OAuth 2.0

1. Trong app settings, chọn **"Authorization"**
2. Thêm **Callback URL**:
   ```
   http://localhost:8084/auth/jira/callback
   ```
   (Thay đổi domain cho production)

3. Cấu hình **Scopes**:
   - `read:jira-work` - Đọc dữ liệu Jira
   - `write:jira-work` - Ghi dữ liệu Jira
   - `read:jira-user` - Đọc thông tin user
   - `offline_access` - Refresh token

### Bước 3: Lấy credentials

1. Copy **Client ID** và **Client Secret**
2. Lưu lại để cấu hình backend

## ⚙️ Cấu hình Backend

### Bước 1: Environment Variables

Thêm vào file `.env`:

```env
# Jira OAuth Configuration
JIRA_CLIENT_ID=your_client_id_here
JIRA_CLIENT_SECRET=your_client_secret_here
JIRA_REDIRECT_URI=http://localhost:8084/auth/jira/callback

# Database (nếu chưa có)
DATABASE_URL=mysql+pymysql://user:password@localhost/tms_db
```

### Bước 2: Docker Environment

Thêm vào `docker-compose.yml`:

```yaml
environment:
  - JIRA_CLIENT_ID=${JIRA_CLIENT_ID}
  - JIRA_CLIENT_SECRET=${JIRA_CLIENT_SECRET}
  - JIRA_REDIRECT_URI=${JIRA_REDIRECT_URI}
```

## 🗄️ Chạy Migration

### Trong Docker

```bash
# Chạy migration để tạo bảng Jira
docker exec -it tms-backend flask db upgrade

# Kiểm tra migration status
docker exec -it tms-backend flask db current
```

### Migration sẽ tạo:

1. **Jira fields trong User table**:
   - `jira_account_id`
   - `jira_display_name`
   - `jira_avatar_url`

2. **JiraWebhook table**:
   - Lưu trữ webhook configurations
   - Quản lý webhook events

## 🔐 OAuth Authentication Flow

### Bước 1: Khởi tạo OAuth

**Frontend request**:
```javascript
// Redirect user to Jira OAuth
window.location.href = '/auth/jira/login';
```

**Backend endpoint**:
```
GET /auth/jira/login
```

### Bước 2: Xử lý Callback

Jira sẽ redirect về:
```
GET /auth/jira/callback?code=AUTH_CODE&state=STATE
```

Backend sẽ:
1. Exchange code for access token
2. Lấy thông tin user từ Jira
3. Tạo/cập nhật user trong database
4. Tạo Integration record
5. Trả về JWT token

### Bước 3: Kiểm tra kết nối

```bash
curl -X GET http://localhost:8084/webhooks/jira/status \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Response:
```json
{
  "success": true,
  "message": "Jira integration status retrieved",
  "data": {
    "connected": true,
    "platform": "jira",
    "cloud_id": "your-cloud-id",
    "platform_user_id": "user-account-id"
  }
}
```

## 🚀 Sử dụng API

### 1. Lấy danh sách Projects

```bash
curl -X GET http://localhost:8084/webhooks/jira/projects \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 2. Đồng bộ Project từ Jira

```bash
curl -X POST http://localhost:8084/webhooks/jira/projects/PROJ-KEY/sync \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. Lấy Issues từ Project

```bash
curl -X GET http://localhost:8084/webhooks/jira/projects/PROJ-KEY/issues \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 4. Đồng bộ Issue thành Task

```bash
curl -X POST http://localhost:8084/webhooks/jira/issues/PROJ-123/sync \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"project_id": 1}'
```

### 5. Đồng bộ Task thành Issue

```bash
curl -X POST http://localhost:8084/webhooks/jira/tasks/1/sync \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"jira_project_key": "PROJ"}'
```

### 6. Tìm kiếm Issues với JQL

```bash
curl -X GET "http://localhost:8084/webhooks/jira/search?jql=project=PROJ+AND+status=Open" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 7. Thêm Comment vào Issue

```bash
curl -X POST http://localhost:8084/webhooks/jira/issues/PROJ-123/comments \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"comment": "Comment from TMS"}'
```

## 🔗 Webhook Configuration

### Bước 1: Tạo Webhook trong Jira

1. Vào Jira Settings → System → Webhooks
2. Click **"Create a webhook"**
3. Cấu hình:
   - **Name**: TMS Webhook
   - **URL**: `http://your-domain.com/webhooks/jira/webhook?user_id=USER_ID`
   - **Events**: 
     - Issue created
     - Issue updated
     - Issue deleted

### Bước 2: Test Webhook

```bash
curl -X POST http://localhost:8084/webhooks/jira/webhook?user_id=1 \
  -H "Content-Type: application/json" \
  -d '{
    "webhookEvent": "jira:issue_created",
    "issue": {
      "id": "123",
      "key": "PROJ-1",
      "fields": {
        "summary": "Test Issue",
        "description": "This is a test issue",
        "status": { "name": "To Do" },
        "priority": { "name": "Medium" }
      }
    }
  }'
```

### Webhook Events được hỗ trợ:

- `jira:issue_created` - Issue mới được tạo
- `jira:issue_updated` - Issue được cập nhật
- `jira:issue_deleted` - Issue bị xóa

## 🧪 Testing

### 1. Test OAuth Flow

```bash
# 1. Khởi tạo OAuth
curl -X GET http://localhost:8084/auth/jira/login

# 2. Sau khi OAuth thành công, test status
curl -X GET http://localhost:8084/webhooks/jira/status \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 2. Test Project Sync

```bash
# 1. Lấy danh sách projects
curl -X GET http://localhost:8084/webhooks/jira/projects \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 2. Sync một project
curl -X POST http://localhost:8084/webhooks/jira/projects/YOUR-PROJECT-KEY/sync \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. Test Issue Sync

```bash
# 1. Lấy issues từ project
curl -X GET http://localhost:8084/webhooks/jira/projects/YOUR-PROJECT-KEY/issues \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 2. Sync một issue
curl -X POST http://localhost:8084/webhooks/jira/issues/YOUR-ISSUE-KEY/sync \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"project_id": YOUR_PROJECT_ID}'
```

### 4. Test Webhook

```bash
# Test webhook endpoint
curl -X POST http://localhost:8084/webhooks/jira/webhook?user_id=YOUR_USER_ID \
  -H "Content-Type: application/json" \
  -d @test-webhook-payload.json
```

## 🔍 Troubleshooting

### Lỗi thường gặp

#### 1. "No active Jira integration found"

**Nguyên nhân**: User chưa kết nối Jira hoặc integration bị vô hiệu hóa

**Giải pháp**:
```sql
-- Kiểm tra integration
SELECT * FROM integration WHERE user_id = YOUR_USER_ID AND platform = 'jira';

-- Kích hoạt lại nếu cần
UPDATE integration SET is_active = 1 WHERE user_id = YOUR_USER_ID AND platform = 'jira';
```

#### 2. "Invalid or expired token"

**Nguyên nhân**: Access token hết hạn

**Giải pháp**:
- Thực hiện OAuth flow lại
- Hoặc implement refresh token logic

#### 3. "Webhook validation failed"

**Nguyên nhân**: Webhook URL không đúng hoặc user_id không hợp lệ

**Giải pháp**:
- Kiểm tra URL webhook: `/webhooks/jira/webhook?user_id=VALID_USER_ID`
- Đảm bảo user_id tồn tại trong database

#### 4. "Project not found or not synced with Jira"

**Nguyên nhân**: Project chưa được sync từ Jira

**Giải pháp**:
```bash
# Sync project trước
curl -X POST http://localhost:8084/webhooks/jira/projects/PROJECT-KEY/sync \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Debug Commands

```bash
# Kiểm tra logs
docker logs tms-backend

# Kiểm tra database
docker exec -it tms-mysql mysql -u root -p tms_db

# Kiểm tra migration
docker exec -it tms-backend flask db current
```

### Environment Variables Debug

```bash
# Kiểm tra env vars trong container
docker exec -it tms-backend env | grep JIRA
```

## 📚 API Reference

### Authentication Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/auth/jira/login` | Khởi tạo OAuth flow |
| GET | `/auth/jira/callback` | OAuth callback handler |

### Jira Integration Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/webhooks/jira/status` | Kiểm tra trạng thái kết nối |
| POST | `/webhooks/jira/webhook` | Webhook event handler |
| GET | `/webhooks/jira/projects` | Lấy danh sách projects |
| POST | `/webhooks/jira/projects/<key>/sync` | Sync project |
| GET | `/webhooks/jira/projects/<key>/issues` | Lấy issues của project |
| POST | `/webhooks/jira/issues/<key>/sync` | Sync issue thành task |
| POST | `/webhooks/jira/tasks/<id>/sync` | Sync task thành issue |
| GET | `/webhooks/jira/issues/<key>` | Lấy chi tiết issue |
| POST | `/webhooks/jira/issues/<key>/comments` | Thêm comment |
| GET | `/webhooks/jira/search` | Tìm kiếm với JQL |

## 🎉 Hoàn thành!

Bạn đã hoàn thành việc tích hợp Jira với TMS Backend. Hệ thống hiện có thể:

✅ Xác thực người dùng qua Jira OAuth  
✅ Đồng bộ projects và issues  
✅ Xử lý webhook events real-time  
✅ Hỗ trợ tìm kiếm và comment  
✅ Đồng bộ 2 chiều giữa TMS và Jira  

Để biết thêm chi tiết, tham khảo:
- [API Documentation](./api-documentation.md)
- [Database Schema](./database-v2.md)
- [Testing Guide](./testing_guide.md)
- [Jira Webhook Examples](./jira-webhook-examples.md)
- [Jira Configuration Templates](./jira-config-templates.md)
