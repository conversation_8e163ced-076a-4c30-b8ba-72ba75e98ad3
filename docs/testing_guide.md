# Testing Guide

## Table of Contents
- [Setup Environment](#setup-environment)
- [Authentication Testing](#authentication-testing)
- [Project Testing](#project-testing)
- [Task Testing](#task-testing)
- [Jira Integration Testing](#jira-integration-testing)
- [Telegram Integration Testing](#telegram-integration-testing)
- [Webhook Testing](#webhook-testing)
- [Statistics Testing](#statistics-testing)
- [Member Management Testing](#member-management-testing)

## Setup Environment

1. Install dependencies:
```bash
pip install -r docker/requirements.txt
```

2. Run the application:
```bash
python app/__init__.py
```

3. Default test server URL: `http://localhost:5000`

## Authentication Testing

### Register
```bash
curl -X POST http://localhost:5000/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "email": "<EMAIL>", "password": "password123"}'
```

### Login
```bash
curl -X POST http://localhost:5000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'
```

## Project Testing

### Create Project
```bash
curl -X POST http://localhost:5000/projects \
  -H "Authorization: Bearer <your_jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{"name": "Test Project", "description": "This is a test project"}'
```

### List Projects
```bash
curl -X GET http://localhost:5000/projects \
  -H "Authorization: Bearer <your_jwt_token>"
```

### Update Project
```bash
curl -X PUT http://localhost:5000/projects/<project_id> \
  -H "Authorization: Bearer <your_jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{"name": "Updated Project Name"}'
```

## Task Testing

### Create Task
```bash
curl -X POST http://localhost:5000/tasks/create_task_view \
  -H "Authorization: Bearer <your_jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Task",
    "description": "This is a test task",
    "status": "todo",
    "priority": "medium",
    "project_id": <project_id>
  }'
```

### List Tasks
```bash
curl -X GET http://localhost:5000/tasks/list_tasks \
  -H "Authorization: Bearer <your_jwt_token>"
```

## Jira Integration Testing

### Get Jira Projects
```bash
curl -X GET http://localhost:5000/webhooks/jira/projects \
  -H "Authorization: Bearer <your_jwt_token>"
```

### Sync Jira Project
```bash
curl -X POST http://localhost:5000/webhooks/jira/projects/PROJ-123/sync \
  -H "Authorization: Bearer <your_jwt_token>"
```

### Test Jira Webhook
```bash
curl -X POST http://localhost:5000/webhooks/jira/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "webhookEvent": "jira:issue_created",
    "issue": {
      "id": "123",
      "key": "PROJ-1",
      "fields": {
        "summary": "Test Issue",
        "description": "This is a test issue",
        "status": { "name": "To Do" },
        "priority": { "name": "Medium" }
      }
    }
  }'
```

## Telegram Integration Testing

### Link Telegram Account
```bash
curl -X POST http://localhost:5000/telegram/link \
  -H "Authorization: Bearer <your_jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "telegram_user_id": "*********",
    "chat_id": "*********",
    "telegram_data": {
      "username": "testuser",
      "first_name": "Test",
      "last_name": "User"
    }
  }'
```

### Test Telegram Webhook
```bash
curl -X POST http://localhost:5000/telegram/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "update_id": *********,
    "message": {
      "chat": { "id": ********* },
      "text": "/start"
    }
  }'
```

## Webhook Testing

### Test GitHub Webhook
```bash
curl -X POST http://localhost:5000/webhooks/create \
  -H "Authorization: Bearer <your_jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{"repository_urls": ["https://github.com/your/repo"]}'
```

## Statistics Testing

### Get Project Statistics
```bash
curl -X GET http://localhost:5000/statistics/project/<project_id> \
  -H "Authorization: Bearer <your_jwt_token>"
```

### Get User Reliability
```bash
curl -X GET http://localhost:5000/statistics/reliability \
  -H "Authorization: Bearer <your_jwt_token>" \
  -d '{"period": 30}'
```

## Member Management Testing

### Add Member to Project
```bash
curl -X POST http://localhost:5000/members/project/<project_id>/add \
  -H "Authorization: Bearer <your_jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{"user_id": <user_id>, "role": "member"}'
```

### Remove Member
```bash
curl -X DELETE http://localhost:5000/members/project/<project_id>/remove/<user_id> \
  -H "Authorization: Bearer <your_jwt_token>"
```

## Test Response Validation

### Success Response
```json
{
  "success": true,
  "message": "string",
  "data": {
    // Response data
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "string",
  "code": integer,
  "details": {
    // Error details
  }
}
```

## Common Error Codes
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 500: Internal Server Error

## Testing Tips
1. Always include JWT token in Authorization header for protected endpoints
2. Check response status codes and messages
3. Validate response data structure
4. Test edge cases and error scenarios
5. Use environment variables for configuration
6. Test webhook handlers with sample events
7. Verify database operations and transactions
8. Test integration points with external services (Jira, GitHub, Telegram)
