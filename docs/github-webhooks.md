# GitHub Webhooks Feature

Tính năng GitHub Webhooks cho phép hệ thống TMS tự động đồng bộ hóa thông tin từ GitHub khi có thay đổi trong repositories và issues.

## Tổng quan

Chức năng webhook GitHub bao gồm:

1. **Tạo Webhooks** - Tạo webhooks cho multiple repositories
2. **Nhận Events** - Xử lý webhook events từ GitHub
3. **Auto Sync** - Tự động đồng bộ projects và tasks khi có thay đổi
4. **Event Logging** - Ghi log tất cả webhook events
5. **Quản lý Webhooks** - Liệ<PERSON> kê, xóa webhooks

## Yêu cầu

- User phải có GitHub OAuth integration được kích hoạt
- Access token GitHub hợp lệ với quyền `admin:repo_hook` để tạo webhooks
- Repository phải được sync từ GitHub trước khi tạo webhook

## API Endpoints

### 1. Tạo Webhooks cho Multiple Repositories

**POST** `/webhooks/create`

Tạo webhooks cho nhiều repositories cùng lúc.

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "repository_urls": [
    "https://github.com/owner/repo1",
    "https://github.com/owner/repo2"
  ],
  "webhook_url": "https://your-domain.com/webhooks/github" // optional
}
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully created 2 webhook(s)",
  "data": {
    "created_webhooks": [
      {
        "webhook_id": 1,
        "github_webhook_id": "12345678",
        "repository": "owner/repo1",
        "events": ["push", "issues", "pull_request", "issue_comment", "pull_request_review", "repository"]
      }
    ],
    "errors": []
  },
  "code": 200
}
```

### 2. Liệt kê Webhooks của User

**GET** `/webhooks/list`

Lấy danh sách tất cả webhooks active của user.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "message": "Found 2 webhook(s)",
  "data": {
    "webhooks": [
      {
        "id": 1,
        "user_id": 123,
        "project_id": 456,
        "github_webhook_id": "12345678",
        "github_repo_full_name": "owner/repository",
        "webhook_url": "https://your-domain.com/webhooks/github",
        "events": ["push", "issues", "pull_request"],
        "is_active": true,
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z",
        "last_ping": "2024-01-15T12:45:00Z"
      }
    ]
  },
  "code": 200
}
```

### 3. Xóa Webhook

**DELETE** `/webhooks/{webhook_id}`

Xóa một webhook cụ thể.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "message": "Webhook deleted successfully for owner/repository",
  "data": null,
  "code": 200
}
```

### 4. Lấy Events của Webhook

**GET** `/webhooks/events/{webhook_id}?page=1&per_page=20`

Lấy danh sách events của một webhook với pagination.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "message": "Found 10 event(s) for webhook 1",
  "data": {
    "events": [
      {
        "id": 1,
        "webhook_id": 1,
        "github_delivery_id": "12345678-1234-1234-1234-123456789012",
        "event_type": "push",
        "action": null,
        "processed": true,
        "processed_at": "2024-01-15T10:35:00Z",
        "error_message": null,
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total": 50,
      "pages": 3,
      "has_next": true,
      "has_prev": false
    }
  },
  "code": 200
}
```

### 5. GitHub Webhook Endpoint

**POST** `/webhooks/github`

Endpoint nhận webhook events từ GitHub (được gọi bởi GitHub, không phải users).

**Headers:**
```
X-GitHub-Event: push
X-GitHub-Delivery: 12345678-1234-1234-1234-123456789012
X-Hub-Signature-256: sha256=...
Content-Type: application/json
```

**Request Body:** GitHub webhook payload (khác nhau tùy theo event type)

## Webhook Events được hỗ trợ

### 1. Push Events
- **Trigger:** Khi có push commits vào repository
- **Action:** Đồng bộ thông tin repository (project info)

### 2. Issues Events
- **Trigger:** Khi có thay đổi về issues (opened, closed, edited, etc.)
- **Action:** Đồng bộ tất cả issues thành tasks

### 3. Issue Comment Events
- **Trigger:** Khi có comment mới trên issue
- **Action:** Ghi log event (có thể mở rộng để sync comments)

### 4. Pull Request Events
- **Trigger:** Khi có thay đổi về pull requests
- **Action:** Ghi log event (có thể mở rộng để xử lý PRs)

### 5. Repository Events
- **Trigger:** Khi repository bị deleted, archived, renamed, transferred
- **Action:** 
  - `deleted/archived`: Deactivate webhook
  - `renamed/transferred`: Update project info

## Security

### Webhook Signature Verification
- Tất cả webhook requests được verify bằng HMAC-SHA256 signature
- Secret được generate tự động khi tạo webhook
- Invalid signatures sẽ bị reject với status 401

### Authentication
- Webhook endpoints không yêu cầu JWT authentication (được gọi bởi GitHub)
- Management endpoints yêu cầu JWT authentication


## Workflow

### 1. Setup Webhooks
1. User sync repositories từ GitHub
2. User gọi API `/webhooks/create` với list repository URLs
3. System tạo webhooks trên GitHub cho từng repository
4. Webhooks được lưu vào database với secret keys

### 2. Receive Events
1. GitHub gửi webhook events đến `/webhooks/github`
2. System verify signature và validate request
3. Event được log vào database
4. System xử lý event dựa trên event type

### 3. Auto Sync
1. **Push events**: Sync repository info
2. **Issues events**: Sync tất cả issues thành tasks
3. **Repository events**: Update project status

## Error Handling

- Invalid signatures: 401 Unauthorized
- Webhook not found: 404 Not Found
- Processing errors: Logged trong webhook_events table
- GitHub API errors: Returned trong response với error details

## Monitoring

- Tất cả webhook events được log với timestamps
- Processing status và error messages được track
- Last ping time được update cho mỗi webhook
- Pagination support cho event history

## Limitations

- Chỉ hỗ trợ public và private repositories mà user có admin access
- Webhook secret không thể thay đổi sau khi tạo
- Maximum 20 webhooks per user (có thể config)
- Event payload size limit: 25MB (GitHub limit)
