# API Documentation

## Table of Contents
- [Authentication](#authentication)
- [Projects](#projects)
- [Tasks](#tasks)
- [Jira Integration](#jira-integration)
- [Telegram Integration](#telegram-integration)
- [Webhooks](#webhooks)
- [Statistics](#statistics)
- [Members](#members)

## Authentication

### Login
- **Endpoint:** `/auth/login`
- **Method:** POST
- **Request Body:**
  ```json
  {
    "email": "string",
    "password": "string"
  }
  ```
- **Response:**
  ```json
  {
    "access_token": "string",
    "refresh_token": "string"
  }
  ```

### Register
- **Endpoint:** `/auth/register`
- **Method:** POST
- **Request Body:**
  ```json
  {
    "username": "string",
    "email": "string",
    "password": "string"
  }
  ```

### GitHub OAuth
- **Callback:** `/auth/github/callback`

### Jira OAuth
- **Callback:** `/auth/jira/callback`
- **Link Account:** `/auth/jira/link`

## Projects

### List Projects
- **Endpoint:** `/projects`
- **Method:** GET
- **Query Params:**
  - `include_deleted` (optional)

### Create Project
- **Endpoint:** `/projects`
- **Method:** POST
- **Request Body:**
  ```json
  {
    "name": "string",
    "description": "string"
  }
  ```

### Get Project
- **Endpoint:** `/projects/<project_id>`
- **Method:** GET

### Update Project
- **Endpoint:** `/projects/<project_id>`
- **Method:** PUT
- **Request Body:**
  ```json
  {
    "name": "string",
    "description": "string"
  }
  ```

### Delete Project
- **Endpoint:** `/projects/<project_id>`
- **Method:** DELETE
- **Query Params:**
  - `hard_delete` (optional)

### Restore Project
- **Endpoint:** `/projects/<project_id>/restore`
- **Method:** POST

### GitHub Integration
- **Get Repositories:** `/projects/github/repositories`
- **Sync Repositories:** `/projects/github/sync`
- **Complete Sync:** `/projects/<project_id>/github/sync-complete`

## Tasks

### List Tasks
- **Endpoint:** `/tasks/list_tasks`
- **Method:** GET
- **Query Params:**
  - `assignee_id` (optional)

### Create Task
- **Endpoint:** `/tasks/create_task_view`
- **Method:** POST
- **Request Body:**
  ```json
  {
    "title": "string",
    "description": "string",
    "status": "string",
    "priority": "string",
    "project_id": "integer"
  }
  ```

### Update Task
- **Endpoint:** `/tasks/update_task_view/<task_id>`
- **Method:** PUT
- **Request Body:**
  ```json
  {
    "title": "string",
    "status": "string",
    "priority": "string"
  }
  ```

### Delete Task
- **Endpoint:** `/tasks/delete_task_view/<task_id>`
- **Method:** DELETE

### GitHub Integration
- **Sync Issues:** `/tasks/github/sync/<project_id>`

## Jira Integration

### Status
- **Endpoint:** `/integrations/jira/status`
- **Method:** GET

### Projects
- **Endpoint:** `/integrations/jira/projects`
- **Method:** GET

### Sync Project
- **Endpoint:** `/integrations/jira/projects/<project_key>/sync`
- **Method:** POST

### Search Issues
- **Endpoint:** `/integrations/jira/search`
- **Method:** GET
- **Query Params:**
  - `jql` (Jira Query Language)
  - `startAt` (optional)
  - `maxResults` (optional)

### Sync Issue
- **Endpoint:** `/integrations/jira/issues/<issue_key>/sync`
- **Method:** POST
- **Request Body:**
  ```json
  {
    "project_id": "integer"
  }
  ```

### Sync Task to Jira
- **Endpoint:** `/integrations/jira/tasks/<task_id>/sync`
- **Method:** POST
- **Request Body:**
  ```json
  {
    "jira_project_key": "string"
  }
  ```

### Webhook
- **Endpoint:** `/integrations/jira/webhook`
- **Method:** POST
- **Request Body:**
  ```json
  {
    "webhookEvent": "string",
    "issue": {
      "id": "string",
      "key": "string",
      "fields": {
        "summary": "string",
        "description": "string",
        "status": { "name": "string" },
        "priority": { "name": "string" }
      }
    }
  }
  ```

## Telegram Integration

### Link Account
- **Endpoint:** `/telegram/link`
- **Method:** POST
- **Request Body:**
  ```json
  {
    "telegram_user_id": "string",
    "chat_id": "string",
    "telegram_data": {
      "username": "string",
      "first_name": "string",
      "last_name": "string"
    }
  }
  ```

### Webhook
- **Endpoint:** `/telegram/webhook`
- **Method:** POST
- **Request Body:**
  ```json
  {
    "update_id": "integer",
    "message": {
      "chat": { "id": "integer" },
      "text": "string"
    }
  }
  ```

### Setup Webhook
- **Endpoint:** `/telegram/webhook/setup`
- **Method:** POST
- **Request Body:**
  ```json
  {
    "webhook_url": "string",
    "secret_token": "string" (optional)
  }
  ```

### Bot Info
- **Endpoint:** `/telegram/bot/info`
- **Method:** GET

## Webhooks

### Create Webhooks
- **Endpoint:** `/webhooks/create`
- **Method:** POST
- **Request Body:**
  ```json
  {
    "repository_urls": ["string"]
  }
  ```

## Statistics

### Project Statistics
- **Endpoint:** `/statistics/project/<project_id>`
- **Method:** GET

### User Statistics
- **Endpoint:** `/statistics/user/<user_id>`
- **Method:** GET

### Dashboard Statistics
- **Endpoint:** `/statistics/dashboard`
- **Method:** GET

### Member Reliability
- **Endpoint:** `/statistics/reliability`
- **Method:** GET
- **Query Params:**
  - `period` (optional, default=30)

## Members

### Get Project Members
- **Endpoint:** `/members/project/<project_id>`
- **Method:** GET

### Add Member
- **Endpoint:** `/members/project/<project_id>/add`
- **Method:** POST
- **Request Body:**
  ```json
  {
    "user_id": "integer",
    "role": "string"
  }
  ```

### Update Member Role
- **Endpoint:** `/members/project/<project_id>/update/<user_id>`
- **Method:** PUT
- **Request Body:**
  ```json
  {
    "role": "string"
  }
  ```

### Remove Member
- **Endpoint:** `/members/project/<project_id>/remove/<user_id>`
- **Method:** DELETE

### Get User Projects
- **Endpoint:** `/members/user/<user_id>/projects`
- **Method:** GET
