# 🤖 TMS Telegram Bot - User Guide

## 📋 Overview

TMS Telegram Bot allows you to receive GitHub notifications directly in Telegram. You can link your TMS account, subscribe to notifications, and manage your preferences all through Telegram commands.

## 🚀 Getting Started

### Step 1: Admin Setup - Configure Webhook (One-time setup)

**⚠️ Important:** Before users can interact with the bot, administrators need to configure the webhook. Telegram requires HTTPS URLs for webhooks.

#### Option 1: Using Swagger API (Recommended for Production)

1. **Access Swagger UI**: Go to `http://your-domain.com/apidocs`
2. **Authenticate**: Use `/auth/login` to get JWT token
3. **Navigate to**: `Telegram Administration` section
4. **Use endpoint**: `POST /telegram/webhook/setup`

**Example request body:**
```json
{
  "webhook_url": "https://your-domain.com/telegram/webhook"
}
```

The API will automatically generate a secure secret token and return it for your environment configuration.

#### Option 2: Using ngrok for Development/Testing

1. **Install ngrok**: Download from https://ngrok.com/download

2. **Expose Flask app directly** (bypass nginx):
   ```bash
   ngrok http 5001
   ```

3. **Copy HTTPS URL** from ngrok output (e.g., `https://abc123.ngrok.io`)

4. **Setup webhook using curl**:
   ```bash
   curl -X POST "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/setWebhook" \
        -H "Content-Type: application/json" \
        -d '{
          "url": "https://abc123.ngrok.io/telegram/webhook",
          "allowed_updates": ["message"],
          "drop_pending_updates": true
        }'
   ```

   **Replace `<YOUR_BOT_TOKEN>`** with your actual bot token from environment variables.

#### Option 3: Production Setup with Domain

For production environments with a proper domain:

```bash
curl -X POST "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/setWebhook" \
     -H "Content-Type: application/json" \
     -d '{
       "url": "https://your-production-domain.com/telegram/webhook",
       "secret_token": "your_secure_secret_token",
       "allowed_updates": ["message"],
       "drop_pending_updates": true,
       "max_connections": 40
     }'
```

#### Verify Webhook Configuration

**Check webhook status:**
```bash
curl "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getWebhookInfo"
```

**Expected successful response:**
```json
{
  "ok": true,
  "result": {
    "url": "https://your-domain.com/telegram/webhook",
    "has_custom_certificate": false,
    "pending_update_count": 0,
    "max_connections": 40,
    "allowed_updates": ["message"]
  }
}
```

**If you see errors:**
- `"Connection refused"`: Check if your server is accessible from internet
- `"SSL error"`: Ensure your URL uses HTTPS
- `"Invalid URL"`: Verify the webhook URL format

#### Troubleshooting Webhook Issues

1. **Test webhook endpoint manually:**
   ```bash
   curl -X POST "https://your-domain.com/telegram/webhook" \
        -H "Content-Type: application/json" \
        -d '{"test": "webhook"}'
   ```

2. **Check server logs** for incoming requests

3. **Verify port forwarding:**
   - For ngrok: Use port 5001 (Flask app) instead of 8084 (nginx)
   - For production: Ensure proper reverse proxy configuration

4. **Delete and recreate webhook** if needed:
   ```bash
   # Delete webhook
   curl -X POST "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/deleteWebhook" \
        -d '{"drop_pending_updates": true}'

   # Setup new webhook
   curl -X POST "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/setWebhook" \
        -H "Content-Type: application/json" \
        -d '{"url": "https://your-new-url.com/telegram/webhook"}'
   ```

### Step 2: User Setup - Start the Bot

1. **Find the TMS bot** on Telegram (ask your admin for the bot username)
2. **Send `/start`** to begin interaction with the bot

### Step 3: Link Your TMS Account

Link your TMS account using your email and password:

```
/link <EMAIL> your_password
```

**Example:**
```
/link <EMAIL> mypassword123
```

**✅ Success Response:**
```
🎉 Account Linked Successfully!

👤 TMS User: john.doe
📧 Email: <EMAIL>

✅ Your Telegram account is now linked to TMS!

Next steps:
• Use /subscribe github_notifications to get GitHub notifications
• Use /status to check your subscription status
• Use /help to see all available commands
```

**❌ Error Responses:**
- Invalid credentials: Check your email and password
- Account already linked: Use `/unlink` first, then link again
- OAuth account: Use web interface to link OAuth accounts

## 📱 Available Commands

### Account Management

| Command | Description | Example |
|---------|-------------|---------|
| `/start` | Start the bot and get welcome message | `/start` |
| `/link <email> <password>` | Link your TMS account | `/link <EMAIL> pass123` |
| `/unlink` | Unlink your TMS account | `/unlink` |
| `/status` | Check account and subscription status | `/status` |

### Notifications

| Command | Description | Example |
|---------|-------------|---------|
| `/subscribe github_notifications` | Subscribe to GitHub notifications | `/subscribe github_notifications` |
| `/unsubscribe github_notifications` | Unsubscribe from GitHub notifications | `/unsubscribe github_notifications` |

### Help

| Command | Description | Example |
|---------|-------------|---------|
| `/help` | Show all available commands | `/help` |

## 🔔 Notification Types

### GitHub Notifications (`github_notifications`)

When subscribed, you'll receive notifications for:

- **🚀 Push Events**: New commits pushed to repositories
- **🆕 Issues**: New issues opened, closed, or updated
- **💬 Issue Comments**: New comments on issues
- **🔀 Pull Requests**: PRs opened, closed, merged, or updated
- **👀 PR Reviews**: New reviews on pull requests
- **📁 Repository Events**: Repository created, deleted, renamed, etc.

**Example Notification:**
```
🚀 New Push

📁 Repository: company/awesome-project
🌿 Branch: main
👤 Pusher: john.doe
📝 Commits: 3

Latest commit:
• Fix user authentication bug
```

## 📊 Status Command

Use `/status` to check your account and subscription status:

```
📊 Your Account Status

✅ Account: Linked to TMS
👤 User: john.doe
📧 Email: <EMAIL>
🕒 Linked: 2024-01-15 10:30

✅ Active Subscriptions (1):
• github_notifications

Commands:
• /subscribe github_notifications - Enable notifications
• /unsubscribe github_notifications - Disable notifications
• /unlink - Unlink account
```

## 🔧 Managing Subscriptions

### Subscribe to Notifications

```
/subscribe github_notifications
```

**Success Response:**
```
✅ Successfully subscribed to github_notifications
```

### Unsubscribe from Notifications

```
/unsubscribe github_notifications
```

**Success Response:**
```
✅ Successfully unsubscribed from github_notifications
```

## 🔗 Unlinking Account

To unlink your Telegram account from TMS:

```
/unlink
```

**Success Response:**
```
👋 Account Unlinked Successfully!

Your Telegram account has been unlinked from:
👤 User: john.doe
📧 Email: <EMAIL>

❌ You will no longer receive notifications.

Use /link <email> <password> to link again.
```

## ❓ Troubleshooting

### Common Issues

**1. "Authentication failed" when linking**
- Check your email and password are correct
- Make sure your account exists in TMS
- OAuth accounts need to be linked through web interface

**2. "Account already linked" error**
- Your TMS account is already linked to another Telegram account
- Use `/unlink` on the other Telegram account first
- Or contact admin to resolve the conflict

**3. "No linked account found" when using commands**
- You need to link your account first using `/link`
- Check if you're using the correct Telegram account

**4. Not receiving notifications**
- Check if you're subscribed: `/status`
- Subscribe to notifications: `/subscribe github_notifications`
- Make sure GitHub webhooks are configured in TMS
- Check if your projects have active webhooks

**5. Bot not responding to commands**
- Check if webhook is properly configured (admin task)
- Verify webhook URL is accessible from internet
- Check server logs for incoming webhook requests
- Ensure bot token is valid and configured

### Getting Help

1. Use `/help` command in the bot
2. Check your status with `/status`
3. Contact your TMS administrator
4. Visit the TMS web dashboard for more options

## 🔧 Admin: Webhook Management

### Using Swagger API

Access the Swagger UI at `/apidocs` and use these endpoints:

- **`GET /telegram/bot/info`** - Get bot information and verify token
- **`POST /telegram/webhook/setup`** - Setup webhook with automatic secret token generation
- **`GET /telegram/webhook/info`** - Check current webhook status and health
- **`POST /telegram/webhook/delete`** - Remove webhook configuration

### Manual Webhook Commands

**Check webhook status:**
```bash
curl "https://api.telegram.org/bot<BOT_TOKEN>/getWebhookInfo"
```

**Setup webhook:**
```bash
curl -X POST "https://api.telegram.org/bot<BOT_TOKEN>/setWebhook" \
     -H "Content-Type: application/json" \
     -d '{
       "url": "https://your-domain.com/telegram/webhook",
       "secret_token": "your_secret_token",
       "allowed_updates": ["message"],
       "drop_pending_updates": true
     }'
```

**Delete webhook:**
```bash
curl -X POST "https://api.telegram.org/bot<BOT_TOKEN>/deleteWebhook" \
     -d '{"drop_pending_updates": true}'
```

### Environment Variables

Make sure these are configured in your `.env` file:

```bash
# Required
TELEGRAM_BOT_TOKEN=*********:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA

# Optional (for webhook security)
TELEGRAM_WEBHOOK_TOKEN=your_generated_secret_token_here
```

## 🔒 Security Notes

- Your password is only used for authentication and not stored
- Use strong passwords for your TMS account
- You can unlink your account anytime using `/unlink`
- Only you can control your notification preferences

## 🎯 Best Practices

1. **Link your account immediately** after starting the bot
2. **Subscribe to relevant notifications** to stay updated
3. **Use `/status` regularly** to check your subscription status
4. **Unlink when not needed** to maintain security
5. **Keep your TMS credentials secure**
