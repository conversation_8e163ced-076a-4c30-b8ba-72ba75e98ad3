# GitHub OAuth Setup Guide

## 🔧 Quick Fix for GitHub Integration Import Issue

### Problem
- GitHub repositories show "Import" buttons but clicking them fails
- Error: "No active GitHub integration found for user"
- Root cause: GitHub OAuth not configured

### Solution Steps

#### 1. Create GitHub OAuth App
1. **Go to GitHub Settings**: https://github.com/settings/developers
2. **Click "New OAuth App"**
3. **Fill in details**:
   - **Application name**: `TMS Local Development`
   - **Homepage URL**: `http://localhost:5001`
   - **Authorization callback URL**: `http://localhost:5001/auth/github/callback`
   - **Description**: `Task Management System - Local Dev`

4. **Click "Register application"**
5. **Copy Client ID and Client Secret**

#### 2. Update Environment Variables
Edit your `.env` file (create from `.env.example` if needed):

```bash
# GitHub OAuth
GITHUB_CLIENT_ID=your_actual_client_id_here
GITHUB_CLIENT_SECRET=your_actual_client_secret_here
GITHUB_REDIRECT_URI=http://localhost:5001/auth/github/callback
```

#### 3. Restart Docker Container
```bash
docker-compose restart flask_app
```

#### 4. Complete OAuth Flow
1. **Go to GitHub Integration**: http://localhost:5001/dashboard/github-integration
2. **Enter your JWT token** (from Auth Manager)
3. **Click "Get OAuth URL"** or **"Start GitHub OAuth"**
4. **Authorize the app** on GitHub
5. **You'll be redirected back** with GitHub integration active

#### 5. Import Repositories
1. **Click "Load Repositories"** - should show your GitHub repos
2. **Click "Sync" button** on any repository to import it as a project
3. **Repository will be imported** with all metadata and issues

### Test Commands

#### Test OAuth URL Generation:
```bash
curl -X GET http://localhost:5001/auth/github/url
```

#### Test Repository Sync (after OAuth):
```bash
curl -X POST http://localhost:5001/projects/github/sync \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"repository_urls": ["https://github.com/octocat/Hello-World"]}'
```

### Expected Workflow

#### Before OAuth:
- ❌ "No active GitHub integration found"
- ❌ Import buttons don't work
- ❌ Repository sync fails

#### After OAuth:
- ✅ GitHub status shows "Connected"
- ✅ Repositories load successfully
- ✅ Import/Sync buttons work
- ✅ Repositories imported as projects with:
  - Project name = Repository name
  - Description = Repository description
  - GitHub URL linked
  - Issues synced as tasks

### Alternative: Use Personal Access Token

If OAuth is too complex for testing, you can use GitHub Personal Access Token:

#### 1. Create PAT
1. **Go to**: https://github.com/settings/tokens
2. **Generate new token (classic)**
3. **Select scopes**: `repo`, `user:email`
4. **Copy the token**

#### 2. Link PAT to User
```bash
curl -X POST http://localhost:5001/auth/github/link \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"access_token": "github_pat_xxx", "token_type": "bearer"}'
```

### Troubleshooting

#### Issue: "client_id=None" in OAuth URL
- **Cause**: Environment variables not loaded
- **Fix**: Check `.env` file and restart container

#### Issue: "Invalid redirect_uri"
- **Cause**: Callback URL mismatch
- **Fix**: Update GitHub app settings to match `.env`

#### Issue: "Repository sync fails"
- **Cause**: No GitHub integration linked to user
- **Fix**: Complete OAuth flow first

#### Issue: "Import button does nothing"
- **Cause**: JavaScript calling wrong endpoint
- **Fix**: Already fixed in latest code update

### Success Indicators

#### GitHub Integration Page:
- ✅ Status shows "Connected to GitHub"
- ✅ Username and repo count displayed
- ✅ Repositories list loads
- ✅ Sync buttons work

#### Projects Manager:
- ✅ Imported repositories appear as projects
- ✅ GitHub URL and metadata preserved
- ✅ Issues synced as tasks

#### Tasks Manager:
- ✅ GitHub repository dropdown populated
- ✅ Can create tasks linked to GitHub issues
- ✅ Sync functionality works both ways

### Quick Test Sequence

1. **Setup OAuth** (5 minutes)
2. **Restart container** (30 seconds)
3. **Complete OAuth flow** (1 minute)
4. **Test import** (30 seconds)
5. **Verify in Projects Manager** (30 seconds)

**Total setup time: ~7 minutes**

Once GitHub OAuth is configured, all repository import functionality will work perfectly!
