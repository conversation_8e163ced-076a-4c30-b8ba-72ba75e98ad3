name: Deploy PROD Server

on:
  push:
    branches: [ "main" ]

jobs:
  deploy:
    runs-on: self-hosted
    steps:
      - name: Pull repos
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.REMOTE_HOST_WEB }}
          username: ${{ secrets.REMOTE_USERNAME_WEB }}
          password: ${{ secrets.REMOTE_PASS_WEB }}
          key: ${{ secrets.SSH_PRIVATE_KEY_WEB }}
          port: ${{ secrets.REMOTE_PORT_WEB }}
          script: |
            ${{ vars.PROD_DEPLOY_COMMAND }}
