"""
Test cases for GitHub webhook functionality
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent.parent))

import unittest
from unittest.mock import patch, MagicMock
import json
from flask import Flask
from app.config import Config
from app.helpers.extensions import db
from app.models.user import User
from app.models.project import Project
from app.models.task import Task
from app.models.integration import Integration
from app.models.webhook import GitHubWebhook, WebhookEvent
from app.webhooks.github_webhook_service import GitHubWebhookService, GitHubWebhookEventProcessor
from app.webhooks.webhook_validator import WebhookValidator


class TestWebhookFunctionality(unittest.TestCase):
    """Test GitHub webhook functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.app = Flask(__name__)
        self.app.config.from_object(Config)
        self.app.config['TESTING'] = True
        self.app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        
        db.init_app(self.app)
        
        with self.app.app_context():
            db.create_all()
            
            # Create test user
            self.test_user = User(
                email="<EMAIL>",
                username="testuser",
                password_hash="hashed_password"
            )
            db.session.add(self.test_user)
            db.session.flush()
            
            # Create GitHub integration
            self.github_integration = Integration(
                user_id=self.test_user.id,
                platform="github",
                access_token="test_token",
                is_active=True
            )
            db.session.add(self.github_integration)
            
            # Create test project
            self.test_project = Project(
                name="Test Project",
                description="Test project description",
                created_by=self.test_user.id,
                user_id=self.test_user.id,
                github_repo_full_name="testuser/test-repo",
                is_github_synced=True
            )
            db.session.add(self.test_project)
            db.session.flush()
            
            # Create test webhook
            self.test_webhook = GitHubWebhook(
                user_id=self.test_user.id,
                project_id=self.test_project.id,
                github_webhook_id="12345678",
                github_repo_full_name="testuser/test-repo",
                webhook_url="https://example.com/webhooks/github",
                secret="test_secret",
                events=["push", "issues", "pull_request"],
                is_active=True
            )
            db.session.add(self.test_webhook)
            db.session.commit()
    
    def tearDown(self):
        """Clean up test environment"""
        with self.app.app_context():
            db.session.remove()
            db.drop_all()
    
    def test_webhook_signature_validation(self):
        """Test webhook signature validation"""
        with self.app.app_context():
            payload = b'{"test": "data"}'
            secret = "test_secret"
            
            # Generate valid signature
            import hmac
            import hashlib
            signature = "sha256=" + hmac.new(
                secret.encode('utf-8'),
                payload,
                hashlib.sha256
            ).hexdigest()
            
            # Test valid signature
            is_valid = WebhookValidator.verify_github_signature(payload, secret, signature)
            self.assertTrue(is_valid)
            
            # Test invalid signature
            is_valid = WebhookValidator.verify_github_signature(payload, secret, "sha256=invalid")
            self.assertFalse(is_valid)
    
    @patch('app.webhooks.github_webhook_service.requests.post')
    def test_create_webhook_for_repository(self, mock_post):
        """Test creating webhook for a repository"""
        with self.app.app_context():
            # Mock GitHub API response
            mock_response = MagicMock()
            mock_response.status_code = 201
            mock_response.json.return_value = {"id": 87654321}
            mock_post.return_value = mock_response

            # Get fresh user ID from database
            user_id = User.query.filter_by(email="<EMAIL>").first().id

            webhook_service = GitHubWebhookService(user_id)
            result = webhook_service.create_webhook_for_repository(
                "testuser/new-repo",
                "https://example.com/webhooks/github"
            )

            self.assertTrue(result.success)
            self.assertIn("webhook_id", result.data)

            # Verify webhook was saved to database
            webhook = GitHubWebhook.query.filter_by(
                github_repo_full_name="testuser/new-repo"
            ).first()
            self.assertIsNotNone(webhook)
            self.assertEqual(webhook.github_webhook_id, "87654321")
    
    def test_process_push_event(self):
        """Test processing push webhook event"""
        with self.app.app_context():
            # Get fresh webhook from database
            webhook = GitHubWebhook.query.filter_by(
                github_repo_full_name="testuser/test-repo"
            ).first()

            payload = {
                "ref": "refs/heads/main",
                "repository": {
                    "full_name": "testuser/test-repo"
                },
                "commits": [
                    {"message": "Test commit"}
                ]
            }

            with patch('app.webhooks.github_webhook_service.GitHubRepositoryService') as mock_service:
                mock_instance = mock_service.return_value
                mock_instance.sync_repositories_to_projects.return_value = MagicMock(success=True)

                result = GitHubWebhookEventProcessor.process_webhook_event(
                    webhook,
                    "push",
                    payload,
                    "test-delivery-id"
                )

                self.assertTrue(result.success)
                mock_instance.sync_repositories_to_projects.assert_called_once()
    
    def test_process_issues_event(self):
        """Test processing issues webhook event"""
        with self.app.app_context():
            # Get fresh webhook and project from database
            webhook = GitHubWebhook.query.filter_by(
                github_repo_full_name="testuser/test-repo"
            ).first()
            project = Project.query.filter_by(
                github_repo_full_name="testuser/test-repo"
            ).first()

            payload = {
                "action": "opened",
                "issue": {
                    "number": 1,
                    "title": "Test issue",
                    "body": "Test issue description"
                },
                "repository": {
                    "full_name": "testuser/test-repo"
                }
            }

            with patch('app.webhooks.github_webhook_service.GitHubIssuesService') as mock_service:
                mock_instance = mock_service.return_value
                mock_instance.sync_repository_issues_to_tasks.return_value = MagicMock(success=True)

                result = GitHubWebhookEventProcessor.process_webhook_event(
                    webhook,
                    "issues",
                    payload,
                    "test-delivery-id"
                )

                self.assertTrue(result.success)
                mock_instance.sync_repository_issues_to_tasks.assert_called_once_with(
                    project.id
                )
    
    def test_webhook_event_logging(self):
        """Test that webhook events are properly logged"""
        with self.app.app_context():
            # Get fresh webhook from database
            webhook = GitHubWebhook.query.filter_by(
                github_repo_full_name="testuser/test-repo"
            ).first()

            payload = {"test": "data"}

            result = GitHubWebhookEventProcessor.process_webhook_event(
                webhook,
                "ping",
                payload,
                "test-delivery-id"
            )

            # Check that event was logged
            event = WebhookEvent.query.filter_by(
                github_delivery_id="test-delivery-id"
            ).first()

            self.assertIsNotNone(event)
            self.assertEqual(event.webhook_id, webhook.id)
            self.assertEqual(event.event_type, "ping")
            self.assertEqual(event.payload, payload)
    
    @patch('app.webhooks.github_webhook_service.requests.delete')
    def test_delete_webhook(self, mock_delete):
        """Test deleting a webhook"""
        with self.app.app_context():
            # Mock GitHub API response
            mock_response = MagicMock()
            mock_response.status_code = 204
            mock_delete.return_value = mock_response

            # Get fresh user ID and webhook ID from database
            user_id = User.query.filter_by(email="<EMAIL>").first().id
            webhook = GitHubWebhook.query.filter_by(
                github_repo_full_name="testuser/test-repo"
            ).first()

            webhook_service = GitHubWebhookService(user_id)
            result = webhook_service.delete_webhook(webhook.id)

            self.assertTrue(result.success)

            # Verify webhook was deactivated
            updated_webhook = GitHubWebhook.query.get(webhook.id)
            self.assertFalse(updated_webhook.is_active)
    
    def test_get_user_webhooks(self):
        """Test getting user's webhooks"""
        with self.app.app_context():
            # Get fresh user ID from database
            user_id = User.query.filter_by(email="<EMAIL>").first().id
            webhook = GitHubWebhook.query.filter_by(
                github_repo_full_name="testuser/test-repo"
            ).first()

            webhook_service = GitHubWebhookService(user_id)
            result = webhook_service.get_user_webhooks()

            self.assertTrue(result.success)
            self.assertEqual(len(result.data["webhooks"]), 1)
            self.assertEqual(result.data["webhooks"][0]["id"], webhook.id)


if __name__ == '__main__':
    unittest.main()
