import sys, os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import os
import io
import pytest
from flask_jwt_extended import create_access_token
from app.helpers.extensions import db
from app.models import User, Task, Project

@pytest.fixture
def client():
    from app import create_app
    app = create_app({
        "TESTING": True,
        "SQLALCHEMY_DATABASE_URI": "sqlite:///:memory:",
        "JWT_SECRET_KEY": "test-secret"
    })
    with app.test_client() as client:
        with app.app_context():
            db.drop_all()
            db.create_all()
            user = User(id=1, username="test", email="<EMAIL>", password_hash="hashed")
            project = Project(id=1, name="Demo", description="desc", created_by=1)
            task = Task(id=1, title="test task", assignee_id=1, project_id=1)
            db.session.add_all([user, project, task])
            db.session.commit()
        yield client


@pytest.fixture
def auth_headers(client):
    with client.application.app_context():
        token = create_access_token(identity="1")
    return {"Authorization": f"Bearer {token}"}

def test_upload_attachment(client, auth_headers):
    dummy_file = (io.BytesIO(b"dummy content"), "test.txt")

    res = client.post("/attachments/upload", data={
        "task_id": "1",
        "file": dummy_file
    }, headers=auth_headers, content_type='multipart/form-data')

    assert res.status_code == 200
    assert res.json["message"] == "Attachment uploaded"
    assert "id" in res.json["data"]

def test_get_attachments(client, auth_headers):
    # First upload a file
    dummy_file = (io.BytesIO(b"dummy content"), "getfile.txt")
    client.post("/attachments/upload", data={
        "task_id": "1",
        "file": dummy_file
    }, headers=auth_headers, content_type='multipart/form-data')

    res = client.get("/attachments/task/1", headers=auth_headers)
    assert res.status_code == 200
    assert isinstance(res.json["data"], list)
    assert len(res.json["data"]) >= 1
    assert res.json["data"][0]["file_name"] == "getfile.txt"

def test_delete_attachment(client, auth_headers):
    # Upload a file to delete
    dummy_file = (io.BytesIO(b"delete me"), "delete.txt")
    upload_res = client.post("/attachments/upload", data={
        "task_id": "1",
        "file": dummy_file
    }, headers=auth_headers, content_type='multipart/form-data')

    attachment_id = upload_res.json["data"]["id"]

    # Delete it
    delete_res = client.delete(f"/attachments/{attachment_id}", headers=auth_headers)
    assert delete_res.status_code == 200
    assert delete_res.json["message"] == "Attachment deleted"

    # Check again to ensure it's filtered
    res = client.get("/attachments/task/1", headers=auth_headers)
    ids = [a["id"] for a in res.json["data"]]
    assert attachment_id not in ids
