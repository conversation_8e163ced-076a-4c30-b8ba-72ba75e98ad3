"""
Test cases for Telegram integration functionality
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent.parent))

import unittest
from unittest.mock import patch, MagicMock
import json
from flask import Flask
from app.config import Config
from app.helpers.extensions import db
from app.models.user import User
from app.models.telegram_integration import TelegramIntegration, TelegramSubscription, TelegramNotificationLog
from app.models.webhook import GitH<PERSON>Webhook, WebhookEvent
from app.telegram.bot_service import TelegramBotService
from app.telegram.notification_service import TelegramNotificationService


class TestTelegramIntegration(unittest.TestCase):
    """Test Telegram integration functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.app = Flask(__name__)
        self.app.config.from_object(Config)
        self.app.config['TESTING'] = True
        self.app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        self.app.config['TELEGRAM_BOT_TOKEN'] = 'test_bot_token'
        
        db.init_app(self.app)
        
        with self.app.app_context():
            db.create_all()
            
            # Create test user
            self.test_user = User(
                email="<EMAIL>",
                username="testuser",
                password_hash="hashed_password"
            )
            db.session.add(self.test_user)
            db.session.commit()
            
            self.user_id = self.test_user.id
    
    def tearDown(self):
        """Clean up after tests"""
        with self.app.app_context():
            db.session.remove()
            db.drop_all()
    
    def test_telegram_integration_creation(self):
        """Test creating a Telegram integration"""
        with self.app.app_context():
            integration = TelegramIntegration(
                user_id=self.user_id,
                telegram_user_id="123456789",
                chat_id="123456789",
                telegram_username="testuser",
                telegram_first_name="Test",
                telegram_last_name="User"
            )
            db.session.add(integration)
            db.session.commit()
            
            # Verify integration was created
            saved_integration = TelegramIntegration.query.filter_by(user_id=self.user_id).first()
            self.assertIsNotNone(saved_integration)
            self.assertEqual(saved_integration.telegram_user_id, "123456789")
            self.assertEqual(saved_integration.chat_id, "123456789")
            self.assertTrue(saved_integration.is_active)
    
    def test_telegram_subscription_creation(self):
        """Test creating a Telegram subscription"""
        with self.app.app_context():
            # Create integration first
            integration = TelegramIntegration(
                user_id=self.user_id,
                telegram_user_id="123456789",
                chat_id="123456789"
            )
            db.session.add(integration)
            db.session.flush()
            
            # Create subscription
            subscription = TelegramSubscription(
                telegram_integration_id=integration.id,
                notification_type="github_notifications",
                is_enabled=True
            )
            db.session.add(subscription)
            db.session.commit()
            
            # Verify subscription was created
            saved_subscription = TelegramSubscription.query.filter_by(
                telegram_integration_id=integration.id
            ).first()
            self.assertIsNotNone(saved_subscription)
            self.assertEqual(saved_subscription.notification_type, "github_notifications")
            self.assertTrue(saved_subscription.is_enabled)
    
    @patch('app.telegram.bot_service.requests.post')
    def test_bot_service_send_message(self, mock_post):
        """Test Telegram bot service send message"""
        with self.app.app_context():
            # Mock successful response
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "ok": True,
                "result": {"message_id": 123}
            }
            mock_post.return_value = mock_response
            
            bot_service = TelegramBotService()
            success, response_data = bot_service.send_message("123456789", "Test message")
            
            self.assertTrue(success)
            self.assertIsNotNone(response_data)
            self.assertEqual(response_data["result"]["message_id"], 123)
    
    @patch('app.telegram.bot_service.requests.post')
    def test_bot_service_process_start_command(self, mock_post):
        """Test processing /start command"""
        with self.app.app_context():
            # Mock successful response
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"ok": True}
            mock_post.return_value = mock_response
            
            bot_service = TelegramBotService()
            
            update_data = {
                "message": {
                    "chat": {"id": 123456789},
                    "from": {
                        "id": 123456789,
                        "first_name": "Test",
                        "username": "testuser"
                    },
                    "text": "/start"
                }
            }
            
            result = bot_service.process_webhook_update(update_data)
            self.assertTrue(result.success)
    
    def test_notification_service_format_push_event(self):
        """Test formatting push event notification"""
        with self.app.app_context():
            # Create webhook and event
            webhook = GitHubWebhook(
                user_id=self.user_id,
                github_webhook_id="webhook123",
                github_repo_full_name="user/repo",
                webhook_url="http://example.com/webhook",
                secret="secret",
                events=["push"]
            )
            db.session.add(webhook)
            db.session.flush()
            
            payload = {
                "repository": {
                    "full_name": "user/repo",
                    "html_url": "https://github.com/user/repo"
                },
                "pusher": {"name": "testuser"},
                "ref": "refs/heads/main",
                "commits": [
                    {
                        "message": "Test commit",
                        "url": "https://github.com/user/repo/commit/abc123"
                    }
                ]
            }
            
            webhook_event = WebhookEvent(
                webhook_id=webhook.id,
                github_delivery_id="delivery123",
                event_type="push",
                payload=payload
            )
            db.session.add(webhook_event)
            db.session.commit()
            
            notification_service = TelegramNotificationService()
            message = notification_service._format_github_notification(webhook_event)
            
            self.assertIsNotNone(message)
            self.assertIn("🚀", message)  # Push emoji
            self.assertIn("user/repo", message)
            self.assertIn("main", message)  # Branch name
            self.assertIn("testuser", message)  # Pusher name
    
    @patch('app.telegram.bot_service.TelegramBotService.send_message')
    def test_notification_service_send_notification(self, mock_send_message):
        """Test sending GitHub webhook notification"""
        with self.app.app_context():
            # Mock successful message sending
            mock_send_message.return_value = (True, {"result": {"message_id": 123}})
            
            # Create integration and subscription
            integration = TelegramIntegration(
                user_id=self.user_id,
                telegram_user_id="123456789",
                chat_id="123456789"
            )
            db.session.add(integration)
            db.session.flush()
            
            subscription = TelegramSubscription(
                telegram_integration_id=integration.id,
                notification_type="github_notifications",
                is_enabled=True
            )
            db.session.add(subscription)
            db.session.flush()
            
            # Create webhook and event
            webhook = GitHubWebhook(
                user_id=self.user_id,
                github_webhook_id="webhook123",
                github_repo_full_name="user/repo",
                webhook_url="http://example.com/webhook",
                secret="secret",
                events=["push"]
            )
            db.session.add(webhook)
            db.session.flush()
            
            payload = {
                "repository": {
                    "full_name": "user/repo",
                    "html_url": "https://github.com/user/repo"
                },
                "pusher": {"name": "testuser"},
                "ref": "refs/heads/main",
                "commits": []
            }
            
            webhook_event = WebhookEvent(
                webhook_id=webhook.id,
                github_delivery_id="delivery123",
                event_type="push",
                payload=payload
            )
            db.session.add(webhook_event)
            db.session.commit()
            
            notification_service = TelegramNotificationService()
            result = notification_service.send_github_webhook_notification(webhook_event)
            
            self.assertTrue(result)
            mock_send_message.assert_called_once()
            
            # Check notification log was created
            log = TelegramNotificationLog.query.filter_by(
                webhook_event_id=webhook_event.id
            ).first()
            self.assertIsNotNone(log)
            self.assertEqual(log.status, 'sent')

    @patch('app.telegram.bot_service.TelegramBotService.send_message')
    def test_link_command_success(self, mock_send_message):
        """Test successful /link command"""
        with self.app.app_context():
            # Mock successful message sending
            mock_send_message.return_value = (True, {"result": {"message_id": 123}})

            # Create test user with password
            from werkzeug.security import generate_password_hash
            test_user = User(
                email="<EMAIL>",
                username="linkuser",
                password_hash=generate_password_hash("testpassword")
            )
            db.session.add(test_user)
            db.session.commit()

            bot_service = TelegramBotService()

            update_data = {
                "message": {
                    "chat": {"id": 987654321},
                    "from": {
                        "id": 987654321,
                        "first_name": "Link",
                        "username": "linkuser"
                    },
                    "text": "/link <EMAIL> testpassword"
                }
            }

            result = bot_service.process_webhook_update(update_data)
            self.assertTrue(result.success)

            # Check integration was created
            integration = TelegramIntegration.query.filter_by(
                telegram_user_id="987654321"
            ).first()
            self.assertIsNotNone(integration)
            self.assertEqual(integration.user_id, test_user.id)

    @patch('app.telegram.bot_service.TelegramBotService.send_message')
    def test_link_command_invalid_credentials(self, mock_send_message):
        """Test /link command with invalid credentials"""
        with self.app.app_context():
            mock_send_message.return_value = (True, {"result": {"message_id": 123}})

            bot_service = TelegramBotService()

            update_data = {
                "message": {
                    "chat": {"id": 987654321},
                    "from": {
                        "id": 987654321,
                        "first_name": "Link",
                        "username": "linkuser"
                    },
                    "text": "/link <EMAIL> wrongpassword"
                }
            }

            result = bot_service.process_webhook_update(update_data)
            self.assertTrue(result.success)  # Command processed, but auth failed

            # Check no integration was created
            integration = TelegramIntegration.query.filter_by(
                telegram_user_id="987654321"
            ).first()
            self.assertIsNone(integration)

    @patch('app.telegram.bot_service.TelegramBotService.send_message')
    def test_unlink_command(self, mock_send_message):
        """Test /unlink command"""
        with self.app.app_context():
            mock_send_message.return_value = (True, {"result": {"message_id": 123}})

            # Create integration first
            integration = TelegramIntegration(
                user_id=self.user_id,
                telegram_user_id="987654321",
                chat_id="987654321"
            )
            db.session.add(integration)
            db.session.commit()

            bot_service = TelegramBotService()

            update_data = {
                "message": {
                    "chat": {"id": 987654321},
                    "from": {
                        "id": 987654321,
                        "first_name": "Unlink",
                        "username": "unlinkuser"
                    },
                    "text": "/unlink"
                }
            }

            result = bot_service.process_webhook_update(update_data)
            self.assertTrue(result.success)

            # Check integration was deleted
            integration = TelegramIntegration.query.filter_by(
                telegram_user_id="987654321"
            ).first()
            self.assertIsNone(integration)


if __name__ == '__main__':
    unittest.main()
