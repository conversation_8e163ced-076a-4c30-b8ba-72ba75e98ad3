"""
Test cases for GitHub webhook API endpoints
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent.parent))

import unittest
import json
from unittest.mock import patch, MagicMock
from flask import Flask
from app import create_app
from app.helpers.extensions import db
from app.models.user import User
from app.models.project import Project
from app.models.integration import Integration
from app.models.webhook import GitHubWebhook
from flask_jwt_extended import create_access_token


class TestWebhookAPI(unittest.TestCase):
    """Test GitHub webhook API endpoints"""
    
    def setUp(self):
        """Set up test environment"""
        self.app = create_app({
            'TESTING': True,
            'SQLALCHEMY_DATABASE_URI': 'sqlite:///:memory:',
            'JWT_SECRET_KEY': 'test-secret-key'
        })
        
        self.client = self.app.test_client()
        
        with self.app.app_context():
            db.create_all()
            
            # Create test user
            self.test_user = User(
                email="<EMAIL>",
                username="testuser",
                password_hash="hashed_password"
            )
            db.session.add(self.test_user)
            db.session.flush()
            
            # Create GitHub integration
            self.github_integration = Integration(
                user_id=self.test_user.id,
                platform="github",
                access_token="test_token",
                is_active=True
            )
            db.session.add(self.github_integration)
            
            # Create test project
            self.test_project = Project(
                name="Test Project",
                description="Test project description",
                created_by=self.test_user.id,
                user_id=self.test_user.id,
                github_repo_full_name="testuser/test-repo",
                is_github_synced=True
            )
            db.session.add(self.test_project)
            db.session.flush()
            
            # Create test webhook
            self.test_webhook = GitHubWebhook(
                user_id=self.test_user.id,
                project_id=self.test_project.id,
                github_webhook_id="12345678",
                github_repo_full_name="testuser/test-repo",
                webhook_url="https://example.com/webhooks/github",
                secret="test_secret",
                events=["push", "issues", "pull_request"],
                is_active=True
            )
            db.session.add(self.test_webhook)
            db.session.commit()
            
            # Create JWT token
            self.access_token = create_access_token(identity=str(self.test_user.id))
    
    def tearDown(self):
        """Clean up test environment"""
        with self.app.app_context():
            db.session.remove()
            db.drop_all()
    
    def test_list_webhooks_endpoint(self):
        """Test GET /webhooks/list endpoint"""
        with self.app.app_context():
            response = self.client.get(
                '/webhooks/list',
                headers={'Authorization': f'Bearer {self.access_token}'}
            )
            
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.data)
            self.assertTrue(data['success'])
            self.assertEqual(len(data['data']['webhooks']), 1)
            self.assertEqual(data['data']['webhooks'][0]['github_repo_full_name'], 'testuser/test-repo')
    
    def test_list_webhooks_unauthorized(self):
        """Test GET /webhooks/list without authorization"""
        response = self.client.get('/webhooks/list')
        self.assertEqual(response.status_code, 401)
    
    @patch('app.webhooks.github_webhook_service.requests.post')
    def test_create_webhooks_endpoint(self, mock_post):
        """Test POST /webhooks/create endpoint"""
        with self.app.app_context():
            # Mock GitHub API response
            mock_response = MagicMock()
            mock_response.status_code = 201
            mock_response.json.return_value = {"id": 87654321}
            mock_post.return_value = mock_response
            
            payload = {
                "repository_urls": [
                    "https://github.com/testuser/new-repo"
                ]
            }
            
            response = self.client.post(
                '/webhooks/create',
                data=json.dumps(payload),
                content_type='application/json',
                headers={'Authorization': f'Bearer {self.access_token}'}
            )
            
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.data)
            self.assertTrue(data['success'])
            self.assertEqual(len(data['data']['created_webhooks']), 1)
    
    def test_create_webhooks_bad_request(self):
        """Test POST /webhooks/create with bad request"""
        with self.app.app_context():
            payload = {
                "invalid_field": "value"
            }
            
            response = self.client.post(
                '/webhooks/create',
                data=json.dumps(payload),
                content_type='application/json',
                headers={'Authorization': f'Bearer {self.access_token}'}
            )
            
            self.assertEqual(response.status_code, 400)
            data = json.loads(response.data)
            self.assertFalse(data['success'])
    
    @patch('app.webhooks.github_webhook_service.requests.delete')
    def test_delete_webhook_endpoint(self, mock_delete):
        """Test DELETE /webhooks/{webhook_id} endpoint"""
        with self.app.app_context():
            # Mock GitHub API response
            mock_response = MagicMock()
            mock_response.status_code = 204
            mock_delete.return_value = mock_response
            
            webhook = GitHubWebhook.query.filter_by(
                github_repo_full_name="testuser/test-repo"
            ).first()
            
            response = self.client.delete(
                f'/webhooks/{webhook.id}',
                headers={'Authorization': f'Bearer {self.access_token}'}
            )
            
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.data)
            self.assertTrue(data['success'])
    
    def test_delete_webhook_not_found(self):
        """Test DELETE /webhooks/{webhook_id} with non-existent webhook"""
        with self.app.app_context():
            response = self.client.delete(
                '/webhooks/99999',
                headers={'Authorization': f'Bearer {self.access_token}'}
            )
            
            self.assertEqual(response.status_code, 404)
            data = json.loads(response.data)
            self.assertFalse(data['success'])
    
    def test_get_webhook_events_endpoint(self):
        """Test GET /webhooks/events/{webhook_id} endpoint"""
        with self.app.app_context():
            webhook = GitHubWebhook.query.filter_by(
                github_repo_full_name="testuser/test-repo"
            ).first()
            
            response = self.client.get(
                f'/webhooks/events/{webhook.id}',
                headers={'Authorization': f'Bearer {self.access_token}'}
            )
            
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.data)
            self.assertTrue(data['success'])
            self.assertIn('events', data['data'])
            self.assertIn('pagination', data['data'])
    
    def test_github_webhook_endpoint_invalid_signature(self):
        """Test POST /webhooks/github with invalid signature"""
        payload = {
            "repository": {
                "full_name": "testuser/test-repo"
            }
        }
        
        response = self.client.post(
            '/webhooks/github',
            data=json.dumps(payload),
            content_type='application/json',
            headers={
                'X-GitHub-Event': 'push',
                'X-GitHub-Delivery': 'test-delivery-id',
                'X-Hub-Signature-256': 'sha256=invalid'
            }
        )
        
        self.assertEqual(response.status_code, 401)
        data = json.loads(response.data)
        self.assertEqual(data['error'], 'Invalid webhook signature')
    
    def test_github_webhook_endpoint_missing_headers(self):
        """Test POST /webhooks/github with missing headers"""
        payload = {
            "repository": {
                "full_name": "testuser/test-repo"
            }
        }
        
        response = self.client.post(
            '/webhooks/github',
            data=json.dumps(payload),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('Missing', data['error'])


if __name__ == '__main__':
    unittest.main()
